"""
Table Cell Manager
=================

Cell content management for table drawing in the Drawing-Production application.
This module handles all cell content operations including triangle cells, data cells,
and zone detail cells.

Key Features:
- Triangle cell content management (column mark, floor mark)
- Data cell content management (column mark data, floor value)
- Zone detail cell management with section drawer integration
- Zone text label fallback functionality
- Professional cell content rendering with AIA layer standards

This module was extracted from TableDrawer to improve maintainability and
reduce the main class size while preserving all functionality.
"""

import logging
from typing import Optional
from ezdxf.enums import TextEntityAlignment
from ezdxf import const
from ...models.column_config import ColumnConfig
from ...models.zone_config import ZoneConfigSet
from ...models.table_config import TableConfig
from ...io.dxf_writer import DXFWriter
from .layer_manager_mixin import LayerManagerMixin
from .validation_utils import TableValidationUtils, TableValidationError

logger = logging.getLogger(__name__)


class TableCellManager(LayerManagerMixin):
    """
    Manages all cell content operations for table drawing.

    This class handles content placement in various table cells including
    triangle cells, data cells, and zone detail cells with proper layer
    management and positioning.
    """

    def __init__(self, table_config: TableConfig, dxf_writer: Optional[DXFWriter] = None,
                 modelspace=None, section_drawer=None, config=None):
        """
        Initialize the table cell manager.

        Args:
            table_config: Table configuration instance
            dxf_writer: DXF writer with layer management (optional)
            modelspace: DXF modelspace for drawing operations (optional)
            section_drawer: SectionDrawer instance for zone detail drawing (optional)
            config: Drawing configuration object (optional)
        """
        # Validate required inputs
        TableValidationUtils.validate_table_config(
            table_config, "TableCellManager initialization")
        TableValidationUtils.validate_optional_config(
            dxf_writer, "DXFWriter", "TableCellManager initialization")

        super().__init__(table_config, dxf_writer)
        self.msp = modelspace
        self.section_drawer = section_drawer
        self.config = config

    def add_column_mark_triangle_cell(self, title_column_x: float, header_row_y: float, zone_d_row_y: float) -> None:
        """
        Add 'COLUMN' and 'MARK' text in 2 rows at the geometric center of the upper triangular cell.

        Args:
            title_column_x: X coordinate of the title column
            header_row_y: Y coordinate of the header row
            zone_d_row_y: Y coordinate of zone D row (unused but kept for consistency)
        """
        try:
            # Validate inputs
            TableValidationUtils.validate_coordinates(
                title_column_x, header_row_y, "column mark triangle cell position")
            TableValidationUtils.validate_modelspace(
                self.msp, "modelspace for triangle cell drawing")

            return TableValidationUtils.safe_execute(
                "add column mark triangle cell",
                self._add_column_mark_triangle_content,
                title_column_x, header_row_y, zone_d_row_y
            )
        except Exception as e:
            logger.error(f"Error adding column mark triangle cell: {e}")
            raise

    def _add_column_mark_triangle_content(self, title_column_x: float, header_row_y: float, zone_d_row_y: float) -> None:
        """Internal method to add column mark triangle content."""
        # Get layers for text elements
        text_layer = self.get_text_layer()

        try:
            # Get text position using table configuration
            text_x, text_y = self.table_config.get_upper_triangle_text_position(
                title_column_x, header_row_y)

            # Add "COLUMN" text in upper row, right-aligned
            gap_half = self.table_config.get_title_column_mark_gap() / 2
            self.msp.add_text(
                "COLUMN",
                height=self.table_config.get_title_column_mark_text_height(),
                dxfattribs={'layer': text_layer, 'color': const.BYLAYER, 'lineweight': const.LINEWEIGHT_BYLAYER,
                            'linetype': 'ByLayer', 'style': self.table_config.get_engineering_style()}
            ).set_placement(
                (text_x, text_y + gap_half),  # Position above center with gap
                align=TextEntityAlignment.MIDDLE_RIGHT
            )

            # Add "MARK" text in lower row, right-aligned
            self.msp.add_text(
                "MARK",
                height=self.table_config.get_title_column_mark_text_height(),
                dxfattribs={'layer': text_layer, 'color': const.BYLAYER, 'lineweight': const.LINEWEIGHT_BYLAYER,
                            'linetype': 'ByLayer', 'style': self.table_config.get_engineering_style()}
            ).set_placement(
                (text_x, text_y - gap_half),  # Position below center with gap
                align=TextEntityAlignment.MIDDLE_RIGHT
            )

            logger.debug(
                f"Added 'COLUMN' and 'MARK' text at geometric center ({text_x:.1f}, {text_y:.1f})")

        except Exception as e:
            logger.error(
                f"Error positioning COLUMN MARK text in triangular cell: {e}")
            raise

    def add_floor_mark_triangle_cell(self, title_column_x: float, header_row_y: float, zone_d_row_y: float) -> None:
        """
        Add 'FLOOR' text at the geometric center of the lower triangular cell.

        Args:
            title_column_x: X coordinate of the title column
            header_row_y: Y coordinate of the header row
            zone_d_row_y: Y coordinate of zone D row (unused but kept for consistency)
        """
        # Get layers for text elements
        text_layer = self.get_text_layer()

        try:
            # Get text position using table configuration
            text_x, text_y = self.table_config.get_lower_triangle_text_position(
                title_column_x, header_row_y)

            # Add "FLOOR" text at calculated position
            self.msp.add_text(
                "FLOOR",
                height=self.table_config.get_title_floor_mark_text_height(),
                dxfattribs={'layer': text_layer, 'color': const.BYLAYER, 'lineweight': const.LINEWEIGHT_BYLAYER,
                            'linetype': 'ByLayer', 'style': self.table_config.get_engineering_style()}
            ).set_placement(
                (text_x, text_y),
                align=TextEntityAlignment.MIDDLE_CENTER
            )

            logger.debug(
                f"Added 'FLOOR' text at geometric center ({text_x:.1f}, {text_y:.1f})")

        except Exception as e:
            logger.error(
                f"Error positioning FLOOR text in triangular cell: {e}")
            raise

    def add_column_mark_data_cell(self, detail_column_x: float, header_row_y: float, zone_d_row_y: float, column_config: ColumnConfig) -> None:
        """Add column mark data in the COLUMN MARK VALUE CELL using new coordinates."""
        # Get layers for text elements
        header_layer = self.get_header_layer()

        try:
            # Get cell center position using table configuration
            cell_center_x, cell_center_y = self.table_config.get_column_mark_cell_center(
                detail_column_x, header_row_y)

            # Add column mark data in the cell center
            self.msp.add_text(
                column_config.name,
                height=self.table_config.get_text_height_title(),
                dxfattribs={'layer': header_layer, 'color': const.BYLAYER, 'lineweight': const.LINEWEIGHT_BYLAYER,
                            'linetype': 'ByLayer', 'style': self.table_config.get_title_style()}
            ).set_placement(
                (cell_center_x, cell_center_y),
                align=TextEntityAlignment.MIDDLE_CENTER
            )

            logger.debug(
                f"Added column mark '{column_config.name}' at ({cell_center_x:.1f}, {cell_center_y:.1f})")

        except Exception as e:
            logger.error(f"Error positioning column mark data: {e}")
            raise

    def add_floor_value_cell(self, title_column_x: float, header_row_y: float, zone_d_row_y: float, column_config: ColumnConfig) -> None:
        """Add floor value in the FLOOR MARK VALUE CELL using new coordinates."""
        # Get layers for text elements
        header_layer = self.get_header_layer()

        try:
            # Get cell center position using table configuration
            cell_center_x, cell_center_y = self.table_config.get_floor_value_cell_center(
                title_column_x, header_row_y)

            # Add floor data in center of floor value cell with configured rotation
            rotation = self.table_config.get_floor_text_rotation()
            self.msp.add_text(
                column_config.floor,
                height=self.table_config.get_value_floor_text_height(),
                dxfattribs={'layer': header_layer, 'color': const.BYLAYER, 'lineweight': const.LINEWEIGHT_BYLAYER,
                            'linetype': 'ByLayer', 'style': self.table_config.get_title_style(), 'rotation': rotation}
            ).set_placement(
                (cell_center_x, cell_center_y),
                align=TextEntityAlignment.MIDDLE_CENTER
            )

            logger.debug(
                f"Added floor value '{column_config.floor}' at ({cell_center_x:.1f}, {cell_center_y:.1f})")

        except Exception as e:
            logger.error(f"Error positioning floor value: {e}")
            raise

    def add_zone_details(self, detail_column_x: float, elevation_column_x: float, row_positions: list, column_config: ColumnConfig, zone_config_set: Optional[ZoneConfigSet] = None) -> None:
        """Add zone D, C, B, A details in the four ZONE_*_DETAIL cells (top to bottom sequence)."""
        try:
            # Visual layout order: D (top) → B (moved to C position) → A (moved to B position)
            # Zone C has been merged into Zone A and is no longer drawn separately
            # This is independent of link mark numbering which processes A→B→D
            visual_zone_order = ["D", "B", "A"]

            if self.section_drawer and zone_config_set:
                # Draw detailed zone drawings
                logger.debug("Drawing zone details with SectionDrawer")

                for i, (zone_id, row_y) in enumerate(zip(visual_zone_order, row_positions)):
                    try:
                        # Get zone configuration
                        zone_config = zone_config_set.get_zone(zone_id)

                        # Calculate ZONE_*_DETAIL cell bounds using precise table coordinates
                        # These coordinates position zone drawings within their designated cells
                        logger.debug(
                            f"Zone {zone_id} ZONE_{zone_id}_DETAIL cell coordinates: detail_column_x={detail_column_x:.1f}, row_y={row_y:.1f}")
                        zone_bounds = self.config.get_zone_cell_bounds(
                            zone_id, detail_column_x, row_y)
                        logger.debug(
                            f"Zone {zone_id} calculated bounds: {zone_bounds}")

                        # Import GeometryCalculator here to avoid circular imports
                        from ...calculators.geometry_calculator import GeometryCalculator
                        geometry_calculator = GeometryCalculator(self.config)

                        # Draw zone detail
                        detail_bounds, rebar_data = self.section_drawer.draw_zone_detail(
                            column_config, zone_config, zone_bounds, geometry_calculator
                        )

                        logger.debug(
                            f"Drew zone {zone_id} detail at scale {detail_bounds['scale']:.2f}")

                    except Exception as e:
                        logger.error(
                            f"Error drawing zone {zone_id} detail: {e}")
                        # Fall back to text label in ZONE_*_DETAIL cell
                        self.add_zone_text_label(
                            zone_id, detail_column_x, row_y, column_config)
            else:
                # Fall back to text labels only in ZONE_*_DETAIL cells
                logger.debug(
                    "Drawing zone text labels (no SectionDrawer or ZoneConfigSet)")
                for i, (zone_id, row_y) in enumerate(zip(visual_zone_order, row_positions)):
                    self.add_zone_text_label(
                        zone_id, detail_column_x, row_y, column_config)

        except Exception as e:
            logger.error(f"Error adding zone details: {e}")
            raise

    def add_zone_text_label(self, zone_id: str, detail_column_x: float, row_y: float, column_config: ColumnConfig) -> None:
        """Add a simple text label for a zone in its ZONE_*_DETAIL cell (fallback method)."""
        try:
            # Get layers for text elements
            text_layer = self.get_text_layer()

            # Import the zone display name utility function
            from ...models.zone_config import get_zone_display_name

            # Determine the start floor type from column configuration
            # Check both floor and start_floor_name fields
            start_floor_type = ""
            if hasattr(column_config, 'start_floor_name') and column_config.start_floor_name:
                start_floor_type = column_config.start_floor_name
            elif hasattr(column_config, 'floor') and column_config.floor:
                start_floor_type = column_config.floor

            # Create zone label text with conditional naming based on start floor type
            zone_text = get_zone_display_name(zone_id, start_floor_type)

            # Add zone label in the center of the ZONE_*_DETAIL cell
            self.msp.add_text(
                zone_text,
                height=self.table_config.get_table_data_text_height(),
                dxfattribs={'layer': text_layer, 'color': const.BYLAYER, 'lineweight': const.LINEWEIGHT_BYLAYER,
                            'linetype': 'ByLayer', 'style': self.table_config.get_engineering_style()}
            ).set_placement(
                (detail_column_x + self.table_config.get_detail_column_center_offset(),
                 row_y - self.table_config.get_table_data_y_offset()),
                align=TextEntityAlignment.MIDDLE_CENTER
            )

        except Exception as e:
            logger.error(f"Error adding zone {zone_id} text label: {e}")
            raise
