"""
Application Core Module
========================

Contains the core business logic and component initialization for the
column drawing application. This module provides the foundation that
can be used by both CLI and GUI interfaces.
"""

import logging
from typing import Optional, Dict, Any

from ..models.drawing_config import DrawingConfig
from ..models.layer_config import StructuralLayerConfig
from ..models.table_cell_config import ASDTableCellConfig, CellType
from ..models.rebar_layer_data import RebarLayerData
from ..io.csv_reader import CSVReader
from ..io.dxf_writer import DXFWriter
from ..calculators.rebar_calculator import RebarCalculator
from ..calculators.geometry_calculator import Geo<PERSON><PERSON>alculator
from ..drawers.table_drawer import TableDrawer
from ..drawers.section_drawer import SectionDrawer
from ..processors.data_processor import DataProcessor
from ..processors.column_sorter import ColumnSorter
from ..orchestrators.drawing_orchestrator import DrawingOrchestrator

logger = logging.getLogger(__name__)


class ApplicationCore:
    """
    Core application logic and component management.

    This class provides the foundation for the column drawing application,
    handling component initialization, configuration management, and
    providing access to core functionality for both CLI and GUI interfaces.
    """

    def __init__(self, config: Optional[DrawingConfig] = None, layer_config: Optional[StructuralLayerConfig] = None):
        """
        Initialize the application core.

        Args:
            config: Drawing configuration object (uses default if None)
            layer_config: Layer configuration object (uses default if None)
        """
        self.config = config or DrawingConfig()
        self.layer_config = layer_config or StructuralLayerConfig()

        # Initialize table cell configuration for standardized cell access
        self.table_cell_config = ASDTableCellConfig()

        # Initialize all components
        self._initialize_components()

        logger.info("Application Core initialized")

    def _initialize_components(self):
        """Initialize all application components."""
        # Initialize basic components
        self.csv_reader = CSVReader()
        self.dxf_writer = DXFWriter(
            config=self.config, layer_config=self.layer_config)
        self.rebar_calculator = RebarCalculator(config=self.config)
        self.geometry_calculator = GeometryCalculator(config=self.config)

        # Initialize data processing components
        self.data_processor = DataProcessor(
            self.csv_reader, self.rebar_calculator)
        self.column_sorter = ColumnSorter()

        # Initialize drawing components with layer management
        self.rebar_drawer = SectionDrawer(
            self.dxf_writer.get_document(),
            self.dxf_writer.get_modelspace(),
            self.config,
            self.rebar_calculator,
            self.dxf_writer
        )
        self.table_drawer = TableDrawer(
            self.dxf_writer.get_modelspace(),
            self.config,
            self.dxf_writer,
            self.rebar_drawer,  # Pass SectionDrawer for zone detail drawing
            self.dxf_writer.get_document()  # Pass document for dimension drawing
        )

        # Initialize drawing orchestrator with DXF writer for drawing space support
        self.drawing_orchestrator = DrawingOrchestrator(
            self.config,
            self.table_drawer,
            self.rebar_drawer,
            self.rebar_calculator,
            self.geometry_calculator,
            self.data_processor,
            self.column_sorter,
            self.dxf_writer  # Pass DXF writer for drawing space integration
        )
        
        # Set up drawing space generator
        self.drawing_orchestrator.set_dxf_writer(self.dxf_writer)

    def generate_drawings(self, csv_filename: str, output_filename: str, use_zone_details: bool = True) -> int:
        """
        Generate all column drawings from CSV data.

        Args:
            csv_filename: Path to CSV file containing column data
            output_filename: Path for output DXF file
            use_zone_details: Whether to use zone details (default: True)

        Returns:
            int: Number of successfully generated drawings

        Raises:
            FileNotFoundError: If CSV file doesn't exist
            ValueError: If CSV data is invalid
        """
        try:
            # Setup logging with output directory for log file placement
            from ..utils.logging_config import setup_logging
            setup_logging(debug_mode=False, output_path=output_filename)
            
            logger.info(f"Processing {csv_filename} -> {output_filename}")

            if use_zone_details:
                # Read and validate data with zone configurations
                columns_data = self.data_processor.read_and_validate_data_with_zones(
                    csv_filename)
                if not columns_data:
                    raise ValueError("No valid column data found")

                # Generate drawings for each column with zone details
                successful_count = self.drawing_orchestrator.process_columns_with_zones(
                    columns_data)
            else:
                # Use legacy processing without zone details
                columns_data = self.data_processor.read_and_validate_data(
                    csv_filename)
                if not columns_data:
                    raise ValueError("No valid column data found")

                # Generate drawings for each column (legacy mode)
                successful_count = self.drawing_orchestrator.process_columns(
                    columns_data)

            # Save the DXF file
            save_success = self.dxf_writer.save_document(output_filename)
            if not save_success:
                raise RuntimeError("Failed to save DXF file")

            # Log consolidated message summary
            from ..utils.logging_config import get_message_counter
            message_counter = get_message_counter()
            message_counter.log_summary(logger)

            logger.info(f"Successfully generated {successful_count} drawings")
            return successful_count

        except Exception as e:
            logger.error(f"Error generating drawings: {e}")
            raise

    def generate_drawings_with_spaces(self, csv_filename: str, output_filename: str, use_zone_details: bool = True) -> int:
        """
        Generate all column drawings from CSV data with drawing space organization.
        
        This is the enhanced version that organizes tables into structured drawing spaces
        within the same DXF file.

        Args:
            csv_filename: Path to CSV file containing column data
            output_filename: Path for output DXF file
            use_zone_details: Whether to use zone details (default: True)

        Returns:
            int: Number of successfully generated drawings

        Raises:
            FileNotFoundError: If CSV file doesn't exist
            ValueError: If CSV data is invalid
        """
        try:
            # Setup logging with output directory for log file placement
            from ..utils.logging_config import setup_logging
            setup_logging(debug_mode=False, output_path=output_filename)
            
            logger.info(f"Processing {csv_filename} -> {output_filename} with drawing space organization")

            if use_zone_details:
                # Read and validate data with zone configurations
                columns_data = self.data_processor.read_and_validate_data_with_zones(
                    csv_filename)
                if not columns_data:
                    raise ValueError("No valid column data found")

                # Generate drawings with drawing space organization
                successful_count = self.drawing_orchestrator.process_columns_with_zones_and_spaces(
                    columns_data)
            else:
                # For non-zone mode, fall back to regular processing
                # (Drawing spaces work best with zone details)
                columns_data = self.data_processor.read_and_validate_data(
                    csv_filename)
                if not columns_data:
                    raise ValueError("No valid column data found")

                # Generate drawings for each column (legacy mode)
                successful_count = self.drawing_orchestrator.process_columns(
                    columns_data)

            # Save the DXF file
            save_success = self.dxf_writer.save_document(output_filename)
            if not save_success:
                raise RuntimeError("Failed to save DXF file")

            # Log consolidated message summary
            from ..utils.logging_config import get_message_counter
            message_counter = get_message_counter()
            message_counter.log_summary(logger)

            logger.info(f"Successfully generated {successful_count} drawings with drawing space organization")
            return successful_count

        except Exception as e:
            logger.error(f"Error generating drawings with spaces: {e}")
            raise

    def get_generation_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive statistics about the drawing generation process.

        Returns:
            dict: Detailed statistics including document info, validation results, table configuration, etc.
        """
        try:
            generation_stats = {
                'document_info': self.dxf_writer.get_document_info(),
                'csv_validation_errors': len(self.data_processor.get_validation_errors()),
                'config_version': '4.0',  # Updated for new table cell configuration
                'table_configuration': {
                    'cell_count': len(self.table_cell_config.get_all_cells()),
                    'zone_detail_cells': len(self.table_cell_config.get_zone_detail_cells()),
                    'cell_types': {cell_type.value: len(self.table_cell_config.get_cells_by_type(cell_type))
                                   for cell_type in CellType}
                },
                'components': {
                    'csv_reader': type(self.csv_reader).__name__,
                    'dxf_writer': type(self.dxf_writer).__name__,
                    'rebar_calculator': type(self.rebar_calculator).__name__,
                    'geometry_calculator': type(self.geometry_calculator).__name__,
                    'table_cell_config': type(self.table_cell_config).__name__
                }
            }

            # Add rebar data statistics if available
            rebar_data = self.drawing_orchestrator.get_column_rebar_data()
            if rebar_data:
                generation_stats['rebar_data'] = {
                    'columns_processed': len(rebar_data),
                    'column_names': list(rebar_data.keys())
                }

            return generation_stats

        except Exception as e:
            logger.error(f"Error getting generation statistics: {e}")
            return {}

    def get_column_rebar_data(self, column_name: str = None) -> Optional[RebarLayerData]:
        """
        Get organized rebar data for a specific column or all columns.

        Args:
            column_name: Name of the column to retrieve data for (optional)

        Returns:
            RebarLayerData for specific column, or dict of all column data
        """
        return self.drawing_orchestrator.get_column_rebar_data(column_name)

    def export_rebar_data_summary(self) -> Dict[str, Any]:
        """
        Export a comprehensive summary of all rebar data for analysis.

        Returns:
            Dictionary with detailed rebar information for all columns
        """
        rebar_data = self.drawing_orchestrator.get_column_rebar_data()
        if not rebar_data:
            logger.warning(
                "No rebar data available. Run generate_drawings() first.")
            return {}

        summary = {}
        for column_name, column_rebar_data in rebar_data.items():
            summary[column_name] = column_rebar_data.get_coordinate_summary()

        return summary

    def get_table_cell_info(self, cell_name: str = None) -> Dict[str, Any]:
        """
        Get information about table cell configuration for debugging and analysis.

        Args:
            cell_name: Specific cell name to get info for (optional)

        Returns:
            dict: Table cell information
        """
        try:
            if cell_name:
                # Get specific cell information
                cell_def = self.table_cell_config.get_cell(cell_name)
                if cell_def:
                    return {
                        'cell_name': cell_def.name,
                        'cell_type': cell_def.cell_type.value,
                        'shape': cell_def.shape.value,
                        'description': cell_def.description,
                        'center_point': cell_def.center_point,
                        'dimensions': {
                            'width': cell_def.width,
                            'height': cell_def.height,
                            'area': cell_def.area
                        },
                        'bounds': cell_def.bounds,
                        'corner_count': len(cell_def.corner_points)
                    }
                else:
                    return {'error': f'Cell {cell_name} not found'}
            else:
                # Get overall table information
                all_cells = self.table_cell_config.get_all_cells()
                return {
                    'total_cells': len(all_cells),
                    'available_cells': list(all_cells.keys()),
                    'cell_types': {
                        cell_type.value: [
                            cell.name for cell in self.table_cell_config.get_cells_by_type(cell_type).values()]
                        for cell_type in CellType
                    },
                    'table_bounds': self.table_cell_config.get_table_bounds(),
                    'statistics': self.table_cell_config.get_cell_statistics()
                }

        except Exception as e:
            logger.error(f"Error getting table cell info: {e}")
            return {'error': str(e)}
