"""
Note Selection Orchestrator
===========================

Coordinates the note selection workflow for the General Notes Drawing Generator.

This module manages the note selection process including:
- Selection criteria processing
- Filter application
- Selection validation
- Selection state management

Author: Drawing Production System
"""

import logging
from typing import List, Optional, Dict, Any

from ..core.application_core import ApplicationCore
from ..models.selection import SelectionState, SelectionCriteria

logger = logging.getLogger(__name__)


class NoteSelectionOrchestrator:
    """
    Orchestrator for note selection workflow.
    
    Manages the process of selecting notes based on criteria,
    applying filters, and validating selections.
    """
    
    def __init__(self, core: ApplicationCore):
        """
        Initialize the note selection orchestrator.
        
        Args:
            core: Application core instance
        """
        self.core = core
        
    def process_selection(self, selected_notes: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Process note selection.
        
        Args:
            selected_notes: List of note titles to select
            
        Returns:
            Dictionary with selection results
        """
        try:
            selection_state = self.core.get_selection_state()
            available_notes = self.core.get_available_notes()
            
            if selected_notes:
                # Validate selection
                self.core.business_logic.validate_selection(selected_notes, available_notes)
                
                # Apply selection
                selection_state.deselect_all("Clear for new selection")
                for note_title in selected_notes:
                    selection_state.select_note(note_title, "Orchestrator selection")
            else:
                # Select all if none specified
                selection_state.select_all(available_notes, "Select all notes")
            
            return selection_state.get_selection_summary()
            
        except Exception as e:
            logger.error(f"Note selection processing failed: {str(e)}")
            raise
