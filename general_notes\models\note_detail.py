"""
Note Detail Models
=================

Data models for representing note details with boundary information and metadata.

This module provides classes for:
- Individual note details with associated boundaries
- Collections of note details with management functionality
- Note detail validation and metadata handling

Author: Drawing Production System
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from datetime import datetime
import logging

from .boundary import Boundary, BoundaryValidationError

logger = logging.getLogger(__name__)


@dataclass
class NoteDetail:
    """
    Represents a single note detail with its boundary and metadata.
    
    A note detail defines a specific technical drawing element that can be
    selected and copied from a DXF database based on its boundary coordinates.
    
    Attributes:
        title: Unique identifier for the note detail
        boundary: Coordinate boundary defining the note area
        description: Optional description of the note content
        category: Optional category for grouping notes
        tags: Optional list of tags for filtering and search
        metadata: Additional metadata dictionary
        created_date: Creation timestamp
        is_selected: Selection state for UI purposes
    """
    title: str
    boundary: Boundary
    description: Optional[str] = None
    category: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_date: datetime = field(default_factory=datetime.now)
    is_selected: bool = False
    
    def __post_init__(self):
        """Validate note detail after initialization."""
        self._validate_title()
        self._validate_boundary()
    
    def _validate_title(self) -> None:
        """Validate the note title."""
        if not self.title or not self.title.strip():
            raise ValueError("Note title cannot be empty")
        
        # Ensure title is a clean string
        self.title = self.title.strip()
        
        # Check for reasonable title length
        if len(self.title) > 200:
            logger.warning(f"Note title is very long ({len(self.title)} characters): {self.title[:50]}...")
    
    def _validate_boundary(self) -> None:
        """Validate the boundary."""
        if not isinstance(self.boundary, Boundary):
            raise TypeError("Boundary must be a Boundary instance")
        
        # Ensure boundary has reasonable dimensions
        min_size = 1.0  # Minimum 1 unit
        max_size = 100000.0  # Maximum 100,000 units
        
        if self.boundary.width < min_size or self.boundary.height < min_size:
            logger.warning(f"Note '{self.title}' has very small boundary: {self.boundary.width}×{self.boundary.height}")
        
        if self.boundary.width > max_size or self.boundary.height > max_size:
            logger.warning(f"Note '{self.title}' has very large boundary: {self.boundary.width}×{self.boundary.height}")
    
    @property
    def area(self) -> float:
        """Get the area of the note detail boundary."""
        return self.boundary.area
    
    @property
    def aspect_ratio(self) -> float:
        """Get the aspect ratio (width/height) of the note detail."""
        if self.boundary.height == 0:
            return float('inf')
        return self.boundary.width / self.boundary.height
    
    def matches_search(self, search_term: str) -> bool:
        """
        Check if the note detail matches a search term.
        
        Args:
            search_term: Search string to match against
            
        Returns:
            True if the note matches the search term
        """
        if not search_term:
            return True
        
        search_lower = search_term.lower()
        
        # Search in title
        if search_lower in self.title.lower():
            return True
        
        # Search in description
        if self.description and search_lower in self.description.lower():
            return True
        
        # Search in category
        if self.category and search_lower in self.category.lower():
            return True
        
        # Search in tags
        if any(search_lower in tag.lower() for tag in self.tags):
            return True
        
        return False
    
    def matches_category(self, category: str) -> bool:
        """
        Check if the note detail matches a category.
        
        Args:
            category: Category to match against
            
        Returns:
            True if the note matches the category
        """
        if not category:
            return True
        
        return self.category and self.category.lower() == category.lower()
    
    def add_tag(self, tag: str) -> None:
        """
        Add a tag to the note detail.
        
        Args:
            tag: Tag to add
        """
        if tag and tag not in self.tags:
            self.tags.append(tag.strip())
    
    def remove_tag(self, tag: str) -> None:
        """
        Remove a tag from the note detail.
        
        Args:
            tag: Tag to remove
        """
        if tag in self.tags:
            self.tags.remove(tag)
    
    def set_metadata(self, key: str, value: Any) -> None:
        """
        Set a metadata value.
        
        Args:
            key: Metadata key
            value: Metadata value
        """
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """
        Get a metadata value.
        
        Args:
            key: Metadata key
            default: Default value if key not found
            
        Returns:
            Metadata value or default
        """
        return self.metadata.get(key, default)
    
    @classmethod
    def from_excel_row(cls, row_data: dict) -> 'NoteDetail':
        """
        Create NoteDetail from Excel row data.
        
        Args:
            row_data: Dictionary with Excel row data
            
        Returns:
            NoteDetail instance
            
        Raises:
            ValueError: If row data is invalid
        """
        try:
            # Create boundary from row data
            boundary = Boundary.from_excel_row(row_data)
            
            # Extract title
            title = row_data.get('Title', '').strip()
            if not title:
                raise ValueError("Title is required")
            
            # Extract optional fields
            description = row_data.get('Description', '').strip() or None
            category = row_data.get('Category', '').strip() or None
            
            # Parse tags if present
            tags = []
            tags_str = row_data.get('Tags', '')
            if tags_str:
                tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()]
            
            return cls(
                title=title,
                boundary=boundary,
                description=description,
                category=category,
                tags=tags
            )
            
        except BoundaryValidationError:
            raise
        except Exception as e:
            raise ValueError(f"Error creating note detail from row data: {e}")
    
    def __str__(self) -> str:
        """String representation of the note detail."""
        return f"NoteDetail('{self.title}', {self.boundary.width:.1f}×{self.boundary.height:.1f})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the note detail."""
        return (f"NoteDetail(title='{self.title}', "
                f"boundary={self.boundary}, "
                f"category='{self.category}', "
                f"selected={self.is_selected})")


@dataclass
class NoteDetailCollection:
    """
    Collection of note details with management functionality.
    
    Provides methods for filtering, searching, and managing groups of note details.
    
    Attributes:
        notes: List of note details
        name: Optional name for the collection
        description: Optional description of the collection
    """
    notes: List[NoteDetail] = field(default_factory=list)
    name: Optional[str] = None
    description: Optional[str] = None
    
    def __len__(self) -> int:
        """Get the number of notes in the collection."""
        return len(self.notes)
    
    def __iter__(self):
        """Iterate over notes in the collection."""
        return iter(self.notes)
    
    def __getitem__(self, index) -> NoteDetail:
        """Get note by index."""
        return self.notes[index]
    
    def add_note(self, note: NoteDetail) -> None:
        """
        Add a note to the collection.
        
        Args:
            note: Note detail to add
        """
        if not isinstance(note, NoteDetail):
            raise TypeError("Can only add NoteDetail instances")
        
        # Check for duplicate titles
        if self.get_note_by_title(note.title):
            logger.warning(f"Note with title '{note.title}' already exists in collection")
        
        self.notes.append(note)
    
    def remove_note(self, title: str) -> bool:
        """
        Remove a note by title.
        
        Args:
            title: Title of note to remove
            
        Returns:
            True if note was removed, False if not found
        """
        for i, note in enumerate(self.notes):
            if note.title == title:
                del self.notes[i]
                return True
        return False
    
    def get_note_by_title(self, title: str) -> Optional[NoteDetail]:
        """
        Get a note by its title.
        
        Args:
            title: Note title to search for
            
        Returns:
            NoteDetail if found, None otherwise
        """
        for note in self.notes:
            if note.title == title:
                return note
        return None
    
    def get_selected_notes(self) -> List[NoteDetail]:
        """
        Get all selected notes.
        
        Returns:
            List of selected note details
        """
        return [note for note in self.notes if note.is_selected]
    
    def get_notes_by_category(self, category: str) -> List[NoteDetail]:
        """
        Get notes by category.
        
        Args:
            category: Category to filter by
            
        Returns:
            List of notes in the specified category
        """
        return [note for note in self.notes if note.matches_category(category)]
    
    def search_notes(self, search_term: str) -> List[NoteDetail]:
        """
        Search notes by term.
        
        Args:
            search_term: Search term
            
        Returns:
            List of notes matching the search term
        """
        return [note for note in self.notes if note.matches_search(search_term)]
    
    def get_categories(self) -> List[str]:
        """
        Get all unique categories in the collection.
        
        Returns:
            List of unique categories
        """
        categories = set()
        for note in self.notes:
            if note.category:
                categories.add(note.category)
        return sorted(list(categories))
    
    def get_all_tags(self) -> List[str]:
        """
        Get all unique tags in the collection.
        
        Returns:
            List of unique tags
        """
        tags = set()
        for note in self.notes:
            tags.update(note.tags)
        return sorted(list(tags))
    
    def select_all(self) -> None:
        """Select all notes in the collection."""
        for note in self.notes:
            note.is_selected = True
    
    def deselect_all(self) -> None:
        """Deselect all notes in the collection."""
        for note in self.notes:
            note.is_selected = False
    
    def get_total_area(self) -> float:
        """
        Get the total area of all notes.
        
        Returns:
            Sum of all note areas
        """
        return sum(note.area for note in self.notes)
    
    def get_selection_summary(self) -> Dict[str, Any]:
        """
        Get summary information about the current selection.
        
        Returns:
            Dictionary with selection statistics
        """
        selected = self.get_selected_notes()
        return {
            'total_notes': len(self.notes),
            'selected_notes': len(selected),
            'selection_percentage': len(selected) / len(self.notes) * 100 if self.notes else 0,
            'total_area': self.get_total_area(),
            'selected_area': sum(note.area for note in selected),
            'categories': self.get_categories(),
            'selected_categories': list(set(note.category for note in selected if note.category))
        }
