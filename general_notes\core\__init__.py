"""
Core Package
===========

Core application functionality for the General Notes Drawing Generator.

This package provides the central application logic and coordination:
- Application core facade pattern implementation
- Business logic and rules
- Dependency injection and component initialization
- Application constants and configuration

Modules:
    - application_core: Facade pattern implementation for core functionality
    - business_logic: Core business rules and validation
    - dependency_injection: Component initialization and wiring
    - constants: Application constants and enumerations
"""

from .application_core import ApplicationCore
from .business_logic import BusinessLogic
from .dependency_injection import <PERSON>penden<PERSON><PERSON>ontaine<PERSON>
from .constants import (
    APPLICATION_NAME,
    APPLICATION_VERSION,
    DEFAULT_DXF_VERSION,
    SUPPORTED_FILE_FORMATS,
    COORDINATE_PRECISION,
    MAX_ENTITIES_PER_NOTE,
    DEFAULT_LAYOUT_SETTINGS
)

__all__ = [
    # Core components
    'ApplicationCore',
    'BusinessLogic',
    'DependencyContainer',
    
    # Constants
    'APPLICATION_NAME',
    'APPLICATION_VERSION',
    'DEFAULT_DXF_VERSION',
    'SUPPORTED_FILE_FORMATS',
    'COORDINATE_PRECISION',
    'MAX_ENTITIES_PER_NOTE',
    'DEFAULT_LAYOUT_SETTINGS'
]
