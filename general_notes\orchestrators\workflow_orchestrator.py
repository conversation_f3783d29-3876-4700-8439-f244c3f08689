"""
Workflow Orchestrator
====================

High-level workflow coordination for the General Notes Drawing Generator.

This module provides the main workflow orchestrator that coordinates the entire
process from file loading to output generation, managing the interaction between
all subsystems and handling error recovery.

Author: Drawing Production System
"""

import logging
from typing import List, Optional, Dict, Any, Callable
from pathlib import Path

from ..core.application_core import ApplicationCore
from ..models.note_detail import NoteDetailCollection
from ..models.selection import SelectionState
from .note_selection_orchestrator import NoteSelectionOrchestrator
from .generation_orchestrator import GenerationOrchestrator

logger = logging.getLogger(__name__)


class WorkflowError(Exception):
    """Raised when workflow execution fails."""
    pass


class WorkflowOrchestrator:
    """
    Main workflow orchestrator for the General Notes Drawing Generator.
    
    Coordinates the complete workflow from file loading through output generation:
    1. Load and validate input files
    2. Parse note details and catalog entities
    3. Manage note selection process
    4. Generate output drawings
    5. Handle errors and provide status updates
    
    Attributes:
        core: Application core instance
        selection_orchestrator: Note selection coordinator
        generation_orchestrator: Output generation coordinator
        progress_callback: Optional callback for progress updates
    """
    
    def __init__(self, 
                 core: ApplicationCore,
                 progress_callback: Optional[Callable[[str, float], None]] = None):
        """
        Initialize the workflow orchestrator.
        
        Args:
            core: Application core instance
            progress_callback: Optional callback for progress updates (message, percentage)
        """
        self.core = core
        self.progress_callback = progress_callback
        
        # Initialize sub-orchestrators
        self.selection_orchestrator = NoteSelectionOrchestrator(core)
        self.generation_orchestrator = GenerationOrchestrator(core)
        
        # Workflow state
        self._current_step = ""
        self._total_steps = 0
        self._completed_steps = 0
        
        logger.info("Workflow orchestrator initialized")
    
    def execute_workflow(self,
                        database_path: str,
                        summary_path: str,
                        output_path: str,
                        selected_notes: Optional[List[str]] = None) -> bool:
        """
        Execute the complete workflow from input files to output generation.
        
        Args:
            database_path: Path to DXF database file
            summary_path: Path to Excel summary file
            output_path: Path for output DXF file
            selected_notes: List of note titles to include (all if None)
            
        Returns:
            True if workflow completed successfully
            
        Raises:
            WorkflowError: If workflow execution fails
        """
        try:
            logger.info("Starting complete workflow execution")
            self._initialize_workflow_progress(5)  # 5 main steps
            
            # Step 1: Load and validate input files
            self._update_progress("Loading input files...", 0)
            self._load_input_files(database_path, summary_path)
            self._complete_step()
            
            # Step 2: Parse and catalog entities
            self._update_progress("Cataloging entities...", 1)
            self._catalog_entities()
            self._complete_step()
            
            # Step 3: Handle note selection
            self._update_progress("Processing note selection...", 2)
            self._process_note_selection(selected_notes)
            self._complete_step()
            
            # Step 4: Validate selection and prerequisites
            self._update_progress("Validating workflow...", 3)
            self._validate_workflow_prerequisites()
            self._complete_step()
            
            # Step 5: Generate output
            self._update_progress("Generating output...", 4)
            success = self._generate_output(output_path)
            self._complete_step()
            
            if success:
                self._update_progress("Workflow completed successfully", 5)
                logger.info("Workflow execution completed successfully")
                return True
            else:
                raise WorkflowError("Output generation failed")
                
        except Exception as e:
            error_msg = f"Workflow execution failed: {str(e)}"
            logger.error(error_msg)
            self._update_progress(f"Error: {str(e)}", self._completed_steps)
            raise WorkflowError(error_msg) from e
    
    def execute_interactive_workflow(self) -> Dict[str, Any]:
        """
        Execute workflow in interactive mode for GUI applications.
        
        Returns workflow status and allows for step-by-step execution
        with user interaction between steps.
        
        Returns:
            Dictionary with workflow status and next steps
        """
        try:
            logger.info("Starting interactive workflow")
            
            # Get current workflow status
            status = self.core.get_workflow_status()
            
            # Determine next steps based on current state
            next_steps = self._determine_next_steps(status)
            
            return {
                'status': 'ready',
                'current_state': status,
                'next_steps': next_steps,
                'can_proceed': self._can_proceed_with_workflow(status)
            }
            
        except Exception as e:
            logger.error(f"Interactive workflow error: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'next_steps': ['fix_error']
            }
    
    def load_files_step(self, database_path: str, summary_path: str) -> Dict[str, Any]:
        """
        Execute the file loading step of the workflow.
        
        Args:
            database_path: Path to DXF database file
            summary_path: Path to Excel summary file
            
        Returns:
            Dictionary with step results
        """
        try:
            logger.info("Executing file loading step")
            
            # Load input files
            self._load_input_files(database_path, summary_path)
            
            # Catalog entities
            self._catalog_entities()
            
            return {
                'status': 'success',
                'message': 'Files loaded and entities cataloged successfully',
                'note_count': len(self.core.get_available_notes()),
                'entity_summary': self.core.get_entity_catalog().get_catalog_summary()
            }
            
        except Exception as e:
            logger.error(f"File loading step failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def selection_step(self, selected_notes: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Execute the note selection step of the workflow.
        
        Args:
            selected_notes: List of note titles to select
            
        Returns:
            Dictionary with step results
        """
        try:
            logger.info("Executing selection step")
            
            result = self.selection_orchestrator.process_selection(selected_notes)
            
            return {
                'status': 'success',
                'message': 'Note selection processed successfully',
                'selection_summary': result
            }
            
        except Exception as e:
            logger.error(f"Selection step failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def generation_step(self, output_path: str) -> Dict[str, Any]:
        """
        Execute the output generation step of the workflow.
        
        Args:
            output_path: Path for output file
            
        Returns:
            Dictionary with step results
        """
        try:
            logger.info("Executing generation step")
            
            result = self.generation_orchestrator.generate_output(output_path)
            
            return {
                'status': 'success',
                'message': 'Output generated successfully',
                'output_path': output_path,
                'generation_summary': result
            }
            
        except Exception as e:
            logger.error(f"Generation step failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def get_available_notes(self, database_path: str, summary_path: str) -> List[str]:
        """
        Get list of available note titles from input files.
        
        Args:
            database_path: Path to DXF database file
            summary_path: Path to Excel summary file
            
        Returns:
            List of available note titles
            
        Raises:
            WorkflowError: If files cannot be loaded
        """
        try:
            # Load files if not already loaded
            if not self.core.get_note_collection():
                self._load_input_files(database_path, summary_path)
            
            return self.core.get_available_notes()
            
        except Exception as e:
            raise WorkflowError(f"Cannot get available notes: {str(e)}") from e
    
    def _load_input_files(self, database_path: str, summary_path: str) -> None:
        """Load and validate input files."""
        # Load note collection from Excel
        self.core.load_note_collection(summary_path)
        
        # Load DXF database
        self.core.load_dxf_database(database_path)
        
        logger.debug("Input files loaded successfully")
    
    def _catalog_entities(self) -> None:
        """Catalog entities from DXF file by note boundaries."""
        self.core.catalog_entities()
        logger.debug("Entity cataloging completed")
    
    def _process_note_selection(self, selected_notes: Optional[List[str]]) -> None:
        """Process note selection."""
        if selected_notes:
            # Use provided selection
            available_notes = self.core.get_available_notes()
            self.core.business_logic.validate_selection(selected_notes, available_notes)
            
            # Update selection state
            selection_state = self.core.get_selection_state()
            selection_state.deselect_all("Clear previous selection")
            for note_title in selected_notes:
                selection_state.select_note(note_title, "CLI selection")
        else:
            # Select all notes if none specified
            available_notes = self.core.get_available_notes()
            selection_state = self.core.get_selection_state()
            selection_state.select_all(available_notes, "Select all notes")
        
        logger.debug(f"Note selection processed: {len(self.core.get_selection_state().selected_titles)} notes selected")
    
    def _validate_workflow_prerequisites(self) -> None:
        """Validate that all workflow prerequisites are met."""
        prerequisites = self.core.validate_workflow_prerequisites()
        
        if not prerequisites['all_prerequisites_met']:
            missing = [key for key, value in prerequisites.items() if not value and key != 'all_prerequisites_met']
            raise WorkflowError(f"Workflow prerequisites not met: {missing}")
        
        logger.debug("Workflow prerequisites validation passed")
    
    def _generate_output(self, output_path: str) -> bool:
        """Generate output file."""
        return self.generation_orchestrator.generate_output(output_path)
    
    def _initialize_workflow_progress(self, total_steps: int) -> None:
        """Initialize workflow progress tracking."""
        self._total_steps = total_steps
        self._completed_steps = 0
        self._current_step = ""
    
    def _update_progress(self, message: str, step: int) -> None:
        """Update workflow progress."""
        self._current_step = message
        percentage = (step / self._total_steps) * 100 if self._total_steps > 0 else 0
        
        logger.info(f"Workflow progress: {message} ({percentage:.1f}%)")
        
        if self.progress_callback:
            try:
                self.progress_callback(message, percentage)
            except Exception as e:
                logger.warning(f"Progress callback error: {str(e)}")
    
    def _complete_step(self) -> None:
        """Mark current step as completed."""
        self._completed_steps += 1
    
    def _determine_next_steps(self, status: Dict[str, Any]) -> List[str]:
        """Determine next steps based on current workflow status."""
        next_steps = []
        
        if not status['note_collection']['loaded']:
            next_steps.append('load_excel_file')
        
        if not status['dxf_database']['loaded']:
            next_steps.append('load_dxf_file')
        
        if not status['entity_catalog']['created']:
            next_steps.append('catalog_entities')
        
        if status['selection']['selected_count'] == 0:
            next_steps.append('select_notes')
        
        if not next_steps:
            next_steps.append('generate_output')
        
        return next_steps
    
    def _can_proceed_with_workflow(self, status: Dict[str, Any]) -> bool:
        """Check if workflow can proceed to next step."""
        prerequisites = status['prerequisites']
        return prerequisites['all_prerequisites_met']
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """
        Get current workflow status.
        
        Returns:
            Dictionary with comprehensive workflow status
        """
        core_status = self.core.get_workflow_status()
        
        return {
            **core_status,
            'orchestrator': {
                'current_step': self._current_step,
                'total_steps': self._total_steps,
                'completed_steps': self._completed_steps,
                'progress_percentage': (self._completed_steps / self._total_steps) * 100 if self._total_steps > 0 else 0
            }
        }
    
    def reset_workflow(self) -> None:
        """Reset workflow to initial state."""
        logger.info("Resetting workflow orchestrator")
        
        self.core.reset_workflow()
        self._current_step = ""
        self._total_steps = 0
        self._completed_steps = 0
        
        logger.info("Workflow orchestrator reset complete")
