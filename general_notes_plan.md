# General Notes Drawing Generator - Implementation Plan

## Project Overview

The General Notes Drawing Generator is a Python application that automates the creation of technical drawings by selecting and copying predefined note details from a template DXF database. The system uses an Excel summary file to define boundaries for each note detail and provides a user-friendly checklist interface for selection.

## Technical Specifications

### Input Files
- **Database DXF File**: Contains all available general note details in a structured layout
  - File Path: `C:\Users\<USER>\OneDrive - Asia Infrastructure Solutions Limited\RPA Documentation\Drafting Agent\General Notes\General Notes Database.dxf`
  - Drawing Space Dimensions: 67,600 units width × 56,400 units height
  - Module Dimensions: 16,900 units width × 14,100 units height (4 modules per drawing space: 2×2 grid)
  - Coordinate System: AutoCAD coordinate system with origin at drawing base point (0,0)
  - Entity Types: Contains lines, text, circles, polylines, blocks, and dimensions
  - Layer Structure: Preserves original layer names, colors, and line types
  - Note Details Layout: Each note detail occupies a specific region defined by boundary coordinates

- **Summary Excel File**: Contains metadata for each note detail including titles and boundary coordinates
  - File Path: `C:\Users\<USER>\OneDrive - Asia Infrastructure Solutions Limited\RPA Documentation\Drafting Agent\General Notes\General Notes Summary.xlsx`
  - Sheet Name: `Detail`
  - Required Columns: Title, Point1, Point2, Point3, Point4
  - Coordinate Format: String format "(x, y, z)" where x, y, z are numeric values
  - Boundary Definition: Four points define a rectangular boundary in counterclockwise order starting from bottom-left
  - Purpose: Defines the exact boundary coordinates for each note detail in the database DXF
  - Data Validation: Each row must have a unique title and valid coordinate points
  - Example Data:
    ```
    Title: "WELDED JOINT FOR H-PILE"
    Point1: "(0, -57400, 0)"      # bottom-left corner
    Point2: "(33800, -57400, 0)"  # bottom-right corner  
    Point3: "(33800, -71500.0, 0)" # top-right corner
    Point4: "(0, -71500.0, 0)"    # top-left corner
    ```

### Output
- **Generated DXF File**: Contains only the selected note details copied from the database with intelligent layout arrangement
  - File Path: User-defined via file dialog
  - Drawing Space Dimensions: 67,600 units width × 56,400 units height (same as database)
  - Module Dimensions: 16,900 units width × 14,100 units height (4 modules per drawing space)
  - Layout Strategy: Automatically arrange selected note details into optimal grid layout within drawing spaces
  - Positioning Logic: 
    - Start from top-left module (0,0) of first drawing space
    - Fill modules left-to-right, top-to-bottom
    - Create new drawing space when current space is full
    - Maintain original relative positions of entities within each note detail
  - Spacing Requirements: Use module boundaries as natural spacing between note details
- **File Format**: AutoCAD-compatible DXF format (R2018 or newer)
- **Content**: Selected entities within specified boundaries, preserving all original properties
  - Entity Preservation: Maintain all original entity properties including layers, colors, line weights, and line types
  - Text Integrity: Preserve all text formatting, fonts, and sizing
  - Block References: Maintain block definitions and their instances
  - Dimension Accuracy: Preserve dimensional annotations and their associativity

## Architecture Design

### Package Structure
The application is implemented as a standalone package within the Drawing-Production project, following established architectural patterns without direct dependencies on other packages except for auth and drawing_spaces.

### Dependencies and References
The general_notes package has controlled dependencies and references within the Drawing-Production project:

**Allowed Dependencies:**
- `auth` package for user authentication and session management
- `drawing_spaces` package for layout management and drawing space creation

**Reference Only (No Direct Dependencies):**
- `column_drawing` package patterns for architectural consistency without function calls
- Follow established patterns and naming conventions from column_drawing
- Reference configuration and error handling approaches for consistency

**Independence Requirements:**
- No direct function calls to column_drawing modules
- Independent implementation of core utilities and managers
- Can leverage auth package for authentication services
- Can use drawing_spaces package for layout functionality

**Main Package**: `general_notes/`
- `__init__.py`: Package initialization
- `__main__.py`: Entry point for standalone execution
- `main.py`: Main application logic and CLI/GUI mode selection
- `README.md`: Package documentation
- `requirements.txt`: Package-specific dependencies

**Models Subpackage**: `general_notes/models/`
- `note_detail.py`: Data structure for note details with boundary information
- `configuration.py`: Configuration classes using dataclass patterns
- `selection.py`: User selection state management
- `boundary.py`: Boundary coordinate representation and validation

**Parsers Subpackage**: `general_notes/parsers/`
- `excel_parser.py`: Excel file parsing with pandas/openpyxl
- `dxf_parser.py`: DXF database loading using ezdxf
- `coordinate_parser.py`: Parse "(x, y, z)" format strings
- `validation.py`: Input validation and error reporting

**Processors Subpackage**: `general_notes/processors/`
- `entity_selector.py`: Entity selection based on boundaries
- `boundary_processor.py`: Boundary validation and processing
- `layout_processor.py`: Layout optimization algorithms
- `validation_processor.py`: Data validation and consistency checks

**Generators Subpackage**: `general_notes/generators/`
- `dxf_generator.py`: Output DXF file creation
- `entity_copier.py`: Entity copying with property preservation
- `layout_generator.py`: Drawing space layout generation
- `report_generator.py`: Summary report generation

**Orchestrators Subpackage**: `general_notes/orchestrators/`
- `note_selection_orchestrator.py`: Coordinate note selection workflow
- `generation_orchestrator.py`: Manage output generation process
- `workflow_orchestrator.py`: High-level process coordination

**Managers Subpackage**: `general_notes/managers/`
- `layout_manager.py`: Manage note detail arrangement
- `entity_manager.py`: Manage entity relationships
- `configuration_manager.py`: Manage application configuration
- `layer_manager.py`: Handle layer operations

**Interfaces Subpackage**: `general_notes/interfaces/`
- `main_window.py`: Main GUI window using tkinter
- `checklist_widget.py`: Note selection checklist
- `progress_dialog.py`: Progress indication
- `error_dialog.py`: Error reporting interface

**Utils Subpackage**: `general_notes/utils/`
- `coordinate_utils.py`: Coordinate parsing and validation
- `geometry_utils.py`: Geometric calculations
- `file_utils.py`: File path handling
- `logging_utils.py`: Logging configuration

**Core Subpackage**: `general_notes/core/`
- `application_core.py`: Facade pattern implementation
- `business_logic.py`: Core business rules
- `dependency_injection.py`: Component initialization
- `constants.py`: Application constants

## Core Components

### Excel Parser
Responsible for reading and interpreting the Excel summary file to extract note titles and their corresponding boundary coordinates.

**Primary Functions:**
```python
def load_excel_file(filepath: str) -> pd.DataFrame:
    """Load Excel file and validate structure"""
    
def parse_coordinates(coord_str: str) -> Tuple[float, float, float]:
    """Parse '(x, y, z)' format to numeric tuple"""
    
def extract_note_details(df: pd.DataFrame) -> List[NoteDetail]:
    """Extract note details with boundaries from DataFrame"""
```

**Data Processing:**
- Convert coordinate text to numeric values with validation
- Example parsing: "(33800, -57400, 0)" → (33800.0, -57400.0, 0.0)
- Validate rectangular boundary formation
- Handle coordinate precision (6 decimal places)
- Check z-coordinate consistency (typically 0 for 2D drawings)

### Database Parser
Handles loading the DXF database file and cataloging entities according to the boundary definitions.

**Primary Functions:**
```python
def load_dxf_database(filepath: str) -> ezdxf.document.Drawing:
    """Load DXF file using ezdxf"""
    
def get_entities_in_boundary(doc: Drawing, boundary: Boundary) -> List[DXFEntity]:
    """Get all entities within boundary polygon"""
    
def catalog_entities_by_note(doc: Drawing, note_details: List[NoteDetail]) -> Dict[str, List[DXFEntity]]:
    """Group entities by note title"""
```

**Entity Management:**
- Use ezdxf's spatial query functions for boundary selection
- Handle entity types: LINE, TEXT, CIRCLE, LWPOLYLINE, INSERT, DIMENSION
- Preserve entity properties: layer, color, linetype, lineweight
- Handle block references with proper transformation matrices

### Selection Processor
Manages user selections and prepares chosen note details for output.

**Primary Functions:**
```python
def process_selections(selected_titles: List[str], entity_catalog: Dict) -> Dict[str, List[DXFEntity]:
    """Process user selections and return entities to copy"""
    
def validate_selection(title: str, entities: List[DXFEntity]) -> ValidationResult:
    """Validate that selection has valid entities"""
```

### Notes Generator
Creates the final output DXF file with intelligent layout management.

**Primary Functions:**
```python
def create_output_dxf(selected_entities: Dict[str, List[DXFEntity]], output_path: str):
    """Generate output DXF with selected note details"""
    
def arrange_in_drawing_spaces(note_details: List[NoteDetail]) -> List[DrawingSpaceLayout]:
    """Calculate optimal arrangement in drawing spaces"""
    
def copy_entities_with_translation(entities: List[DXFEntity], offset: Vector) -> List[DXFEntity]:
    """Copy entities with position offset"""
```

**Layout Algorithm:**
1. Calculate bounding box for each selected note detail
2. Determine module assignment (1 module = 16,900 × 14,100 units)
3. Arrange in 2×2 grid per drawing space
4. Create new drawing space when full
5. Apply translation to position entities in assigned modules

### User Interface
Provides intuitive checklist interface for note selection.

**Interface Components:**
```python
class NoteSelectionWindow(tk.Tk):
    """Main application window"""
    
class ChecklistFrame(tk.Frame):
    """Scrollable checklist of note titles"""
    
class ControlPanel(tk.Frame):
    """Buttons and file selection controls"""
```

**Features:**
- Searchable/filterable checklist
- Select all/none buttons
- Preview selected count
- File browser for output path
- Progress bar during generation

## Implementation Guidelines

### Coordinate System Details
- Origin (0,0) is at bottom-left of drawing
- Positive X extends right, positive Y extends up
- All coordinates in drawing units (typically millimeters)
- Module grid starts at origin of each drawing space
- Translation vectors calculated as: `(module_col * 16900, module_row * 14100, 0)`

### Entity Selection Algorithm
```python
def point_in_polygon(point: Tuple[float, float], polygon: List[Tuple[float, float]]) -> bool:
    """Ray casting algorithm for point-in-polygon test"""

def entity_in_boundary(entity: DXFEntity, boundary: Boundary) -> bool:
    """Check if entity is within boundary"""
    # For LINE: both endpoints inside
    # For TEXT: insertion point inside
    # For CIRCLE: center inside
    # For LWPOLYLINE: all vertices inside
    # For INSERT: insertion point inside
```

### Error Handling Examples
```python
class CoordinateParseError(Exception):
    """Raised when coordinate string cannot be parsed"""
    
class BoundaryValidationError(Exception):
    """Raised when boundary points don't form valid rectangle"""
    
class DXFLoadError(Exception):
    """Raised when DXF file cannot be loaded"""
```