"""
Table Text Utilities
===================

Text-related operations for table drawing in the Drawing-Production application.
This module handles all text placement, layer management, and formatting operations
for table elements.

Key Features:
- Layer management for text elements (table text, headers, data)
- Text placement with proper positioning and alignment
- Style management (engineering, title styles)
- Data text preparation and formatting
- Professional text rendering with AIA layer standards

This module was extracted from TableDrawer to improve maintainability and
reduce the main class size while preserving all functionality.
"""

import logging
from typing import Tuple, Optional
from ezdxf.enums import TextEntityAlignment
from ezdxf import const
from ...models.column_config import ColumnConfig
from ...models.table_config import TableConfig
from ...io.dxf_writer import DXFWriter
from .layer_manager_mixin import LayerManagerMixin
from .validation_utils import TableValidationUtils, TableValidationError

logger = logging.getLogger(__name__)


class TableTextManager(LayerManagerMixin):
    """
    Manages all text-related operations for table drawing.

    This class handles text layer management, text placement, and formatting
    for all table elements including titles, data, and labels.
    """

    def __init__(self, table_config: TableConfig, dxf_writer: Optional[DXFWriter] = None, modelspace=None):
        """
        Initialize the table text manager.

        Args:
            table_config: Table configuration instance
            dxf_writer: DXF writer with layer management (optional)
            modelspace: DXF modelspace for drawing operations (optional)
        """
        # Validate required inputs
        TableValidationUtils.validate_table_config(
            table_config, "TableTextManager initialization")
        TableValidationUtils.validate_optional_config(
            dxf_writer, "DXFWriter", "TableTextManager initialization")

        super().__init__(table_config, dxf_writer)
        self.msp = modelspace

    def add_main_bar_title_text(self, text_layer: str, position: Tuple[float, float]) -> None:
        """Add 'MAIN BAR' text at the specified position."""
        try:
            # Validate inputs
            TableValidationUtils.validate_text_content(
                text_layer, "text layer")
            TableValidationUtils.validate_coordinates(
                position[0], position[1], "text position")
            TableValidationUtils.validate_modelspace(
                self.msp, "modelspace for text drawing")

            return TableValidationUtils.safe_execute(
                "add main bar title text",
                self._add_text_element,
                "MAIN BAR", text_layer, position, self.table_config.get_text_height_base()
            )
        except Exception as e:
            logger.error(f"Error adding main bar title text: {e}")
            raise

    def _add_text_element(self, text: str, text_layer: str, position: Tuple[float, float], height: float) -> None:
        """Helper method to add a text element with consistent formatting."""
        self.msp.add_text(
            text,
            height=height,
            dxfattribs={'layer': text_layer, 'color': const.BYLAYER, 'lineweight': const.LINEWEIGHT_BYLAYER,
                        'linetype': 'ByLayer', 'style': self.table_config.get_engineering_style()}
        ).set_placement(
            position,
            align=TextEntityAlignment.MIDDLE_CENTER
        )

    def add_size_title_text(self, text_layer: str, position: Tuple[float, float]) -> None:
        """Add 'SIZE' text at the specified position."""
        if not self.msp:
            logger.warning("No modelspace available for text drawing")
            return

        self.msp.add_text(
            "SIZE",
            height=self.table_config.get_text_height_base(),
            dxfattribs={'layer': text_layer, 'color': const.BYLAYER, 'lineweight': const.LINEWEIGHT_BYLAYER,
                        'linetype': 'ByLayer', 'style': self.table_config.get_engineering_style()}
        ).set_placement(
            position,
            align=TextEntityAlignment.MIDDLE_CENTER
        )

    def add_main_bar_data_text(self, data_layer: str, main_bar_text: str, position: Tuple[float, float]) -> None:
        """Add main bar data text at the specified position."""
        if not self.msp:
            logger.warning("No modelspace available for text drawing")
            return

        self.msp.add_text(
            main_bar_text,
            height=self.table_config.get_table_data_text_height(),
            dxfattribs={'layer': data_layer, 'color': const.BYLAYER, 'lineweight': const.LINEWEIGHT_BYLAYER,
                        'linetype': 'ByLayer', 'style': self.table_config.get_engineering_style()}
        ).set_placement(
            position,
            align=TextEntityAlignment.MIDDLE_CENTER
        )
        logger.info(f"Added MAIN BAR data at ({position[0]}, {position[1]})")

    def add_size_data_text(self, data_layer: str, size_text: str, position: Tuple[float, float]) -> None:
        """Add size data text at the specified position."""
        if not self.msp:
            logger.warning("No modelspace available for text drawing")
            return

        self.msp.add_text(
            size_text,
            height=self.table_config.get_table_data_text_height(),
            dxfattribs={'layer': data_layer, 'color': const.BYLAYER, 'lineweight': const.LINEWEIGHT_BYLAYER,
                        'linetype': 'ByLayer', 'style': self.table_config.get_engineering_style()}
        ).set_placement(
            position,
            align=TextEntityAlignment.MIDDLE_CENTER
        )
        logger.info(f"Added SIZE data at ({position[0]}, {position[1]})")

    def prepare_data_text(self, column_config: ColumnConfig, actual_rebar_data=None) -> Tuple[str, str]:
        """
        Prepare the text content for size and main bar data.

        Args:
            column_config: Column configuration data
            actual_rebar_data: Actual drawn rebar data (optional)

        Returns:
            Tuple containing (size_text, main_bar_text)
        """
        size_text = column_config.get_size_description()

        # Use actual rebar data if available for MAIN BAR, otherwise fall back to CSV values
        if actual_rebar_data:
            main_bar_text = self._get_actual_reinforcement_description(
                column_config, actual_rebar_data)
        else:
            main_bar_text = column_config.get_reinforcement_description()

        return size_text, main_bar_text

    def _get_actual_reinforcement_description(self, column_config: ColumnConfig, actual_rebar_data) -> str:
        """
        Generate reinforcement description using actual drawn rebar counts.
        Issues warnings if CSV values differ from what was actually drawn.

        Args:
            column_config: Original column configuration from CSV
            actual_rebar_data: RebarLayerData with actual drawn positions

        Returns:
            str: Formatted reinforcement description
        """
        try:
            # Get actual counts from drawn rebar data
            layer_1_count = len(
                actual_rebar_data.layer_1_positions) if actual_rebar_data.layer_1_positions else 0
            layer_2_count = len(
                actual_rebar_data.layer_2_positions) if actual_rebar_data.layer_2_positions else 0
            total_actual = layer_1_count + layer_2_count

            # Get expected counts from CSV
            total_expected = column_config.get_total_rebar_count()

            # Issue warning if counts don't match
            if total_actual != total_expected:
                logger.warning(f"Rebar count mismatch for {column_config.name}: "
                               f"CSV={total_expected}, Drawn={total_actual}")

            # Use actual diameter from CSV (assuming all rebars same diameter)
            diameter = column_config.layer_1_diameter if column_config.layer_1_diameter else 12

            # Format description using actual counts
            if layer_2_count > 0:
                return f"{total_actual}T{diameter} ({layer_1_count}+{layer_2_count})"
            else:
                return f"{total_actual}T{diameter}"

        except Exception as e:
            logger.error(
                f"Error generating actual reinforcement description: {e}")
            # Fall back to CSV description
            return column_config.get_reinforcement_description()

    def add_title_texts(self, text_layer: str, main_bar_position: Tuple[float, float], size_position: Tuple[float, float]) -> None:
        """
        Add both MAIN BAR and SIZE title texts.

        Args:
            text_layer: Layer name for text elements
            main_bar_position: Position for MAIN BAR text
            size_position: Position for SIZE text
        """
        self.add_main_bar_title_text(text_layer, main_bar_position)
        self.add_size_title_text(text_layer, size_position)

    def add_data_texts(self, data_layer: str, main_bar_text: str, main_bar_position: Tuple[float, float],
                       size_text: str, size_position: Tuple[float, float]) -> None:
        """
        Add both main bar and size data texts.

        Args:
            data_layer: Layer name for data elements
            main_bar_text: Text content for main bar
            main_bar_position: Position for main bar text
            size_text: Text content for size
            size_position: Position for size text
        """
        self.add_main_bar_data_text(
            data_layer, main_bar_text, main_bar_position)
        self.add_size_data_text(data_layer, size_text, size_position)
