"""
Custom Dimension Drawing Module
==============================

This module handles dimension annotations for ASD table column drawings using custom
implementations with basic LINE and TEXT entities instead of native DXF dimensions.

Provides precise dimension annotations for:
- Zone detail drawings within ZONE_*_DETAIL cells
- Column section drawings with proper scaling behavior
- Professional engineering dimension lines with stroke end marks
- Text styling and formatting optimized for technical drawings
- Scalable dimensions that maintain proportions when DXF files are scaled

Custom Implementation Features:
- Uses basic LINE and TEXT entities instead of native DXF dimension entities
- Solves scaling issues where dimension text and arrows don't scale proportionally
- All dimension elements (lines, text, tick marks) scale uniformly
- Maintains professional engineering appearance
- Compatible with all DXF viewers and CAD software
- Large, legible dimension text for engineering drawings
- Stroke end marks instead of arrowheads (per user preference)
- Proper layer assignment following AIA standards
- Backward compatible API with existing drawing systems

Authors: <AUTHORS>
Version: 2.0.0 (Custom Dimension Implementation)
Last Modified: 2024
"""

import logging
import math
from typing import Optional, Tuple
from ezdxf import const
from ezdxf.document import Drawing
from ezdxf.layouts import Modelspace
from ezdxf.enums import TextEntityAlignment
from ..models.drawing_config import DrawingConfig
from ..utils.dimension_geometry import DimensionGeometryCalculator

logger = logging.getLogger(__name__)


class CustomDimensionRenderer:
    """
    Renders custom dimensions using basic LINE and TEXT entities.

    This class creates dimension-like annotations that scale proportionally
    when the DXF file is scaled, avoiding the scaling issues of native
    DXF dimension entities.
    """

    def __init__(self, msp: Modelspace, config: DrawingConfig, dxf_writer=None):
        """
        Initialize the custom dimension renderer.

        Args:
            msp: The modelspace to draw in
            config: Drawing configuration
            dxf_writer: DXF writer with layer management (optional)
        """
        self.msp = msp
        self.config = config
        self.dxf_writer = dxf_writer
        self.geometry_calc = DimensionGeometryCalculator(config)

    def render_linear_dimension(self, p1: Tuple[float, float], p2: Tuple[float, float],
                                base: Tuple[float, float], text_override: str = None,
                                angle: float = 0, layer: str = None,
                                elevation_style: bool = False) -> bool:
        """
        Render a linear dimension using basic LINE and TEXT entities.

        Args:
            p1: First measurement point (x, y)
            p2: Second measurement point (x, y)
            base: Dimension line position (x, y)
            text_override: Custom dimension text (optional)
            angle: Dimension angle in degrees (0 for horizontal, 90 for vertical)
            layer: Layer name for dimension entities
            elevation_style: If True, adds vertical indicator lines at measurement points

        Returns:
            True if successful, False otherwise
        """
        try:
            # Calculate dimension geometry
            geometry = self.geometry_calc.calculate_linear_dimension(
                p1, p2, base, angle, text_override)

            # Get layer for dimensions
            if layer is None:
                layer = "AIS030__"
                if self.dxf_writer:
                    layer = self.dxf_writer.get_layer_for_element("dimensions")
                    self.dxf_writer.ensure_layer_exists(layer)

            # Draw dimension line
            self.msp.add_line(
                geometry.dimension_line[0],
                geometry.dimension_line[1],
                dxfattribs={
                    'color': self.config.COLOR_BLACK,
                    'lineweight': self.config.LINEWEIGHT_MEDIUM,
                    'layer': layer
                }
            )

            # Draw extension lines (skip for elevation style dimensions)
            if not elevation_style:
                for ext_line in geometry.extension_lines:
                    self.msp.add_line(
                        ext_line[0],
                        ext_line[1],
                        dxfattribs={
                            'color': self.config.COLOR_BLACK,
                            'lineweight': self.config.LINEWEIGHT_LIGHT,
                            'layer': layer
                        }
                    )

            # Draw tick marks
            for tick in geometry.tick_marks:
                self.msp.add_line(
                    tick[0],
                    tick[1],
                    dxfattribs={
                        'color': self.config.COLOR_BLACK,
                        'lineweight': self.config.LINEWEIGHT_MEDIUM,
                        'layer': layer
                    }
                )

            # Add vertical indicator lines for elevation style dimensions
            if elevation_style:
                self._add_elevation_indicator_lines(p1, p2, base, angle, layer)

            # Calculate dimension text
            if text_override:
                dim_text = text_override
            else:
                # Calculate distance between measurement points
                distance = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
                dim_text = f"{int(distance)}"

            # Handle multiline text (split on \P AutoCAD line breaks)
            self._render_dimension_text(
                dim_text,
                geometry.text_position,
                geometry.text_rotation,
                geometry.text_alignment,
                layer,
                elevation_style=elevation_style
            )

            return True

        except Exception as e:
            logger.error(f"Error rendering linear dimension: {e}")
            return False

    def render_angular_dimension(self, center: Tuple[float, float],
                                 p1: Tuple[float, float], p2: Tuple[float, float],
                                 layer: str = None) -> bool:
        """
        Render an angular dimension using basic LINE and TEXT entities.

        Args:
            center: Center point of the angle (x, y)
            p1: First point defining the angle (x, y)
            p2: Second point defining the angle (x, y)
            layer: Layer name for dimension entities

        Returns:
            True if successful, False otherwise
        """
        try:
            # Calculate dimension geometry
            geometry = self.geometry_calc.calculate_angular_dimension(
                center, p1, p2)

            # Get layer for dimensions
            if layer is None:
                layer = "AIS030__"
                if self.dxf_writer:
                    layer = self.dxf_writer.get_layer_for_element("dimensions")
                    self.dxf_writer.ensure_layer_exists(layer)

            # Draw arc as connected line segments
            for i in range(len(geometry.dimension_line) - 1):
                self.msp.add_line(
                    geometry.dimension_line[i],
                    geometry.dimension_line[i + 1],
                    dxfattribs={
                        'color': self.config.COLOR_BLACK,
                        'lineweight': self.config.LINEWEIGHT_MEDIUM,
                        'layer': layer
                    }
                )

            # Draw extension lines
            for ext_line in geometry.extension_lines:
                self.msp.add_line(
                    ext_line[0],
                    ext_line[1],
                    dxfattribs={
                        'color': self.config.COLOR_BLACK,
                        'lineweight': self.config.LINEWEIGHT_LIGHT,
                        'layer': layer
                    }
                )

            # Draw tick marks
            for tick in geometry.tick_marks:
                self.msp.add_line(
                    tick[0],
                    tick[1],
                    dxfattribs={
                        'color': self.config.COLOR_BLACK,
                        'lineweight': self.config.LINEWEIGHT_MEDIUM,
                        'layer': layer
                    }
                )

            # Calculate angle in degrees
            angle1 = math.atan2(p1[1] - center[1], p1[0] - center[0])
            angle2 = math.atan2(p2[1] - center[1], p2[0] - center[0])
            angle_diff = abs(math.degrees(angle2 - angle1))
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            # Draw dimension text
            dim_text = f"{angle_diff:.1f}°"
            self._render_dimension_text(
                dim_text,
                geometry.text_position,
                geometry.text_rotation,
                geometry.text_alignment,
                layer
            )

            return True

        except Exception as e:
            logger.error(f"Error rendering angular dimension: {e}")
            return False

    def render_radial_dimension(self, center: Tuple[float, float],
                                radius_point: Tuple[float, float],
                                layer: str = None) -> bool:
        """
        Render a radial dimension using basic LINE and TEXT entities.

        Args:
            center: Center point of the radius (x, y)
            radius_point: Point on the radius (x, y)
            layer: Layer name for dimension entities

        Returns:
            True if successful, False otherwise
        """
        try:
            # Calculate dimension geometry
            geometry = self.geometry_calc.calculate_radial_dimension(
                center, radius_point)

            # Get layer for dimensions
            if layer is None:
                layer = "AIS030__"
                if self.dxf_writer:
                    layer = self.dxf_writer.get_layer_for_element("dimensions")
                    self.dxf_writer.ensure_layer_exists(layer)

            # Draw radius line
            self.msp.add_line(
                geometry.dimension_line[0],
                geometry.dimension_line[1],
                dxfattribs={
                    'color': self.config.COLOR_BLACK,
                    'lineweight': self.config.LINEWEIGHT_MEDIUM,
                    'layer': layer
                }
            )

            # Draw tick marks
            for tick in geometry.tick_marks:
                self.msp.add_line(
                    tick[0],
                    tick[1],
                    dxfattribs={
                        'color': self.config.COLOR_BLACK,
                        'lineweight': self.config.LINEWEIGHT_MEDIUM,
                        'layer': layer
                    }
                )

            # Calculate radius
            radius = math.sqrt(
                (radius_point[0] - center[0])**2 + (radius_point[1] - center[1])**2)

            # Draw dimension text
            dim_text = f"R{radius:.1f}"
            self._render_dimension_text(
                dim_text,
                geometry.text_position,
                geometry.text_rotation,
                geometry.text_alignment,
                layer
            )

            return True

        except Exception as e:
            logger.error(f"Error rendering radial dimension: {e}")
            return False

    def _add_elevation_indicator_lines(self, p1: Tuple[float, float], p2: Tuple[float, float],
                                       base: Tuple[float, float], angle: float, layer: str) -> None:
        """
        Add vertical indicator lines at measurement points for elevation dimensions.
        Lines extend beyond their intersection with the diagonal dimension line.

        Args:
            p1: First measurement point (x, y)
            p2: Second measurement point (x, y)
            base: Dimension line position (x, y)
            angle: Dimension angle in degrees
            layer: Layer name for indicator lines
        """
        # Calculate the height of indicator lines (extending beyond intersection)
        indicator_height = 100  # Height of vertical indicator lines
        extension_beyond = 25   # Additional extension beyond intersection point

        # For horizontal dimensions (angle = 0), create vertical indicator lines
        if angle == 0:
            # Vertical lines at p1 and p2 extending beyond intersection
            # Line at p1 - from below dimension line to above
            # Start below dimension line
            line1_start = (p1[0], base[1] - extension_beyond)
            line1_end = (p1[0], base[1] + indicator_height)    # Extend upward

            # Line at p2 - from below dimension line to above
            # Start below dimension line
            line2_start = (p2[0], base[1] - extension_beyond)
            line2_end = (p2[0], base[1] + indicator_height)    # Extend upward

        elif angle == 90:
            # For vertical dimensions, create horizontal indicator lines extending beyond intersection
            # Horizontal lines at p1 and p2 extending beyond dimension line
            # Line at p1 - from before dimension line to beyond
            # Start before dimension line
            line1_start = (base[0] - extension_beyond, p1[1])
            line1_end = (base[0] + indicator_height, p1[1])    # Extend beyond

            # Line at p2 - from before dimension line to beyond
            # Start before dimension line
            line2_start = (base[0] - extension_beyond, p2[1])
            line2_end = (base[0] + indicator_height, p2[1])    # Extend beyond

        else:
            # For angled dimensions, calculate perpendicular indicator lines (one side only)
            import math
            angle_rad = math.radians(angle + 90)  # Perpendicular to dimension
            dx = math.cos(angle_rad) * indicator_height
            dy = math.sin(angle_rad) * indicator_height

            # Line at p1 - from measurement point in one direction only
            line1_start = p1  # Start at measurement point
            # Extend in one direction only
            line1_end = (p1[0] + dx, p1[1] + dy)

            # Line at p2 - from measurement point in one direction only
            line2_start = p2  # Start at measurement point
            # Extend in one direction only
            line2_end = (p2[0] + dx, p2[1] + dy)

        # Draw the indicator lines
        self.msp.add_line(
            line1_start,
            line1_end,
            dxfattribs={
                'color': self.config.COLOR_BLACK,
                'lineweight': self.config.LINEWEIGHT_LIGHT,
                'layer': layer
            }
        )

        self.msp.add_line(
            line2_start,
            line2_end,
            dxfattribs={
                'color': self.config.COLOR_BLACK,
                'lineweight': self.config.LINEWEIGHT_LIGHT,
                'layer': layer
            }
        )

    def _render_dimension_text(self, text: str, position: Tuple[float, float],
                               rotation: float, alignment: str, layer: str,
                               elevation_style: bool = False) -> None:
        """
        Render dimension text, handling multiline text by splitting on \\P.

        Args:
            text: Text to render (may contain \\P line breaks)
            position: Text position (x, y)
            rotation: Text rotation in degrees
            alignment: Text alignment string
            layer: Layer name for text entities
            elevation_style: If True, use larger line spacing for elevation dimensions
        """
        # Split text on AutoCAD line break characters
        lines = text.split("\\P")

        # Remove empty lines and strip whitespace
        lines = [line.strip() for line in lines if line.strip()]

        # For elevation dimensions, keep original order: zone identifier first, rebar specs second
        # No reversal needed since the original format already has zone identifier first

        if not lines:
            return

        # Calculate line spacing using configuration for multiple dimension lines
        # Gap between multiple dimension lines (1st row, 2nd row data)
        line_spacing = self.config.DIMENSION_LINE_SPACING

        # Calculate starting position for multiline text
        start_x = position[0]

        # Render each line
        for i, line in enumerate(lines):
            if not line:  # Skip empty lines
                continue

            # Calculate position for this line
            if elevation_style and len(lines) > 1:
                # For elevation dimensions, handle rotated text positioning
                # When text is rotated 90°, X becomes vertical position and Y becomes horizontal position
                if i == 0:
                    # First line (zone identifier) stays centered on dimension line
                    line_x = start_x
                    line_y = position[1]
                else:
                    # Second line (rebar specs) shifts RIGHT using DIMENSION_LINE_SPACING config
                    # (which puts it below when rotated 90°)
                    line_x = start_x + self.config.DIMENSION_LINE_SPACING
                    line_y = position[1]
                line_position = (line_x, line_y)
            else:
                # Standard positioning for regular dimensions
                if len(lines) > 1:
                    # Center the text block vertically around the dimension line position
                    total_height = (len(lines) - 1) * line_spacing
                    start_y = position[1] + total_height / 2
                    line_y = start_y - (i * line_spacing)
                else:
                    line_y = position[1]
                line_position = (start_x, line_y)

            # Create text entity
            text_entity = self.msp.add_text(
                line,
                height=self.config.DIMENSION_TEXT_HEIGHT,
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer',
                    'style': 'Standard',
                    'layer': layer
                },
                rotation=rotation
            )

            # Set text alignment
            alignment_enum = getattr(TextEntityAlignment, alignment)
            text_entity.set_placement(line_position, align=alignment_enum)


class DimensionDrawer:
    """
    Handles dimension annotations for ASD table column drawings with AIA layer management.

    Provides professional dimension annotations for zone detail drawings within ZONE_*_DETAIL cells,
    column sections, and other technical elements. Uses stroke end marks instead of arrowheads
    per user preference, with large legible text for engineering drawings.
    """

    def __init__(self, doc: Drawing, msp: Modelspace, config: DrawingConfig, dxf_writer=None):
        """
        Initialize the dimension drawer with layer management

        Args:
            doc: The DXF document
            msp: The modelspace to draw in
            config: Drawing configuration
            dxf_writer: DXF writer with layer management (optional for backward compatibility)
        """
        self.doc = doc
        self.msp = msp
        self.config = config
        self.dxf_writer = dxf_writer
        self._dimension_style_created = False
        self._text_styles_created = False

        # Initialize custom dimension renderer
        self.custom_renderer = CustomDimensionRenderer(msp, config, dxf_writer)

    def setup_dimension_style(self) -> Optional[str]:
        """
        Create and configure professional engineering dimension style

        Returns:
            Name of the created dimension style or None if failed
        """
        try:
            if self._dimension_style_created:
                return 'Standard'

            # Create engineering dimension style if it doesn't exist
            if 'Standard' not in self.doc.dimstyles:
                dimstyle = self.doc.dimstyles.new('Standard')

                # Set basic properties for engineering drawings
                dimstyle.dxf.dimtxt = float(
                    self.config.DIMENSION_TEXT_HEIGHT)  # Use config value
                dimstyle.dxf.dimasz = float(
                    self.config.DIMENSION_ARROW_SIZE)   # Use config value
                # Tick size for strokes (user prefers strokes over arrows)
                dimstyle.dxf.dimtsz = 60.0
                dimstyle.dxf.dimblk = ""          # No arrow blocks (use ticks)
                dimstyle.dxf.dimtad = 1           # Text above dimension line
                dimstyle.dxf.dimgap = float(
                    self.config.DIMENSION_GAP)        # Use config value
                dimstyle.dxf.dimexe = float(
                    self.config.DIMENSION_EXTENSION_LENGTH)        # Use config value
                dimstyle.dxf.dimexo = float(
                    self.config.DIMENSION_OFFSET)        # Use config value
                dimstyle.dxf.dimclrt = self.config.COLOR_BLACK  # Text color
                dimstyle.dxf.dimclrd = self.config.COLOR_BLACK  # Dimension line color
                dimstyle.dxf.dimclre = self.config.COLOR_BLACK  # Extension line color
                dimstyle.dxf.dimtxsty = 'Standard'  # Text style

                # Set precision and units
                dimstyle.dxf.dimdec = 0           # No decimal places for integer dimensions
                dimstyle.dxf.dimzin = 8           # Suppress trailing zeros
                dimstyle.dxf.dimlfac = 1.0        # Linear factor (1:1 scale)

                logger.debug(
                    "Created Standard dimension style with stroke end marks")

            self._dimension_style_created = True
            return 'Standard'

        except Exception as e:
            logger.error(f"Error setting up dimension style: {e}")
            return self.doc.dimstyles.get('STANDARD').dxf.name if 'STANDARD' in self.doc.dimstyles else None

    def setup_text_styles(self) -> bool:
        """
        Setup Arial Narrow Standard text style for dimensions

        Returns:
            True if successful, False otherwise
        """
        try:
            if self._text_styles_created:
                return True

            # Create only the Standard text style with Arial Narrow font
            if 'Standard' not in self.doc.styles:
                text_style = self.doc.styles.new('Standard')
                # Arial Narrow for professional appearance
                text_style.dxf.font = self.config.FONT_ENGINEERING
                # 0.9 for optimal readability
                text_style.dxf.width = self.config.TEXT_STYLE_WIDTH_FACTOR
                text_style.dxf.height = 0.0                                 # Variable height
                text_style.dxf.oblique = self.config.TEXT_STYLE_OBLIQUE_ANGLE  # 0.0 for straight text

                logger.debug(
                    f"Created Standard text style for dimensions with Arial Narrow: {self.config.FONT_ENGINEERING}")

            self._text_styles_created = True
            return True

        except Exception as e:
            logger.error(f"Error setting up Arial Narrow text styles: {e}")
            return False

    def draw_section_dimensions(self, x: float, y: float, width: float, height: float,
                                B: float, D: float) -> bool:
        """
        Draw AutoCAD dimension annotations for column sections within ZONE_*_DETAIL cells.

        Creates professional dimension annotations with stroke end marks (user preference)
        and large, legible text positioned outside the column section. Dimensions show
        actual column sizes in millimeters for engineering accuracy.

        Args:
            x: Section X coordinate within the zone detail cell
            y: Section Y coordinate within the zone detail cell
            width: Scaled section width for display
            height: Scaled section height for display
            B: Actual column width in millimeters
            D: Actual column depth in millimeters

        Returns:
            True if dimensions were successfully created, False otherwise
        """
        try:
            # Setup text styles for custom dimensions
            self.setup_text_styles()

            # Get appropriate layer for dimensions
            dimension_layer = "AIS030__"
            if self.dxf_writer:
                dimension_layer = self.dxf_writer.get_layer_for_element(
                    "dimensions")
                self.dxf_writer.ensure_layer_exists(dimension_layer)

            # Width dimension (horizontal) - above column
            width_success = self.custom_renderer.render_linear_dimension(
                # First measurement point (top-left)
                p1=(x, y + height),
                # Second measurement point (top-right)
                p2=(x + width, y + height),
                # Dimension line location (above column)
                base=(x + width/2, y + height + 200),
                # Show actual column width in mm
                text_override=f"{int(B)}",
                angle=0,                               # Horizontal dimension
                layer=dimension_layer
            )

            if not width_success:
                logger.warning("Failed to create width dimension")

            # Height dimension (vertical) - left side of column
            height_success = self.custom_renderer.render_linear_dimension(
                # First measurement point (bottom-left)
                p1=(x, y),
                # Second measurement point (top-left)
                p2=(x, y + height),
                # Dimension line location (left of column)
                base=(x - 200, y + height/2),
                # Show actual column depth in mm
                text_override=f"{int(D)}",
                angle=90,                              # Vertical dimension
                layer=dimension_layer
            )

            if not height_success:
                logger.warning("Failed to create height dimension")

            success = width_success and height_success
            if success:
                logger.debug(
                    f"Added custom dimension annotations: {B}mm x {D}mm")
            else:
                logger.warning(
                    "Some dimensions failed, falling back to simple dimensions")
                return self.draw_simple_section_dimensions(x, y, width, height, B, D)

            return success

        except Exception as e:
            logger.error(f"Error drawing custom dimensions: {e}")
            # Fallback to simple dimensions if custom dimensions fail
            return self.draw_simple_section_dimensions(x, y, width, height, B, D)

    def draw_simple_section_dimensions(self, x: float, y: float, width: float, height: float,
                                       B: float, D: float) -> bool:
        """
        Draw clean, professional dimension lines as fallback

        Args:
            x: Section X coordinate
            y: Section Y coordinate
            width: Scaled section width
            height: Scaled section height
            B: Actual column width (mm)
            D: Actual column depth (mm)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure text styles are setup
            self.setup_text_styles()

            # Width dimension (top) - moved to top of column
            dim_y = y + height + 150

            # Extension lines
            self.msp.add_line(
                (x, y + height + 10),
                (x, dim_y + 20),
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )
            self.msp.add_line(
                (x + width, y + height + 10),
                (x + width, dim_y + 20),
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )

            # Dimension line
            self.msp.add_line(
                (x, dim_y),
                (x + width, dim_y),
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )

            # End marks (strokes instead of arrowheads per user preference)
            mark_size = 20
            self.msp.add_line(
                (x, dim_y - mark_size/2),
                (x, dim_y + mark_size/2),
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )
            self.msp.add_line(
                (x + width, dim_y - mark_size/2),
                (x + width, dim_y + mark_size/2),
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )

            # Width dimension text (large enough to be clearly legible)
            logger.debug(
                f"Using DIMENSION_TEXT_HEIGHT: {self.config.DIMENSION_TEXT_HEIGHT}")
            self.msp.add_text(
                f"{int(B)}",
                height=self.config.DIMENSION_TEXT_HEIGHT,  # Use config value
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer',
                    'style': 'Standard'
                }
            ).set_placement((x + width/2, dim_y + 40), align=TextEntityAlignment.MIDDLE_CENTER)

            # Height dimension (left) - moved to left side of column
            dim_x = x - 150

            # Extension lines
            self.msp.add_line(
                (x - 10, y),
                (dim_x - 20, y),
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )
            self.msp.add_line(
                (x - 10, y + height),
                (dim_x - 20, y + height),
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )

            # Dimension line
            self.msp.add_line(
                (dim_x, y),
                (dim_x, y + height),
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )

            # End marks (strokes instead of arrowheads per user preference)
            self.msp.add_line(
                (dim_x - mark_size/2, y),
                (dim_x + mark_size/2, y),
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )
            self.msp.add_line(
                (dim_x - mark_size/2, y + height),
                (dim_x + mark_size/2, y + height),
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )

            # Height dimension text (large enough to be clearly legible)
            self.msp.add_text(
                f"{int(D)}",
                height=self.config.DIMENSION_TEXT_HEIGHT,  # Use config value
                dxfattribs={
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer',
                    'style': 'Standard'
                },
                rotation=90
            ).set_placement((dim_x - 40, y + height/2), align=TextEntityAlignment.MIDDLE_CENTER)

            logger.debug(f"Drew simple dimension annotations: {B}mm x {D}mm")
            return True

        except Exception as e:
            logger.error(f"Error drawing simple section dimensions: {e}")
            return False

    def draw_linear_dimension(self, p1: Tuple[float, float], p2: Tuple[float, float],
                              base: Tuple[float, float], text_override: str = None,
                              angle: float = 0) -> bool:
        """
        Draw a linear dimension between two points

        Args:
            p1: First measurement point (x, y)
            p2: Second measurement point (x, y)
            base: Dimension line position (x, y)
            text_override: Custom dimension text (optional)
            angle: Dimension angle in degrees (0 for horizontal, 90 for vertical)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Setup text styles for custom dimensions
            self.setup_text_styles()

            # Get appropriate layer for dimensions
            dimension_layer = "AIS030__"
            if self.dxf_writer:
                dimension_layer = self.dxf_writer.get_layer_for_element(
                    "dimensions")
                self.dxf_writer.ensure_layer_exists(dimension_layer)

            # Use custom dimension renderer
            success = self.custom_renderer.render_linear_dimension(
                p1=p1,
                p2=p2,
                base=base,
                text_override=text_override,
                angle=angle,
                layer=dimension_layer
            )

            if success:
                logger.debug(f"Successfully drew custom linear dimension")
            else:
                logger.warning(f"Failed to draw custom linear dimension")

            return success

        except Exception as e:
            logger.error(f"Error drawing linear dimension: {e}")
            return False

    def draw_elevation_dimension(self, p1: Tuple[float, float], p2: Tuple[float, float],
                                 base: Tuple[float, float], text_override: str = None,
                                 angle: float = 0) -> bool:
        """
        Draw an elevation dimension with vertical indicator lines and proper multiline text spacing.

        Args:
            p1: First measurement point (x, y)
            p2: Second measurement point (x, y)
            base: Dimension line position (x, y)
            text_override: Custom dimension text (optional)
            angle: Dimension angle in degrees (0 for horizontal, 90 for vertical)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Setup text styles for custom dimensions
            self.setup_text_styles()

            # Get appropriate layer for dimensions
            dimension_layer = "AIS030__"
            if self.dxf_writer:
                dimension_layer = self.dxf_writer.get_layer_for_element(
                    "dimensions")
                self.dxf_writer.ensure_layer_exists(dimension_layer)

            # Use custom dimension renderer with elevation style
            success = self.custom_renderer.render_linear_dimension(
                p1=p1,
                p2=p2,
                base=base,
                text_override=text_override,
                angle=angle,
                layer=dimension_layer,
                elevation_style=True  # Enable elevation-specific features
            )

            if success:
                logger.debug(f"Successfully drew elevation dimension")
            else:
                logger.warning(f"Failed to draw elevation dimension")

            return success

        except Exception as e:
            logger.error(f"Error drawing elevation dimension: {e}")
            return False

    def draw_angular_dimension(self, center: Tuple[float, float], p1: Tuple[float, float],
                               p2: Tuple[float, float], radius: float) -> bool:
        """
        Draw an angular dimension

        Args:
            center: Center point of the angle (x, y)
            p1: First angle point (x, y)
            p2: Second angle point (x, y)
            radius: Dimension arc radius

        Returns:
            True if successful, False otherwise
        """
        try:
            # Setup text styles for custom dimensions
            self.setup_text_styles()

            # Get appropriate layer for dimensions
            dimension_layer = "AIS030__"
            if self.dxf_writer:
                dimension_layer = self.dxf_writer.get_layer_for_element(
                    "dimensions")
                self.dxf_writer.ensure_layer_exists(dimension_layer)

            # Use custom dimension renderer
            success = self.custom_renderer.render_angular_dimension(
                center=center,
                p1=p1,
                p2=p2,
                layer=dimension_layer
            )

            if success:
                logger.debug(f"Successfully drew custom angular dimension")
            else:
                logger.warning(f"Failed to draw custom angular dimension")

            return success

        except Exception as e:
            logger.error(f"Error drawing angular dimension: {e}")
            return False

    def draw_radial_dimension(self, center: Tuple[float, float], radius_point: Tuple[float, float]) -> bool:
        """
        Draw a radial dimension

        Args:
            center: Center point of the radius (x, y)
            radius_point: Point on the radius (x, y)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Setup text styles for custom dimensions
            self.setup_text_styles()

            # Get appropriate layer for dimensions
            dimension_layer = "AIS030__"
            if self.dxf_writer:
                dimension_layer = self.dxf_writer.get_layer_for_element(
                    "dimensions")
                self.dxf_writer.ensure_layer_exists(dimension_layer)

            # Use custom dimension renderer
            success = self.custom_renderer.render_radial_dimension(
                center=center,
                radius_point=radius_point,
                layer=dimension_layer
            )

            if success:
                logger.debug(f"Successfully drew custom radial dimension")
            else:
                logger.warning(f"Failed to draw custom radial dimension")

            return success

        except Exception as e:
            logger.error(f"Error drawing radial dimension: {e}")
            return False
