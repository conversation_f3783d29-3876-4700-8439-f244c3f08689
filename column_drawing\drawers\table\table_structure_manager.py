"""
Table Structure Manager
======================

Table structure drawing management for table drawing in the Drawing-Production application.
This module handles all table structure drawing operations including line drawing,
border management, and coordinate calculations.

Key Features:
- Table structure line drawing with intelligent filtering
- Border line management for horizontal table layouts
- Layer management for table structure elements
- Coordinate transformation and bounds calculation
- Professional table structure rendering with AIA layer standards

This module was extracted from TableDrawer to improve maintainability and
reduce the main class size while preserving all functionality.
"""

import logging
from typing import Tuple, Optional
from ezdxf import const
from ...models.table_config import TableConfig
from ...io.dxf_writer import DXFWriter
from .layer_manager_mixin import LayerManagerMixin

logger = logging.getLogger(__name__)


class TableStructureManager(LayerManagerMixin):
    """
    Manages all table structure drawing operations.

    This class handles table line drawing, border management, and coordinate
    calculations for table structures with proper layer management.
    """

    def __init__(self, table_config: TableConfig, dxf_writer: Optional[DXFWriter] = None,
                 modelspace=None, config=None):
        """
        Initialize the table structure manager.

        Args:
            table_config: Table configuration instance
            dxf_writer: DXF writer with layer management (optional)
            modelspace: DXF modelspace for drawing operations (optional)
            config: Drawing configuration object (optional)
        """
        super().__init__(table_config, dxf_writer)
        self.msp = modelspace
        self.config = config

    def draw_table_structure(self, origin_x: float, origin_y: float, table_index: int = 0, is_first_in_group: bool = True) -> Tuple[float, float, float, float]:
        """
        Draw the table structure using precise coordinates with intelligent line management.
        Prevents duplicate lines when tables are arranged horizontally.

        Args:
            origin_x: Table origin X coordinate
            origin_y: Table origin Y coordinate
            table_index: Index of this table in the horizontal group (0-based)
            is_first_in_group: Whether this is the first table in a horizontal group

        Returns:
            Tuple[float, float, float, float]: Table bounds (x, y, width, height)
        """
        try:
            logger.debug(
                f"Drawing table structure at origin ({origin_x}, {origin_y}), index={table_index}, first={is_first_in_group}")

            # Get table coordinate lines
            table_coordinates = self.config.get_table_coordinates()

            # Get appropriate layer for table borders
            table_layer = self._get_table_border_layer()

            # Filter lines to prevent duplicates in horizontal arrangements
            lines_to_draw = self._filter_table_lines_for_layout(
                table_coordinates, table_index, is_first_in_group
            )

            # Draw filtered lines
            self._draw_table_lines(
                lines_to_draw, origin_x, origin_y, table_layer)

            # Return table bounds
            table_bounds = self._calculate_table_bounds(origin_x, origin_y)

            logger.debug(
                f"Drew {len(lines_to_draw)} table lines (filtered from {len(table_coordinates)} total)")
            return table_bounds

        except Exception as e:
            logger.error(f"Error drawing table structure: {e}")
            raise

    def _get_table_border_layer(self) -> str:
        """Get the appropriate layer for table borders."""
        return self.get_border_layer()

    def _filter_table_lines_for_layout(self, table_coordinates, table_index: int, is_first_in_group: bool):
        """
        Filter table coordinate lines to prevent duplicates in horizontal table arrangements.

        Args:
            table_coordinates: List of line coordinates
            table_index: Index of this table in the horizontal group (0-based)
            is_first_in_group: Whether this is the first table in a horizontal group

        Returns:
            List of filtered line coordinates to draw
        """
        # No filtering needed - tables are properly spaced and don't overlap
        # Table 1: X=0 to 6500, Table 2: X=7000 to 13500 (with 500mm spacing)
        # Each table's left border is at its own position and doesn't conflict

        logger.debug(
            f"Table {table_index}: Drawing all {len(table_coordinates)} lines (no filtering needed)")
        return table_coordinates

    def _draw_table_lines(self, lines_to_draw, origin_x: float, origin_y: float, table_layer: str) -> None:
        """
        Draw the filtered table lines.

        Args:
            lines_to_draw: List of line coordinates to draw
            origin_x: Table origin X coordinate
            origin_y: Table origin Y coordinate (bottom-left corner)
            table_layer: Layer name for table lines
        """
        # COORDINATE SYSTEM FIX:
        # DrawingSpaceOrganizer passes origin_y as bottom-left corner
        # But table structure coordinates are defined relative to top-left corner
        # Convert: top-left Y = bottom-left Y + table height
        table_height = self.table_config.get_table_height()
        table_top_left_y = origin_y + table_height

        logger.debug(
            f"Table structure coordinate conversion: bottom-left Y={origin_y:.0f} -> top-left Y={table_top_left_y:.0f}")

        for i, (start, end) in enumerate(lines_to_draw):
            start_2d = (origin_x + start[0], table_top_left_y + start[1])
            end_2d = (origin_x + end[0], table_top_left_y + end[1])

            self.msp.add_line(
                start_2d,
                end_2d,
                dxfattribs={
                    'layer': table_layer,
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )

    def _calculate_table_bounds(self, origin_x: float, origin_y: float) -> Tuple[float, float, float, float]:
        """
        Calculate the bounds of the drawn table.

        Args:
            origin_x: Table origin X coordinate
            origin_y: Table origin Y coordinate

        Returns:
            Tuple containing table bounds (x, y, width, height)
        """
        return (
            origin_x,
            origin_y,
            self.table_config.get_table_width(),
            self.table_config.get_table_height()
        )

    def debug_table_coordinates(self, origin_x: float, origin_y: float, table_index: int = 0) -> None:
        """
        Debug method to log table coordinate information for troubleshooting.

        Args:
            origin_x: Table origin X coordinate
            origin_y: Table origin Y coordinate  
            table_index: Index of this table in horizontal group
        """
        try:
            table_coordinates = self.config.get_table_coordinates()

            # Only log if there are issues with table structure
            if len(table_coordinates) == 0:
                logger.warning(
                    f"Table {table_index}: No coordinate lines found")
            elif len(table_coordinates) < 10:  # Minimum expected lines for a table
                logger.warning(
                    f"Table {table_index}: Only {len(table_coordinates)} coordinate lines (may be incomplete)")

        except Exception as e:
            logger.error(f"Error in table coordinate debugging: {e}")

    def validate_table_structure(self, origin_x: float, origin_y: float) -> bool:
        """
        Validate that the table structure can be drawn correctly.

        Args:
            origin_x: Table origin X coordinate
            origin_y: Table origin Y coordinate

        Returns:
            bool: True if structure is valid, False otherwise
        """
        try:
            # Check if coordinates are available
            if not self.config:
                logger.warning(
                    "No drawing configuration available for table structure validation")
                return False

            table_coordinates = self.config.get_table_coordinates()
            if not table_coordinates:
                logger.warning("No table coordinates available")
                return False

            # Check if origin is reasonable
            if origin_x < 0 or origin_y < 0:
                logger.warning(
                    f"Negative origin coordinates: ({origin_x}, {origin_y})")
                return False

            # Check table dimensions
            table_width = self.table_config.get_table_width()
            table_height = self.table_config.get_table_height()

            if table_width <= 0 or table_height <= 0:
                logger.warning(
                    f"Invalid table dimensions: {table_width} x {table_height}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating table structure: {e}")
            return False
