"""
Unit Tests for Parsers
======================

Tests for file parsing and validation components.

Author: Drawing Production System
"""

import unittest
import tempfile
import os
from pathlib import Path
import pandas as pd

from ..parsers.coordinate_parser import CoordinateParser, CoordinateParsingError, ParsedCoordinate
from ..parsers.validation import ValidationResult, ValidationLevel, FileValidator, DataValidator
from ..parsers.excel_parser import ExcelParser, ExcelParsingError
from ..models.boundary import BoundaryPoint


class TestCoordinateParser(unittest.TestCase):
    """Test CoordinateParser class."""
    
    def setUp(self):
        """Set up test parser."""
        self.parser = CoordinateParser()
        self.strict_parser = CoordinateParser(strict_mode=True)
    
    def test_standard_format_parsing(self):
        """Test parsing standard coordinate format."""
        coord = self.parser.parse_coordinate("(100, 200, 0)")
        self.assertEqual(coord.x, 100.0)
        self.assertEqual(coord.y, 200.0)
        self.assertEqual(coord.z, 0.0)
        self.assertEqual(coord.original_string, "(100, 200, 0)")
    
    def test_various_formats(self):
        """Test parsing various coordinate formats."""
        test_cases = [
            "(100, 200, 0)",
            "(100.5, -200.5, 0.0)",
            "100, 200, 0",
            "100 200 0",
            "100\t200\t0"
        ]
        
        for coord_str in test_cases:
            with self.subTest(coord_str=coord_str):
                coord = self.parser.parse_coordinate(coord_str)
                self.assertIsInstance(coord, ParsedCoordinate)
    
    def test_strict_mode(self):
        """Test strict mode parsing."""
        # Should work in strict mode
        coord = self.strict_parser.parse_coordinate("(100, 200, 0)")
        self.assertEqual(coord.x, 100.0)
        
        # Should fail in strict mode
        with self.assertRaises(CoordinateParsingError):
            self.strict_parser.parse_coordinate("100, 200, 0")
    
    def test_invalid_coordinates(self):
        """Test invalid coordinate handling."""
        invalid_coords = [
            "",
            "invalid",
            "(100, 200)",  # Missing z
            "(a, b, c)",   # Non-numeric
            "(100, 200, 0, 1)"  # Too many values
        ]
        
        for coord_str in invalid_coords:
            with self.subTest(coord_str=coord_str):
                with self.assertRaises(CoordinateParsingError):
                    self.parser.parse_coordinate(coord_str)
    
    def test_precision_detection(self):
        """Test precision detection."""
        coord = self.parser.parse_coordinate("(100.123, 200.45, 0.0)")
        self.assertEqual(coord.precision, 3)  # Maximum precision from all coordinates
    
    def test_coordinate_list_parsing(self):
        """Test parsing list of coordinates."""
        coord_strings = ["(0, 0, 0)", "(100, 0, 0)", "(100, 50, 0)", "(0, 50, 0)"]
        coords = self.parser.parse_coordinate_list(coord_strings)
        
        self.assertEqual(len(coords), 4)
        self.assertEqual(coords[0].x, 0.0)
        self.assertEqual(coords[1].x, 100.0)
    
    def test_coordinate_formatting(self):
        """Test coordinate formatting."""
        formatted = self.parser.format_coordinate(100.123, 200.456, 0.0, precision=2)
        self.assertEqual(formatted, "(100.12, 200.46, 0.00)")


class TestValidation(unittest.TestCase):
    """Test validation components."""
    
    def test_validation_result(self):
        """Test ValidationResult class."""
        result = ValidationResult()
        self.assertTrue(result.is_valid)
        self.assertEqual(len(result.messages), 0)
        
        result.add_error("Test error", "test_field", "test_value")
        self.assertFalse(result.is_valid)
        self.assertEqual(len(result.errors), 1)
        
        result.add_warning("Test warning")
        self.assertEqual(len(result.warnings), 1)
    
    def test_validation_result_merge(self):
        """Test merging validation results."""
        result1 = ValidationResult()
        result1.add_error("Error 1")
        
        result2 = ValidationResult()
        result2.add_warning("Warning 1")
        
        result1.merge(result2)
        self.assertEqual(len(result1.errors), 1)
        self.assertEqual(len(result1.warnings), 1)
    
    def test_file_validator(self):
        """Test FileValidator class."""
        # Test non-existent file
        result = FileValidator.validate_file_exists("nonexistent_file.txt")
        self.assertFalse(result.is_valid)
        self.assertTrue(any("does not exist" in msg.message for msg in result.errors))
        
        # Test with temporary file
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as tmp:
            tmp.write(b"test content")
            tmp_path = tmp.name
        
        try:
            result = FileValidator.validate_file_exists(tmp_path)
            self.assertTrue(result.is_valid)
        finally:
            os.unlink(tmp_path)
    
    def test_data_validator(self):
        """Test DataValidator class."""
        validator = DataValidator()
        
        # Test Excel structure validation
        df = pd.DataFrame({
            'Title': ['Note 1', 'Note 2'],
            'Point1': ['(0, 0, 0)', '(100, 0, 0)'],
            'Point2': ['(50, 0, 0)', '(150, 0, 0)'],
            'Point3': ['(50, 25, 0)', '(150, 25, 0)'],
            'Point4': ['(0, 25, 0)', '(100, 25, 0)']
        })
        
        required_columns = ['Title', 'Point1', 'Point2', 'Point3', 'Point4']
        result = validator.validate_excel_structure(df, required_columns)
        self.assertTrue(result.is_valid)
        
        # Test missing columns
        df_missing = pd.DataFrame({'Title': ['Note 1']})
        result = validator.validate_excel_structure(df_missing, required_columns)
        self.assertFalse(result.is_valid)


class TestExcelParser(unittest.TestCase):
    """Test ExcelParser class."""
    
    def setUp(self):
        """Set up test parser and data."""
        self.parser = ExcelParser()
        
        # Create test Excel data
        self.test_data = {
            'Title': ['Test Note 1', 'Test Note 2'],
            'Point1': ['(0, 0, 0)', '(100, 0, 0)'],
            'Point2': ['(50, 0, 0)', '(150, 0, 0)'],
            'Point3': ['(50, 25, 0)', '(150, 25, 0)'],
            'Point4': ['(0, 25, 0)', '(100, 25, 0)'],
            'Description': ['First test note', 'Second test note'],
            'Category': ['Category A', 'Category B']
        }
    
    def test_excel_parsing_with_temp_file(self):
        """Test Excel parsing with temporary file."""
        # Create temporary Excel file
        df = pd.DataFrame(self.test_data)
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            # Write Excel file
            df.to_excel(tmp_path, sheet_name='Detail', index=False)
            
            # Parse the file
            collection = self.parser.parse_excel_file(tmp_path)
            
            self.assertEqual(len(collection), 2)
            self.assertEqual(collection[0].title, 'Test Note 1')
            self.assertEqual(collection[0].description, 'First test note')
            self.assertEqual(collection[0].category, 'Category A')
            
        except Exception as e:
            self.fail(f"Excel parsing failed: {str(e)}")
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
    
    def test_coordinate_parsing_in_excel(self):
        """Test coordinate parsing within Excel context."""
        # Test coordinate parsing
        row_data = {
            'Title': 'Test Note',
            'Point1': '(0, 0, 0)',
            'Point2': '(100, 0, 0)',
            'Point3': '(100, 50, 0)',
            'Point4': '(0, 50, 0)'
        }
        
        coords = self.parser._parse_row_coordinates(pd.Series(row_data), 0)
        self.assertEqual(len(coords), 4)
        self.assertIsInstance(coords[0], BoundaryPoint)
        self.assertEqual(coords[0].x, 0.0)
        self.assertEqual(coords[1].x, 100.0)
    
    def test_invalid_excel_data(self):
        """Test handling of invalid Excel data."""
        # Test empty title
        invalid_data = self.test_data.copy()
        invalid_data['Title'][0] = ''
        
        df = pd.DataFrame(invalid_data)
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            df.to_excel(tmp_path, sheet_name='Detail', index=False)
            
            # Should handle invalid data gracefully
            collection = self.parser.parse_excel_file(tmp_path)
            # Should skip the invalid row
            self.assertEqual(len(collection), 1)
            
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
    
    def test_missing_sheet(self):
        """Test handling of missing Excel sheet."""
        df = pd.DataFrame(self.test_data)
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            # Write to different sheet name
            df.to_excel(tmp_path, sheet_name='WrongSheet', index=False)
            
            # Should raise error for missing sheet
            with self.assertRaises(ExcelParsingError):
                self.parser.parse_excel_file(tmp_path)
                
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)


if __name__ == '__main__':
    unittest.main()
