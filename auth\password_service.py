"""
Password Service Module
=====================

This module provides password generation and validation functionality for the authentication system.
Handles secure password generation, hashing, and validation with salt-based security.

Features:
    - Secure random password generation
    - Salt-based password hashing
    - Password validation with timing attack resistance
    - Multiple character set requirements

Author: Drawing Production System
Date: 2025
"""

import hashlib
import random
import secrets
import string
import logging
from typing import <PERSON>ple


def generate_password_key(length: int = 30) -> str:
    """
    Generate a secure random password of specified length.

    Args:
        length: Length of password to generate (default: 30)

    Returns:
        String containing the secure password
    """
    # Define character sets for password
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = "!@#$%^&*()-_=+"
    
    # Ensure at least one of each type is included
    password = [
        random.choice(lowercase),
        random.choice(uppercase),
        random.choice(digits),
        random.choice(special_chars)
    ]
    
    # Fill the rest randomly
    all_chars = lowercase + uppercase + digits + special_chars
    remaining_length = length - len(password)
    password.extend(random.choice(all_chars) for _ in range(remaining_length))
    
    # Shuffle the password to make it more secure
    random.shuffle(password)
    return ''.join(password)


def generate_password_with_salt() -> Tuple[str, str, str]:
    """
    Generate a secure password with salt and hash.
    
    Returns:
        Tuple containing (plain_password, salt, password_hash)
    """
    # Generate the password
    plain_password = generate_password_key()
    password_salt = secrets.token_hex(16)  # Generate random salt

    # Hash password with salt
    password_with_salt = plain_password + password_salt
    password_hash = hashlib.sha256(password_with_salt.encode()).hexdigest()
    
    return plain_password, password_salt, password_hash


def validate_password(password: str, password_salt: str, stored_hash: str) -> bool:
    """
    Validate a password against stored hash and salt.
    
    Args:
        password: The password to validate
        password_salt: The salt used for hashing
        stored_hash: The stored password hash
        
    Returns:
        Boolean indicating if password is valid
    """
    try:
        # Hash input password with stored salt
        input_password_with_salt = password + password_salt
        input_password_hash = hashlib.sha256(input_password_with_salt.encode()).hexdigest()

        # Compare hashes using timing-attack resistant comparison
        return secrets.compare_digest(input_password_hash, stored_hash)
    except Exception as e:
        logging.error(f"Password validation error: {e}")
        return False


def clear_password_data(password_hash: str, password_salt: str) -> Tuple[str, str]:
    """
    Securely clear password data from memory.
    
    Args:
        password_hash: The password hash to clear
        password_salt: The password salt to clear
        
    Returns:
        Tuple of empty strings
    """
    # Overwrite the original strings with empty values
    password_hash = ""
    password_salt = ""
    return password_hash, password_salt