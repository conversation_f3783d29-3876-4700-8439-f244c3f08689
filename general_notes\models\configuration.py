"""
Configuration Models
===================

Configuration classes for the General Notes Drawing Generator using dataclass patterns.

This module provides configuration classes for:
- Main application configuration
- Database and file path configuration  
- Layout and drawing space configuration
- Output generation configuration

Author: Drawing Production System
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """
    Configuration for database and file paths.
    
    Attributes:
        default_database_path: Default path to DXF database file
        default_summary_path: Default path to Excel summary file
        default_output_directory: Default directory for output files
        backup_enabled: Whether to create backup files
        backup_directory: Directory for backup files
        file_validation_enabled: Whether to validate file formats
    """
    default_database_path: str = "C:\\Users\\<USER>\\OneDrive - Asia Infrastructure Solutions Limited\\RPA Documentation\\Drafting Agent\\General Notes\\General Notes Database.dxf"
    default_summary_path: str = "C:\\Users\\<USER>\\OneDrive - Asia Infrastructure Solutions Limited\\RPA Documentation\\Drafting Agent\\General Notes\\General Notes Summary.xlsx"
    default_output_directory: str = "output"
    backup_enabled: bool = True
    backup_directory: str = "backup"
    file_validation_enabled: bool = True
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        self._validate_paths()
    
    def _validate_paths(self) -> None:
        """Validate that configured paths are reasonable."""
        # Validate database path
        if self.default_database_path:
            db_path = Path(self.default_database_path)
            if not db_path.suffix.lower() == '.dxf':
                logger.warning(f"Database path does not have .dxf extension: {self.default_database_path}")
        
        # Validate summary path
        if self.default_summary_path:
            summary_path = Path(self.default_summary_path)
            if not summary_path.suffix.lower() in ['.xlsx', '.xls']:
                logger.warning(f"Summary path does not have Excel extension: {self.default_summary_path}")
    
    def get_database_path(self) -> Path:
        """Get database path as Path object."""
        return Path(self.default_database_path)
    
    def get_summary_path(self) -> Path:
        """Get summary path as Path object."""
        return Path(self.default_summary_path)
    
    def get_output_directory(self) -> Path:
        """Get output directory as Path object."""
        return Path(self.default_output_directory)
    
    def get_backup_directory(self) -> Path:
        """Get backup directory as Path object."""
        return Path(self.backup_directory)


@dataclass
class LayoutConfig:
    """
    Configuration for layout and drawing space arrangement.
    
    Attributes:
        drawing_space_width: Width of each drawing space in units
        drawing_space_height: Height of each drawing space in units
        module_width: Width of each module within a drawing space
        module_height: Height of each module within a drawing space
        modules_per_row: Number of modules per row in drawing space
        modules_per_column: Number of modules per column in drawing space
        spacing_between_modules: Spacing between modules
        spacing_between_spaces: Spacing between drawing spaces
        max_modules_per_space: Maximum modules per drawing space
        layout_optimization_enabled: Whether to optimize layout arrangement
    """
    drawing_space_width: float = 67600.0
    drawing_space_height: float = 56400.0
    module_width: float = 16900.0
    module_height: float = 14100.0
    modules_per_row: int = 2
    modules_per_column: int = 2
    spacing_between_modules: float = 0.0
    spacing_between_spaces: float = 5000.0
    max_modules_per_space: int = 4
    layout_optimization_enabled: bool = True
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        self._validate_dimensions()
        self._validate_module_arrangement()
    
    def _validate_dimensions(self) -> None:
        """Validate dimension values."""
        dimensions = [
            ('drawing_space_width', self.drawing_space_width),
            ('drawing_space_height', self.drawing_space_height),
            ('module_width', self.module_width),
            ('module_height', self.module_height)
        ]
        
        for name, value in dimensions:
            if value <= 0:
                raise ValueError(f"{name} must be positive, got {value}")
            if value > 1e6:  # 1 million units
                logger.warning(f"{name} is very large: {value}")
    
    def _validate_module_arrangement(self) -> None:
        """Validate module arrangement configuration."""
        if self.modules_per_row <= 0 or self.modules_per_column <= 0:
            raise ValueError("Modules per row and column must be positive")
        
        calculated_max = self.modules_per_row * self.modules_per_column
        if self.max_modules_per_space != calculated_max:
            logger.warning(
                f"max_modules_per_space ({self.max_modules_per_space}) "
                f"does not match calculated value ({calculated_max})"
            )
    
    @property
    def total_module_width(self) -> float:
        """Get total width of all modules in a row."""
        return self.modules_per_row * self.module_width + (self.modules_per_row - 1) * self.spacing_between_modules
    
    @property
    def total_module_height(self) -> float:
        """Get total height of all modules in a column."""
        return self.modules_per_column * self.module_height + (self.modules_per_column - 1) * self.spacing_between_modules
    
    def get_module_position(self, module_index: int) -> tuple[float, float]:
        """
        Get the position of a module within a drawing space.
        
        Args:
            module_index: Index of the module (0-based)
            
        Returns:
            Tuple of (x, y) coordinates for module position
        """
        if module_index >= self.max_modules_per_space:
            raise ValueError(f"Module index {module_index} exceeds maximum {self.max_modules_per_space}")
        
        row = module_index // self.modules_per_row
        col = module_index % self.modules_per_row
        
        x = col * (self.module_width + self.spacing_between_modules)
        y = row * (self.module_height + self.spacing_between_modules)
        
        return (x, y)


@dataclass
class OutputConfig:
    """
    Configuration for output generation.
    
    Attributes:
        dxf_version: DXF file version for output
        preserve_layers: Whether to preserve original layer properties
        preserve_colors: Whether to preserve original colors
        preserve_linetypes: Whether to preserve original line types
        preserve_lineweights: Whether to preserve original line weights
        preserve_text_styles: Whether to preserve original text styles
        preserve_block_definitions: Whether to preserve block definitions
        generate_report: Whether to generate summary report
        report_format: Format for summary report ('txt', 'html', 'pdf')
        compression_enabled: Whether to compress output files
    """
    dxf_version: str = "R2018"
    preserve_layers: bool = True
    preserve_colors: bool = True
    preserve_linetypes: bool = True
    preserve_lineweights: bool = True
    preserve_text_styles: bool = True
    preserve_block_definitions: bool = True
    generate_report: bool = True
    report_format: str = "txt"
    compression_enabled: bool = False
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        self._validate_dxf_version()
        self._validate_report_format()
    
    def _validate_dxf_version(self) -> None:
        """Validate DXF version."""
        valid_versions = ["R12", "R2000", "R2004", "R2007", "R2010", "R2013", "R2018"]
        if self.dxf_version not in valid_versions:
            logger.warning(f"DXF version '{self.dxf_version}' may not be supported. Valid versions: {valid_versions}")
    
    def _validate_report_format(self) -> None:
        """Validate report format."""
        valid_formats = ["txt", "html", "pdf", "json"]
        if self.report_format not in valid_formats:
            raise ValueError(f"Invalid report format '{self.report_format}'. Valid formats: {valid_formats}")


@dataclass
class GeneralNotesConfig:
    """
    Main configuration class for the General Notes Drawing Generator.
    
    Combines all configuration aspects into a single configuration object
    following the pattern established in the column_drawing package.
    
    Attributes:
        database: Database and file path configuration
        layout: Layout and drawing space configuration
        output: Output generation configuration
        application_name: Name of the application
        version: Application version
        debug_mode: Whether debug mode is enabled
        log_level: Logging level
        ui_settings: UI-specific settings
    """
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    layout: LayoutConfig = field(default_factory=LayoutConfig)
    output: OutputConfig = field(default_factory=OutputConfig)
    application_name: str = "General Notes Drawing Generator"
    version: str = "1.0.0"
    debug_mode: bool = False
    log_level: str = "INFO"
    ui_settings: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize default UI settings if not provided."""
        if not self.ui_settings:
            self.ui_settings = self._get_default_ui_settings()
    
    def _get_default_ui_settings(self) -> Dict[str, Any]:
        """Get default UI settings."""
        return {
            'window_width': 800,
            'window_height': 600,
            'window_title': f"{self.application_name} v{self.version}",
            'checklist_height': 400,
            'search_enabled': True,
            'category_filter_enabled': True,
            'progress_bar_enabled': True,
            'auto_save_selections': True,
            'remember_window_position': True,
            'theme': 'default'
        }
    
    @property
    def default_database_path(self) -> str:
        """Get default database path."""
        return self.database.default_database_path
    
    @property
    def default_summary_path(self) -> str:
        """Get default summary path."""
        return self.database.default_summary_path
    
    @property
    def drawing_space_dimensions(self) -> tuple[float, float]:
        """Get drawing space dimensions as tuple."""
        return (self.layout.drawing_space_width, self.layout.drawing_space_height)
    
    @property
    def module_dimensions(self) -> tuple[float, float]:
        """Get module dimensions as tuple."""
        return (self.layout.module_width, self.layout.module_height)
    
    def get_ui_setting(self, key: str, default: Any = None) -> Any:
        """
        Get a UI setting value.
        
        Args:
            key: Setting key
            default: Default value if key not found
            
        Returns:
            Setting value or default
        """
        return self.ui_settings.get(key, default)
    
    def set_ui_setting(self, key: str, value: Any) -> None:
        """
        Set a UI setting value.
        
        Args:
            key: Setting key
            value: Setting value
        """
        self.ui_settings[key] = value
    
    def validate_configuration(self) -> List[str]:
        """
        Validate the entire configuration and return any warnings.
        
        Returns:
            List of validation warning messages
        """
        warnings = []
        
        # Check if database file exists
        if not Path(self.default_database_path).exists():
            warnings.append(f"Database file does not exist: {self.default_database_path}")
        
        # Check if summary file exists
        if not Path(self.default_summary_path).exists():
            warnings.append(f"Summary file does not exist: {self.default_summary_path}")
        
        # Check layout consistency
        if (self.layout.module_width * self.layout.modules_per_row > self.layout.drawing_space_width or
            self.layout.module_height * self.layout.modules_per_column > self.layout.drawing_space_height):
            warnings.append("Modules may not fit within drawing space dimensions")
        
        return warnings
    
    @classmethod
    def create_default(cls) -> 'GeneralNotesConfig':
        """
        Create a default configuration instance.
        
        Returns:
            GeneralNotesConfig with default settings
        """
        return cls()
    
    @classmethod
    def create_development(cls) -> 'GeneralNotesConfig':
        """
        Create a development configuration instance.
        
        Returns:
            GeneralNotesConfig with development settings
        """
        config = cls()
        config.debug_mode = True
        config.log_level = "DEBUG"
        config.database.backup_enabled = False
        config.output.generate_report = True
        config.output.report_format = "html"
        return config
