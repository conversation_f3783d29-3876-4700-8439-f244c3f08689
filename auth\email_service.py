"""
Email Service Module
==================

This module provides email-related functionality for the authentication system.
Handles password delivery, usage logging, and completion notifications.

Features:
    - Secure password email delivery
    - Usage logging for monitoring
    - Drawing completion notifications
    - Multiple SMTP fallback methods
    - Asynchronous email processing

Author: Drawing Production System
Date: 2025
"""

import logging
import smtplib
import ssl
import threading
from datetime import datetime
from email.message import EmailMessage
from typing import Optional, Callable

from .constants import EMAIL_SENDER, EMAIL_PASSWORD, APP_VERSION


def _send_email_in_background(func: Callable, *args, **kwargs) -> None:
    """
    Execute an email sending function in the background to prevent UI blocking.
    
    Args:
        func: The email function to execute
        *args: Arguments to pass to the function
        **kwargs: Keyword arguments to pass to the function
    """
    def email_wrapper():
        try:
            result = func(*args, **kwargs)
            logging.info(f"Background email task completed: {func.__name__}")
            return result
        except Exception as e:
            logging.error(f"Background email task failed: {func.__name__} - {e}")
            return False
    
    # Create and start background thread
    email_thread = threading.Thread(
        target=email_wrapper,
        name="EmailSender",
        daemon=True
    )
    email_thread.start()


def _send_via_ssl_465(email_message: EmailMessage) -> None:
    """Send email using SSL on port 465 (original method)."""
    context = ssl.create_default_context()
    with smtplib.SMTP_SSL('smtp.gmail.com', 465, context=context, timeout=20) as smtp:
        smtp.login(EMAIL_SENDER, EMAIL_PASSWORD)
        smtp.send_message(email_message)


def _send_via_starttls_587(email_message: EmailMessage) -> None:
    """Send email using STARTTLS on port 587 (alternative method)."""
    with smtplib.SMTP('smtp.gmail.com', 587, timeout=20) as smtp:
        smtp.starttls()
        smtp.login(EMAIL_SENDER, EMAIL_PASSWORD)
        smtp.send_message(email_message)


def send_password_email(user_name: str, password_key: str) -> bool:
    """
    Send login password to user's email.

    Args:
        user_name: Username (email prefix)
        password_key: Generated password to send

    Returns:
        Boolean indicating success
    """
    try:
        email_receiver = f"{user_name}@asiainfrasolutions.com"
        subject = f"Drafting Agent {APP_VERSION} Login Password"

        body = f"""
        Dear {user_name},

        Your Login Password for Drafting Agent: 
        {password_key}

        This password will expire after your current session.
        
        Regards,
        Drawing Production System
        """

        # Create email message
        em = EmailMessage()
        em['From'] = EMAIL_SENDER
        em['To'] = email_receiver
        em['Subject'] = subject
        em.set_content(body)

        # Send email securely
        context = ssl.create_default_context()
        with smtplib.SMTP_SSL('smtp.gmail.com', 465, context=context, timeout=20) as smtp:
            smtp.login(EMAIL_SENDER, EMAIL_PASSWORD)
            smtp.send_message(em)

        logging.info(f"Password sent successfully to {email_receiver}")
        return True

    except smtplib.SMTPAuthenticationError as auth_error:
        logging.error(f"SMTP authentication failed: {auth_error}")
        return False
    except smtplib.SMTPException as smtp_error:
        logging.error(f"SMTP error: {smtp_error}")
        return False
    except Exception as e:
        logging.error(f"Error sending password email: {e}")
        return False


def send_email_log_sync(user_name: str, case: str, version_type: str = "Base", 
                        user_email: Optional[str] = None) -> bool:
    """
    Synchronous email sending function with multiple fallback methods.
    
    This function tries multiple SMTP configurations to handle network restrictions.
    
    Args:
        user_name: Username performing the action
        case: Description of the action
        version_type: Software version type
        user_email: Optional email address (<NAME_EMAIL>)

    Returns:
        Boolean indicating success
    """
    email_receiver = EMAIL_SENDER  # Send logs to the same monitoring address
    subject = 'Drawing Production RPA Usage Log'

    # Get current timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Use provided email or construct from username
    if user_email is None:
        user_email = f"{user_name}@asiainfrasolutions.com"

    body = f"""
    Timestamp: {timestamp}
    Username: {user_name}
    Email: {user_email}
    Version: {APP_VERSION} ({version_type})
    Action: {case}
    """

    # Create email message
    em = EmailMessage()
    em['From'] = EMAIL_SENDER
    em['To'] = email_receiver
    em['Subject'] = subject
    em.set_content(body)

    # Try multiple SMTP methods in order of preference
    methods = [
        ("SMTP SSL (Port 465)", _send_via_ssl_465),
        ("SMTP STARTTLS (Port 587)", _send_via_starttls_587),
    ]
    
    for method_name, send_method in methods:
        try:
            logging.info(f"Attempting email via {method_name}")
            send_method(em)
            logging.info(f"Email sent successfully via {method_name}")
            return True
        except Exception as e:
            logging.warning(f"Failed to send email via {method_name}: {e}")
            continue
    
    # All methods failed
    logging.error(f"All email sending methods failed for user: {user_name}, action: {case}")
    logging.info("Email sending failed - this may be due to network restrictions (firewall/SMTP blocking)")
    return False


def send_email_log(user_name: str, case: str, version_type: str = "Base", 
                  user_email: Optional[str] = None) -> bool:
    """
    Log user actions via email for monitoring (Enhanced Asynchronous version).

    This function sends the email in the background using simple threading to prevent UI blocking.
    Provides better error handling and graceful degradation when email services are unavailable.

    Args:
        user_name: Username performing the action
        case: Description of the action
        version_type: Software version type
        user_email: Optional email address (<NAME_EMAIL>)

    Returns:
        Boolean indicating success (True if queued successfully, even if actual sending might fail)
    """
    try:
        _send_email_in_background(send_email_log_sync, user_name, case, version_type, user_email)
        return True
    except Exception as e:
        logging.error(f"Failed to queue email log: {e}")
        return False


def send_drawing_completion_log(user_name: str, drawings_count: int, csv_filename: str = "", 
                               user_email: Optional[str] = None) -> bool:
    """
    Log drawing generation completion via email for monitoring.

    This function sends a completion notification to EMAIL_SENDER when a user 
    successfully finishes generating column drawings, including the count of 
    details drawn.

    Args:
        user_name: Username who completed the drawing generation
        drawings_count: Number of column detail drawings successfully generated
        csv_filename: Name of the CSV file processed (optional)
        user_email: Optional email address (<NAME_EMAIL>)

    Returns:
        Boolean indicating success (True if queued successfully, even if actual sending might fail)
    """
    try:
        _send_email_in_background(send_drawing_completion_log_sync, user_name, drawings_count, csv_filename, user_email)
        return True
    except Exception as e:
        logging.error(f"Failed to queue drawing completion log: {e}")
        return False


def send_drawing_completion_log_sync(user_name: str, drawings_count: int, csv_filename: str = "", 
                                    user_email: Optional[str] = None) -> bool:
    """
    Synchronous drawing completion email sending function.
    
    This function sends detailed completion notifications to EMAIL_SENDER 
    with information about successful drawing generation.
    
    Args:
        user_name: Username who completed the drawing generation
        drawings_count: Number of column detail drawings successfully generated
        csv_filename: Name of the CSV file processed (optional)
        user_email: Optional email address (<NAME_EMAIL>)

    Returns:
        Boolean indicating success
    """
    email_receiver = EMAIL_SENDER  # Send completion logs to monitoring address
    subject = f'Drawing Production - Generation Completed ({drawings_count} drawings)'

    # Get current timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Use provided email or construct from username
    if user_email is None:
        user_email = f"{user_name}@asiainfrasolutions.com"

    # Create detailed completion report
    body = f"""
    DRAWING GENERATION COMPLETED

    Timestamp: {timestamp}
    Username: {user_name}
    Email: {user_email}
    Version: {APP_VERSION}
    
    Generation Results:
    - Number of Details Generated: {drawings_count}
    - Source CSV File: {csv_filename if csv_filename else 'Not specified'}
    - Status: Successfully Completed
    
    This notification confirms that the user has successfully completed 
    the column drawing generation process.
    """

    # Create email message
    em = EmailMessage()
    em['From'] = EMAIL_SENDER
    em['To'] = email_receiver
    em['Subject'] = subject
    em.set_content(body)

    # Try multiple SMTP methods in order of preference
    methods = [
        ("SMTP SSL (Port 465)", _send_via_ssl_465),
        ("SMTP STARTTLS (Port 587)", _send_via_starttls_587),
    ]
    
    for method_name, send_method in methods:
        try:
            logging.info(f"Attempting completion email via {method_name}")
            send_method(em)
            logging.info(f"Drawing completion email sent successfully via {method_name}")
            return True
        except Exception as e:
            logging.warning(f"Failed to send completion email via {method_name}: {e}")
            continue
    
    # All methods failed
    logging.error(f"All email sending methods failed for drawing completion: user={user_name}, drawings={drawings_count}")
    logging.info("Drawing completion email failed - this may be due to network restrictions (firewall/SMTP blocking)")
    return False


def log_security_event(user_name: str, case: str, user_email: Optional[str] = None) -> bool:
    """
    Log security events with error handling.
    
    Args:
        user_name: Username related to the security event
        case: Description of the security event
        user_email: Optional email address
        
    Returns:
        Boolean indicating success
    """
    try:
        return send_email_log(user_name, f"SECURITY: {case}", "Security", user_email)
    except Exception as e:
        logging.error(f"Failed to log security event: {e}")
        return False