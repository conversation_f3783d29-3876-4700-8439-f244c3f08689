"""
Excel Parser
===========

Excel file parsing functionality for the General Notes Drawing Generator.

This module provides functionality for:
- Reading Excel summary files with note metadata
- Parsing note titles and boundary coordinates
- Data validation and error handling
- Converting Excel data to NoteDetail objects

Author: Drawing Production System
"""

import logging
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import pandas as pd

from ..models.note_detail import NoteDetail, NoteDetailCollection
from ..models.boundary import Boundary, BoundaryValidationError
from .validation import DataValidator, ValidationResult
from .coordinate_parser import CoordinateParser, CoordinateParsingError

logger = logging.getLogger(__name__)


class ExcelParsingError(Exception):
    """Raised when Excel parsing fails."""
    pass


class ExcelParser:
    """
    Parser for Excel summary files containing note detail metadata.
    
    Handles reading Excel files with note titles and boundary coordinates,
    converting them to NoteDetail objects with proper validation.
    """
    
    # Required columns in Excel file
    REQUIRED_COLUMNS = ['Title', 'Point1', 'Point2', 'Point3', 'Point4']
    
    # Optional columns that can be included
    OPTIONAL_COLUMNS = ['Description', 'Category', 'Tags']
    
    def __init__(self, sheet_name: str = 'Detail', strict_coordinates: bool = False):
        """
        Initialize the Excel parser.
        
        Args:
            sheet_name: Name of the Excel sheet to read
            strict_coordinates: Whether to use strict coordinate parsing
        """
        self.sheet_name = sheet_name
        self.strict_coordinates = strict_coordinates
        self.coordinate_parser = CoordinateParser(strict_mode=strict_coordinates)
        self.data_validator = DataValidator()
    
    def parse_excel_file(self, file_path: Union[str, Path]) -> NoteDetailCollection:
        """
        Parse an Excel file and return a collection of note details.
        
        Args:
            file_path: Path to the Excel file
            
        Returns:
            NoteDetailCollection with parsed note details
            
        Raises:
            ExcelParsingError: If parsing fails
        """
        try:
            logger.info(f"Parsing Excel file: {file_path}")
            
            # Read Excel file
            df = self._read_excel_file(file_path)
            
            # Validate structure
            validation_result = self._validate_excel_data(df)
            if not validation_result.is_valid:
                raise ExcelParsingError(f"Excel validation failed: {validation_result.get_error_summary()}")
            
            # Log warnings if any
            if validation_result.has_warnings():
                logger.warning(f"Excel validation warnings: {validation_result.get_warning_summary()}")
            
            # Parse note details
            note_details = self._parse_note_details(df)
            
            # Create collection
            collection = NoteDetailCollection(
                notes=note_details,
                name=f"Notes from {Path(file_path).name}",
                description=f"Parsed from Excel file: {file_path}"
            )
            
            logger.info(f"Successfully parsed {len(note_details)} note details from Excel file")
            return collection
            
        except Exception as e:
            error_msg = f"Failed to parse Excel file '{file_path}': {str(e)}"
            logger.error(error_msg)
            raise ExcelParsingError(error_msg) from e
    
    def _read_excel_file(self, file_path: Union[str, Path]) -> pd.DataFrame:
        """
        Read Excel file into DataFrame.
        
        Args:
            file_path: Path to Excel file
            
        Returns:
            DataFrame with Excel data
            
        Raises:
            ExcelParsingError: If file cannot be read
        """
        try:
            # Try to read the specified sheet
            df = pd.read_excel(file_path, sheet_name=self.sheet_name)
            
            if df.empty:
                raise ExcelParsingError(f"Sheet '{self.sheet_name}' is empty")
            
            logger.debug(f"Read {len(df)} rows from sheet '{self.sheet_name}'")
            return df
            
        except ValueError as e:
            # Sheet name not found, try to get available sheets
            try:
                excel_file = pd.ExcelFile(file_path)
                available_sheets = excel_file.sheet_names
                raise ExcelParsingError(
                    f"Sheet '{self.sheet_name}' not found. Available sheets: {', '.join(available_sheets)}"
                )
            except Exception:
                raise ExcelParsingError(f"Cannot read Excel file: {str(e)}")
        
        except Exception as e:
            raise ExcelParsingError(f"Error reading Excel file: {str(e)}")
    
    def _validate_excel_data(self, df: pd.DataFrame) -> ValidationResult:
        """
        Validate Excel DataFrame structure and content.
        
        Args:
            df: DataFrame to validate
            
        Returns:
            ValidationResult with validation details
        """
        # Validate structure
        result = self.data_validator.validate_excel_structure(df, self.REQUIRED_COLUMNS)
        
        if result.is_valid:
            # Validate note data content
            note_result = self.data_validator.validate_note_data(df)
            result.merge(note_result)
        
        return result
    
    def _parse_note_details(self, df: pd.DataFrame) -> List[NoteDetail]:
        """
        Parse note details from DataFrame.
        
        Args:
            df: DataFrame with note data
            
        Returns:
            List of NoteDetail objects
            
        Raises:
            ExcelParsingError: If parsing fails
        """
        note_details = []
        parsing_errors = []
        
        for index, row in df.iterrows():
            try:
                note_detail = self._parse_note_row(row, index)
                if note_detail:
                    note_details.append(note_detail)
            except Exception as e:
                error_msg = f"Row {index + 1}: {str(e)}"
                parsing_errors.append(error_msg)
                logger.warning(error_msg)
        
        # Check if we have any successful parses
        if not note_details and parsing_errors:
            raise ExcelParsingError(f"Failed to parse any note details. Errors: {'; '.join(parsing_errors)}")
        
        if parsing_errors:
            logger.warning(f"Skipped {len(parsing_errors)} rows due to parsing errors")
        
        return note_details
    
    def _parse_note_row(self, row: pd.Series, row_index: int) -> Optional[NoteDetail]:
        """
        Parse a single row into a NoteDetail object.
        
        Args:
            row: DataFrame row
            row_index: Index of the row
            
        Returns:
            NoteDetail object or None if row should be skipped
            
        Raises:
            ExcelParsingError: If row parsing fails
        """
        try:
            # Extract title
            title = str(row.get('Title', '')).strip()
            if not title or pd.isna(row.get('Title')):
                logger.warning(f"Row {row_index + 1}: Empty title, skipping")
                return None
            
            # Parse coordinates
            coordinates = self._parse_row_coordinates(row, row_index)
            
            # Create boundary
            boundary = Boundary(
                point1=coordinates[0],
                point2=coordinates[1],
                point3=coordinates[2],
                point4=coordinates[3],
                title=title
            )
            
            # Extract optional fields
            description = self._extract_optional_field(row, 'Description')
            category = self._extract_optional_field(row, 'Category')
            tags = self._extract_tags(row)
            
            # Create note detail
            note_detail = NoteDetail(
                title=title,
                boundary=boundary,
                description=description,
                category=category,
                tags=tags
            )
            
            logger.debug(f"Parsed note detail: {title}")
            return note_detail
            
        except BoundaryValidationError as e:
            raise ExcelParsingError(f"Boundary validation failed: {str(e)}")
        except Exception as e:
            raise ExcelParsingError(f"Error parsing row: {str(e)}")
    
    def _parse_row_coordinates(self, row: pd.Series, row_index: int) -> List:
        """
        Parse coordinate points from a row.
        
        Args:
            row: DataFrame row
            row_index: Index of the row
            
        Returns:
            List of BoundaryPoint objects
            
        Raises:
            ExcelParsingError: If coordinate parsing fails
        """
        from ..models.boundary import BoundaryPoint
        
        coordinate_columns = ['Point1', 'Point2', 'Point3', 'Point4']
        coordinates = []
        
        for col in coordinate_columns:
            coord_str = row.get(col, '')
            
            if pd.isna(coord_str) or not str(coord_str).strip():
                raise ExcelParsingError(f"{col} is empty or missing")
            
            try:
                # Parse coordinate string
                parsed_coord = self.coordinate_parser.parse_coordinate(str(coord_str))
                
                # Create BoundaryPoint
                boundary_point = BoundaryPoint(
                    x=parsed_coord.x,
                    y=parsed_coord.y,
                    z=parsed_coord.z
                )
                coordinates.append(boundary_point)
                
            except CoordinateParsingError as e:
                raise ExcelParsingError(f"Invalid {col} format: {str(e)}")
        
        return coordinates
    
    def _extract_optional_field(self, row: pd.Series, field_name: str) -> Optional[str]:
        """
        Extract an optional string field from a row.
        
        Args:
            row: DataFrame row
            field_name: Name of the field to extract
            
        Returns:
            Field value or None if empty/missing
        """
        value = row.get(field_name, '')
        if pd.isna(value) or not str(value).strip():
            return None
        return str(value).strip()
    
    def _extract_tags(self, row: pd.Series) -> List[str]:
        """
        Extract tags from a row.
        
        Args:
            row: DataFrame row
            
        Returns:
            List of tag strings
        """
        tags_str = self._extract_optional_field(row, 'Tags')
        if not tags_str:
            return []
        
        # Split by comma and clean up
        tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()]
        return tags
    
    def get_available_sheets(self, file_path: Union[str, Path]) -> List[str]:
        """
        Get list of available sheet names in Excel file.
        
        Args:
            file_path: Path to Excel file
            
        Returns:
            List of sheet names
            
        Raises:
            ExcelParsingError: If file cannot be read
        """
        try:
            excel_file = pd.ExcelFile(file_path)
            return excel_file.sheet_names
        except Exception as e:
            raise ExcelParsingError(f"Cannot read Excel file: {str(e)}")
    
    def preview_excel_data(self, file_path: Union[str, Path], max_rows: int = 5) -> Dict[str, Any]:
        """
        Preview Excel file data without full parsing.
        
        Args:
            file_path: Path to Excel file
            max_rows: Maximum number of rows to preview
            
        Returns:
            Dictionary with preview information
            
        Raises:
            ExcelParsingError: If file cannot be read
        """
        try:
            df = self._read_excel_file(file_path)
            
            preview_data = {
                'sheet_name': self.sheet_name,
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'columns': df.columns.tolist(),
                'required_columns_present': all(col in df.columns for col in self.REQUIRED_COLUMNS),
                'missing_columns': [col for col in self.REQUIRED_COLUMNS if col not in df.columns],
                'optional_columns_present': [col for col in self.OPTIONAL_COLUMNS if col in df.columns],
                'preview_rows': df.head(max_rows).to_dict('records')
            }
            
            return preview_data
            
        except Exception as e:
            raise ExcelParsingError(f"Cannot preview Excel file: {str(e)}")
    
    @classmethod
    def create_strict_parser(cls, sheet_name: str = 'Detail') -> 'ExcelParser':
        """
        Create a parser with strict coordinate validation.
        
        Args:
            sheet_name: Name of Excel sheet to read
            
        Returns:
            ExcelParser with strict validation
        """
        return cls(sheet_name=sheet_name, strict_coordinates=True)
    
    @classmethod
    def create_lenient_parser(cls, sheet_name: str = 'Detail') -> 'ExcelParser':
        """
        Create a parser with lenient coordinate validation.
        
        Args:
            sheet_name: Name of Excel sheet to read
            
        Returns:
            ExcelParser with lenient validation
        """
        return cls(sheet_name=sheet_name, strict_coordinates=False)


# Convenience functions
def parse_excel_file(file_path: Union[str, Path], sheet_name: str = 'Detail') -> NoteDetailCollection:
    """
    Parse Excel file and return note detail collection.
    
    Args:
        file_path: Path to Excel file
        sheet_name: Name of sheet to read
        
    Returns:
        NoteDetailCollection with parsed notes
        
    Raises:
        ExcelParsingError: If parsing fails
    """
    parser = ExcelParser(sheet_name=sheet_name)
    return parser.parse_excel_file(file_path)


def validate_excel_file(file_path: Union[str, Path], sheet_name: str = 'Detail') -> ValidationResult:
    """
    Validate Excel file structure and content.
    
    Args:
        file_path: Path to Excel file
        sheet_name: Name of sheet to validate
        
    Returns:
        ValidationResult with validation details
    """
    try:
        parser = ExcelParser(sheet_name=sheet_name)
        df = parser._read_excel_file(file_path)
        return parser._validate_excel_data(df)
    except Exception as e:
        result = ValidationResult()
        result.add_error(f"Cannot validate Excel file: {str(e)}")
        return result
