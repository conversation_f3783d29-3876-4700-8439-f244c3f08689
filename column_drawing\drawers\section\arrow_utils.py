"""
Arrow Drawing Utilities
=======================

Utility classes and functions for arrow positioning calculations,
coordinate processing, and link mark management in section drawings.
"""

import logging
import math
from typing import List, Tuple, Dict, Optional
from ...models.column_config import ColumnConfig
from ...models.zone_config import ZoneConfig
from ...models.rebar_layer_data import RebarLayerData
from ..rebar.bs8666_shapes import internal_bend_radius, calculate_shape_52_length, calculate_shape_25a_length

logger = logging.getLogger(__name__)


class CoordinateProcessor:
    """Utility class for processing and organizing rebar coordinates."""

    @staticmethod
    def extract_unique_coordinates(rebar_data: RebarLayerData) -> Tuple[List[float], List[float]]:
        """
        Extract unique X and Y coordinates from rebar layer data.

        Args:
            rebar_data: Organized rebar data with layer positions

        Returns:
            Tuple of (sorted_x_coords, sorted_y_coords) where Y is sorted top to bottom
        """
        if not rebar_data.layer1.positions:
            return [], []

        x_coords = sorted(list(set(pos[0]
                          for pos in rebar_data.layer1.positions)))
        y_coords = sorted(
            list(set(pos[1] for pos in rebar_data.layer1.positions)), reverse=True)

        return x_coords, y_coords

    @staticmethod
    def calculate_y_level_position(y_coords: List[float], level_ratio: Tuple[int, int]) -> Optional[float]:
        """
        Calculate Y position based on level ratios between rebar rows.

        Args:
            y_coords: List of Y coordinates sorted from top to bottom
            level_ratio: Tuple of (start_level, end_level) indices (1-based)

        Returns:
            Y position at the middle of specified levels, or None if insufficient coords
        """
        start_idx, end_idx = level_ratio[0] - \
            1, level_ratio[1] - 1  # Convert to 0-based

        if len(y_coords) <= max(start_idx, end_idx):
            return None

        start_y = y_coords[start_idx]
        end_y = y_coords[end_idx]
        return (start_y + end_y) / 2

    @staticmethod
    def select_boundary_coordinates(coords: List[float], count: int) -> Tuple[float, float]:
        """
        Select boundary coordinates based on available coordinate count.

        Args:
            coords: List of coordinates (sorted)
            count: Number of desired intermediate positions

        Returns:
            Tuple of (start_boundary, end_boundary)
        """
        if len(coords) >= 4:
            # Standard case: use 2nd and 2nd-last as boundaries
            return coords[1], coords[-2]
        elif len(coords) == 3:
            # 3 coordinates: use 1st and last (skip middle)
            return coords[0], coords[-1]
        else:  # len(coords) == 2
            # 2 coordinates: use both
            return coords[0], coords[-1]

    @staticmethod
    def select_intermediate_positions(
        coords: List[float],
        num_positions: int,
        boundary_start: float,
        boundary_end: float
    ) -> List[float]:
        """
        Select evenly distributed intermediate positions within boundaries.

        Args:
            coords: Available coordinates within boundaries
            num_positions: Number of positions to select
            boundary_start: Start boundary coordinate
            boundary_end: End boundary coordinate

        Returns:
            List of selected coordinate positions
        """
        # Filter coordinates within boundaries
        if boundary_start <= boundary_end:
            available_coords = [
                c for c in coords if boundary_start <= c <= boundary_end]
        else:
            available_coords = [
                c for c in coords if boundary_end <= c <= boundary_start]

        if len(available_coords) <= num_positions:
            return available_coords

        if num_positions == 1:
            # Single position: use middle coordinate
            mid_idx = len(available_coords) // 2
            return [available_coords[mid_idx]]
        else:
            # Multiple positions: select evenly distributed
            indices = []
            for i in range(num_positions):
                idx = int(i * (len(available_coords) - 1) /
                          (num_positions - 1))
                indices.append(idx)
            return [available_coords[idx] for idx in indices]


class ArrowPositionCalculator:
    """Calculator for arrow positioning in section drawings."""

    def __init__(self, column_config: ColumnConfig, zone_config: ZoneConfig):
        """
        Initialize arrow position calculator.

        Args:
            column_config: Column configuration
            zone_config: Zone configuration with link information
        """
        self.column_config = column_config
        self.zone_config = zone_config
        self.coord_processor = CoordinateProcessor()

    def calculate_52_link_positions(self, rebar_data: RebarLayerData) -> List[float]:
        """
        Calculate exact X positions for 52 link vertical lines.

        Args:
            rebar_data: Rebar layer data for coordinate extraction

        Returns:
            List of X coordinates where 52 link vertical lines are positioned
        """
        x_coords, _ = self.coord_processor.extract_unique_coordinates(
            rebar_data)

        if len(x_coords) < 2:
            return []

        # Calculate BS8666 Shape 52 vertical line positions
        rebar_diameter = self.column_config.dia1
        link_diameter = self.column_config.dia_links
        r = internal_bend_radius(link_diameter)

        # Calculate adjustment
        adjustment = r - rebar_diameter / 2
        adjustment_x = adjustment * math.cos(math.pi / 4)  # 45 degrees

        # Corner rebar positions
        bl_rebar_x = x_coords[0]   # Bottom-left (leftmost)
        br_rebar_x = x_coords[-1]  # Bottom-right (rightmost)

        # Calculate center positions
        bl_center_x = bl_rebar_x + adjustment_x
        br_center_x = br_rebar_x - adjustment_x

        # Calculate actual vertical line X positions
        left_52_x = bl_center_x - (r + link_diameter/2)
        right_52_x = br_center_x + (r + link_diameter/2)

        logger.debug(
            f"52 link vertical lines: left={left_52_x:.1f}, right={right_52_x:.1f}")
        return [left_52_x, right_52_x]

    def calculate_25a_x_link_positions(self, rebar_data: RebarLayerData) -> List[float]:
        """
        Calculate exact X positions for vertical 25A link connecting lines.

        Args:
            rebar_data: Rebar layer data for coordinate extraction

        Returns:
            List of X coordinates where 25A X-direction links are positioned
        """
        x_coords, _ = self.coord_processor.extract_unique_coordinates(
            rebar_data)

        num_25a_vertical_links = max(0, self.zone_config.link_legs_x - 2)
        if num_25a_vertical_links == 0 or len(x_coords) < 3:
            return []

        # Use same algorithm as LinkDrawer for consistency
        boundary_start, boundary_end = self.coord_processor.select_boundary_coordinates(
            x_coords, num_25a_vertical_links
        )

        selected_rebar_x = self.coord_processor.select_intermediate_positions(
            x_coords, num_25a_vertical_links, boundary_start, boundary_end
        )

        # Calculate connecting line positions using BS8666 Shape 25A logic
        rebar_diameter = self.column_config.dia1
        link_diameter = self.zone_config.inner_link_diameter
        r = internal_bend_radius(link_diameter)
        offset_25a = (rebar_diameter + link_diameter) / 2 + r

        # For vertical links, connecting line X = rebar_x + offset
        connecting_line_positions = [
            rebar_x + offset_25a for rebar_x in selected_rebar_x]

        logger.debug(
            f"25A X link connecting lines: {[f'{x:.1f}' for x in connecting_line_positions]}")
        return connecting_line_positions

    def calculate_25a_y_link_positions(self, rebar_data: RebarLayerData) -> List[float]:
        """
        Calculate exact Y positions for horizontal 25A link connecting lines.

        Args:
            rebar_data: Rebar layer data for coordinate extraction

        Returns:
            List of Y coordinates where 25A Y-direction links are positioned
        """
        _, y_coords = self.coord_processor.extract_unique_coordinates(
            rebar_data)

        num_25a_horizontal_links = max(0, self.zone_config.link_legs_y - 2)
        if num_25a_horizontal_links == 0 or len(y_coords) < 3:
            return []

        # Use same algorithm as LinkDrawer for consistency
        boundary_start, boundary_end = self.coord_processor.select_boundary_coordinates(
            y_coords, num_25a_horizontal_links
        )

        selected_rebar_y = self.coord_processor.select_intermediate_positions(
            y_coords, num_25a_horizontal_links, boundary_start, boundary_end
        )

        # Calculate connecting line positions using BS8666 Shape 25A logic
        rebar_diameter = self.column_config.dia1
        link_diameter = self.zone_config.inner_link_diameter
        r = internal_bend_radius(link_diameter)
        offset_25a = (rebar_diameter + link_diameter) / 2 + r

        # For horizontal links, connecting line Y = rebar_y + offset (upward)
        connecting_line_positions = [
            rebar_y + offset_25a for rebar_y in selected_rebar_y]

        logger.debug(
            f"25A Y link connecting lines: {[f'{y:.1f}' for y in connecting_line_positions]}")
        return connecting_line_positions


class LinkMarkProvider:
    """Provider for link marks and length calculations using DataFrame for consistency."""

    def __init__(self, column_config: ColumnConfig, zone_config: ZoneConfig):
        """
        Initialize link mark provider.

        Args:
            column_config: Column configuration
            zone_config: Zone configuration
        """
        self.column_config = column_config
        self.zone_config = zone_config

        # Import DataFrame manager for centralized link mark access
        from ...managers.dataframe_manager import get_global_dataframe_manager
        self.dataframe_manager = get_global_dataframe_manager()

    def _format_link_mark_for_section(self, mark: str) -> str:
        """
        Format link mark for section drawing display with parentheses.

        Adds parentheses around individual mark numbers for section drawings:
        - Individual marks: "101" → "(101)"
        - Combined marks: "101/103" → "(101)/(103)"

        Args:
            mark: Raw link mark string from DataFrame

        Returns:
            Formatted link mark with parentheses around individual numbers for section display
        """
        if not mark or not mark.strip():
            return mark

        # Split by "/" separator and add parentheses to each individual mark
        individual_marks = mark.strip().split('/')
        formatted_marks = [
            f"({mark.strip()})" for mark in individual_marks if mark.strip()]

        # Join back with "/" separator
        return '/'.join(formatted_marks)

    def get_52_link_mark(self) -> Optional[str]:
        """
        Get the link mark for 52 links from DataFrame using zone-specific storage.

        Uses DataFrame as the single source of truth for link marks.
        For zones A and C, uses combined marks from generic columns.
        For zones B and D, uses individual zone-specific marks.

        Returns:
            Link mark string formatted for section display with parentheses, or None if error occurs
        """
        try:
            # First try to get mark from DataFrame
            if self.dataframe_manager.is_loaded():
                logger.debug(
                    f"DataFrame is loaded, looking for column_mark={self.column_config.name}, start_floor={self.column_config.start_floor_name}, zone={self.zone_config.zone_id}")

                # For zones A and C, use combined marks from generic columns (Zone A+C merging)
                if self.zone_config.zone_id in ['A', 'C']:
                    link_marks = self.dataframe_manager.get_link_marks(
                        column_mark=self.column_config.name,
                        start_floor=self.column_config.start_floor_name
                    )
                    outer_mark = link_marks.get('outer_mark', '')
                    logger.debug(
                        f"Zone {self.zone_config.zone_id}: Using combined mark from generic columns: {outer_mark}")
                    if outer_mark:
                        return self._format_link_mark_for_section(outer_mark)

                # For zones B and D, use individual zone-specific marks
                else:
                    zone_marks = self.dataframe_manager.get_link_marks_for_zone(
                        column_mark=self.column_config.name,
                        start_floor=self.column_config.start_floor_name,
                        zone_id=self.zone_config.zone_id
                    )
                    outer_mark = zone_marks.get('outer_mark', '')
                    logger.debug(
                        f"Zone {self.zone_config.zone_id}: Using individual zone mark: {outer_mark}")
                    if outer_mark:
                        return self._format_link_mark_for_section(outer_mark)

                logger.debug(
                    f"No outer_mark found in DataFrame for {self.column_config.name}, zone {self.zone_config.zone_id}")

            # Fallback to original link mark manager if DataFrame not available
            logger.warning(
                "DataFrame not available, falling back to link mark manager")
            from ...managers.link_mark_manager import get_global_link_mark_manager
            link_mark_manager = get_global_link_mark_manager()

            mark_data = link_mark_manager.get_existing_mark(
                category="52",
                diameter=self.zone_config.outer_link_diameter,
                column_mark=self.column_config.name
            )

            raw_mark = mark_data.get_mark_text() if mark_data else None
            return self._format_link_mark_for_section(raw_mark) if raw_mark else None

        except Exception as e:
            logger.error(f"Error getting 52 link mark from DataFrame: {e}")
            return None

    def get_25a_x_link_mark(self) -> Optional[str]:
        """
        Get the link mark for 25A X-direction links from DataFrame using zone-specific storage.

        Uses DataFrame as the single source of truth for link marks.
        For zones A and C, uses combined marks from generic columns.
        For zones B and D, uses individual zone-specific marks.

        Returns:
            Link mark string formatted for section display with parentheses, or None if error occurs
        """
        try:
            # First try to get mark from DataFrame
            if self.dataframe_manager.is_loaded():
                logger.debug(
                    f"Getting 25A X mark for zone {self.zone_config.zone_id}")

                # For zones A and C, use combined marks from generic columns (Zone A+C merging)
                if self.zone_config.zone_id in ['A', 'C']:
                    link_marks = self.dataframe_manager.get_link_marks(
                        column_mark=self.column_config.name,
                        start_floor=self.column_config.start_floor_name
                    )
                    inner_x_mark = link_marks.get('inner_x_mark', '')
                    logger.debug(
                        f"Zone {self.zone_config.zone_id}: Using combined inner_x_mark: {inner_x_mark}")
                    if inner_x_mark:
                        return self._format_link_mark_for_section(inner_x_mark)

                # For zones B and D, use individual zone-specific marks
                else:
                    zone_marks = self.dataframe_manager.get_link_marks_for_zone(
                        column_mark=self.column_config.name,
                        start_floor=self.column_config.start_floor_name,
                        zone_id=self.zone_config.zone_id
                    )
                    inner_x_mark = zone_marks.get('inner_x_mark', '')
                    logger.debug(
                        f"Zone {self.zone_config.zone_id}: Using individual inner_x_mark: {inner_x_mark}")
                    if inner_x_mark:
                        return self._format_link_mark_for_section(inner_x_mark)

            # Fallback to original link mark manager if DataFrame not available
            logger.warning(
                "DataFrame not available, falling back to link mark manager")
            from ...managers.link_mark_manager import get_global_link_mark_manager
            link_mark_manager = get_global_link_mark_manager()

            mark_data = link_mark_manager.get_existing_mark(
                category="25A_X",
                diameter=self.zone_config.inner_link_diameter,
                column_mark=self.column_config.name
            )

            raw_mark = mark_data.get_mark_text() if mark_data else None
            return self._format_link_mark_for_section(raw_mark) if raw_mark else None

        except Exception as e:
            logger.error(f"Error getting 25A X link mark from DataFrame: {e}")
            return None

    def get_25a_y_link_mark(self) -> Optional[str]:
        """
        Get the link mark for 25A Y-direction links from DataFrame using zone-specific storage.

        Uses DataFrame as the single source of truth for link marks.
        For zones A and C, uses combined marks from generic columns.
        For zones B and D, uses individual zone-specific marks.

        Returns:
            Link mark string formatted for section display with parentheses, or None if error occurs
        """
        try:
            # First try to get mark from DataFrame
            if self.dataframe_manager.is_loaded():
                logger.debug(
                    f"Getting 25A Y mark for zone {self.zone_config.zone_id}")

                # For zones A and C, use combined marks from generic columns (Zone A+C merging)
                if self.zone_config.zone_id in ['A', 'C']:
                    link_marks = self.dataframe_manager.get_link_marks(
                        column_mark=self.column_config.name,
                        start_floor=self.column_config.start_floor_name
                    )
                    inner_y_mark = link_marks.get('inner_y_mark', '')
                    logger.debug(
                        f"Zone {self.zone_config.zone_id}: Using combined inner_y_mark: {inner_y_mark}")
                    if inner_y_mark:
                        return self._format_link_mark_for_section(inner_y_mark)

                # For zones B and D, use individual zone-specific marks
                else:
                    zone_marks = self.dataframe_manager.get_link_marks_for_zone(
                        column_mark=self.column_config.name,
                        start_floor=self.column_config.start_floor_name,
                        zone_id=self.zone_config.zone_id
                    )
                    inner_y_mark = zone_marks.get('inner_y_mark', '')
                    logger.debug(
                        f"Zone {self.zone_config.zone_id}: Using individual inner_y_mark: {inner_y_mark}")
                    if inner_y_mark:
                        return self._format_link_mark_for_section(inner_y_mark)

            # Fallback to original link mark manager if DataFrame not available
            logger.warning(
                "DataFrame not available, falling back to link mark manager")
            from ...managers.link_mark_manager import get_global_link_mark_manager
            link_mark_manager = get_global_link_mark_manager()

            mark_data = link_mark_manager.get_existing_mark(
                category="25A_Y",
                diameter=self.zone_config.inner_link_diameter,
                column_mark=self.column_config.name
            )

            raw_mark = mark_data.get_mark_text() if mark_data else None
            return self._format_link_mark_for_section(raw_mark) if raw_mark else None

        except Exception as e:
            logger.error(f"Error getting 25A Y link mark from DataFrame: {e}")
            return None


class ArrowGeometry:
    """Utility class for arrow geometry calculations."""

    @staticmethod
    def calculate_horizontal_arrowhead_points(
        head_x: float,
        head_y: float,
        head_length: float,
        head_width: float
    ) -> List[Tuple[float, float]]:
        """
        Calculate points for horizontal arrow head (pointing left).

        Args:
            head_x: X coordinate of arrow tip
            head_y: Y coordinate of arrow tip
            head_length: Length of arrow head
            head_width: Width of arrow head

        Returns:
            List of (x, y) points forming the arrow head triangle
        """
        return [
            (head_x, head_y),  # Arrow tip
            (head_x + head_length, head_y + head_width / 2),  # Upper wing
            (head_x + head_length, head_y - head_width / 2),  # Lower wing
            (head_x, head_y)  # Close triangle
        ]

    @staticmethod
    def calculate_vertical_arrowhead_points(
        head_x: float,
        head_y: float,
        head_length: float,
        head_width: float
    ) -> List[Tuple[float, float]]:
        """
        Calculate points for vertical arrow head (pointing up).

        Args:
            head_x: X coordinate of arrow tip
            head_y: Y coordinate of arrow tip
            head_length: Length of arrow head
            head_width: Width of arrow head

        Returns:
            List of (x, y) points forming the arrow head triangle
        """
        return [
            (head_x, head_y),  # Arrow tip
            (head_x - head_width / 2, head_y - head_length),  # Left wing
            (head_x + head_width / 2, head_y - head_length),  # Right wing
            (head_x, head_y)  # Close triangle
        ]

    @staticmethod
    def calculate_text_position(
        tail_x: float,
        tail_y: float,
        text_offset_x: float,
        text_offset_y: float,
        is_horizontal: bool
    ) -> Tuple[float, float]:
        """
        Calculate text position relative to arrow tail.

        Args:
            tail_x: X coordinate of arrow tail
            tail_y: Y coordinate of arrow tail
            text_offset_x: Text offset in X direction
            text_offset_y: Text offset in Y direction
            is_horizontal: True for horizontal arrows, False for vertical

        Returns:
            Tuple of (text_x, text_y) coordinates
        """
        if is_horizontal:
            return (tail_x + text_offset_x, tail_y + text_offset_y)
        else:
            return (tail_x + text_offset_x, tail_y + text_offset_y)
