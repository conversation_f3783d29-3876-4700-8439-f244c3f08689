# Calculators Package

Engineering calculation modules for rebar positioning, geometry, and layout algorithms in the Drawing-Production application.

## 📋 Overview

The calculators package provides the core engineering calculations required for generating accurate reinforced concrete column drawings. These modules implement professional-grade algorithms for rebar positioning, geometric transformations, and validation according to engineering standards.

## 🏗️ Package Components

### RebarCalculator (`rebar_calculator.py`)

**Purpose**: Handles all calculations related to reinforcement bar positioning including perimeter placement, layer alignment, and geometric constraints.

**Key Responsibilities**:

- Perimeter rebar positioning with corner duplicate avoidance
- Layer 2 bounds calculation based on Layer 1 positions
- Rebar alignment between layers for proper structural design
- Geometric validation and constraint checking
- Lap length calculations according to concrete grade and rebar diameter

**Main Methods**:

```python
calculate_perimeter_positions(x, y, width, height, num_x, num_y) -> List[Tuple[float, float]]
calculate_layer2_bounds(layer1_x, layer1_y, layer1_width, layer1_height, num_x1, num_y1, dia2) -> <PERSON><PERSON>[float, float, float, float]
calculate_aligned_positions(layer2_positions, layer1_positions, rect_x, rect_y, rect_width, rect_height, num_x, num_y) -> List[Tuple[float, float]]
validate_rebar_layout(config: ColumnConfig) -> bool
calculate_lap_length(diameter: float, concrete_grade: str) -> float
```

**Algorithm Details**:

- **Perimeter Placement**: Places rebars around rectangle perimeter avoiding corner duplicates
- **Edge Distribution**: Evenly distributes rebars along each edge with proper spacing
- **Layer Alignment**: Aligns Layer 2 middle rebars with closest Layer 1 positions
- **Position Calculation**: Uses formula: rebar center = edge + cover + radius

### GeometryCalculator (`geometry_calculator.py`)

**Purpose**: Handles geometric calculations for scaling, positioning, and coordinate transformations used in drawing generation.

**Key Responsibilities**:

- Scale factor calculations for fitting content within available space
- Coordinate transformations and translations
- Distance and angle calculations
- Geometric validation and bounds checking
- Rectangle and point geometry operations

**Main Methods**:

```python
calculate_scale_factor(content_width, content_height, available_width, available_height, margin=None) -> float
transform_coordinates(points: List[Tuple[float, float]], scale: float, offset_x: float, offset_y: float) -> List[Tuple[float, float]]
calculate_center_position(content_width: float, content_height: float, container_width: float, container_height: float) -> Tuple[float, float]
calculate_distance(point1: Tuple[float, float], point2: Tuple[float, float]) -> float
calculate_rectangle_bounds(points: List[Tuple[float, float]]) -> Tuple[float, float, float, float]
```

**Algorithm Details**:

- **Scaling**: Maintains aspect ratio while fitting content within boundaries
- **Centering**: Calculates optimal positioning for visual balance
- **Bounds Calculation**: Determines minimum bounding rectangles for point sets
- **Coordinate Transformation**: Applies scaling and translation transformations

## 🔧 Dependencies

### Internal Dependencies

- `models.column_config.ColumnConfig`: Column specification data
- `models.drawing_config.DrawingConfig`: Drawing configuration parameters

### External Dependencies

- `math`: Mathematical functions for geometric calculations
- `logging`: Error reporting and debugging information
- `typing`: Type hints for better code documentation

## 🚀 Usage Examples

### Basic Rebar Positioning

```python
from column_drawing.calculators.rebar_calculator import RebarCalculator
from column_drawing.models.drawing_config import DrawingConfig

# Initialize calculator
config = DrawingConfig()
calc = RebarCalculator(config)

# Calculate perimeter positions for a 600x700mm column
positions = calc.calculate_perimeter_positions(
    x=0, y=0, width=500, height=600,  # Scaled dimensions
    num_x=3, num_y=5  # Rebar count in each direction
)

print(f"Generated {len(positions)} rebar positions")
for i, (x, y) in enumerate(positions):
    print(f"Rebar {i+1}: ({x:.1f}, {y:.1f})")
```

### Layer 2 Bounds Calculation

```python
# Calculate Layer 2 bounds based on Layer 1
layer1_x, layer1_y = 0, 0
layer1_width, layer1_height = 500, 600
num_x1, num_y1 = 3, 5
dia2 = 32  # Layer 2 diameter

layer2_bounds = calc.calculate_layer2_bounds(
    layer1_x, layer1_y, layer1_width, layer1_height,
    num_x1, num_y1, dia2
)

layer2_x, layer2_y, layer2_width, layer2_height = layer2_bounds
print(f"Layer 2 bounds: ({layer2_x:.1f}, {layer2_y:.1f}) {layer2_width:.1f}x{layer2_height:.1f}")
```

### Geometric Scaling

```python
from column_drawing.calculators.geometry_calculator import GeometryCalculator

# Initialize geometry calculator
geom_calc = GeometryCalculator(config)

# Calculate scale factor to fit 600x700mm column in 200x250mm cell
scale = geom_calc.calculate_scale_factor(
    content_width=600, content_height=700,
    available_width=200, available_height=250,
    margin=10
)

print(f"Optimal scale factor: {scale:.3f}")

# Transform coordinates
original_points = [(0, 0), (600, 0), (600, 700), (0, 700)]
scaled_points = geom_calc.transform_coordinates(
    original_points, scale=scale, offset_x=50, offset_y=50
)

print("Scaled coordinates:")
for i, (x, y) in enumerate(scaled_points):
    print(f"Point {i+1}: ({x:.1f}, {y:.1f})")
```

### Validation Example

```python
from column_drawing.models.column_config import ColumnConfig

# Create column configuration
column_config = ColumnConfig(
    floor="1F", name="C1", B=600, D=700, cover=50,
    dia1=40, num_x1=3, num_y1=5,
    dia2=32, num_x2=2, num_y2=3,
    dia_links=12, num_legs_x=2, num_legs_y=4,
    spacing_typical=200, spacing_critical=100, critical_height=700
)

# Validate rebar layout
is_valid = calc.validate_rebar_layout(column_config)
if is_valid:
    print("Rebar layout is valid")
else:
    print("Rebar layout validation failed - check logs for details")
```

## 🎯 Integration Points

### With Drawing Components

- **SectionDrawer**: Uses calculated rebar positions for drawing rebar patterns
- **DimensionDrawer**: Uses geometric calculations for dimension positioning
- **TableDrawer**: Uses scaling calculations for fitting content in table cells

### With Data Models

- **ColumnConfig**: Provides input specifications for all calculations
- **DrawingConfig**: Provides configuration parameters and tolerances
- **RebarLayerData**: Stores calculated results for use by drawing components

### With Validation Systems

- **Input Validation**: Validates column dimensions and rebar specifications
- **Geometric Validation**: Ensures calculated positions are within valid ranges
- **Engineering Validation**: Checks compliance with structural engineering requirements

## 📊 Engineering Standards Compliance

### Rebar Positioning Standards

- **Cover Distance**: Proper calculation ensuring rebar center = edge + cover + radius
- **Minimum Spacing**: Ensures adequate spacing between rebars for concrete placement
- **Corner Treatment**: Avoids duplicate rebars at corners for accurate material quantities
- **Layer Alignment**: Aligns inner layer rebars with outer layer for structural integrity

### Geometric Precision

- **Coordinate Accuracy**: Millimeter-precision calculations for professional drawings
- **Scaling Consistency**: Maintains aspect ratios and proportional relationships
- **Bounds Checking**: Ensures all elements fit within designated drawing areas
- **Transformation Accuracy**: Precise coordinate transformations for multi-scale drawings

### Validation Criteria

- **Minimum Column Size**: Ensures column dimensions can accommodate specified rebar
- **Layer Feasibility**: Validates that Layer 2 can fit within Layer 1 bounds
- **Spacing Requirements**: Checks minimum spacing requirements for rebar placement
- **Structural Constraints**: Validates against structural engineering requirements

## 🔍 Algorithm Details

### Perimeter Rebar Placement Algorithm

```python
# Pseudocode for perimeter placement
def calculate_perimeter_positions(x, y, width, height, num_x, num_y):
    positions = []

    # Top edge: num_x rebars evenly spaced
    for i in range(num_x):
        pos_x = x + (i * width / (num_x - 1))
        positions.append((pos_x, y + height))

    # Bottom edge: num_x rebars evenly spaced
    for i in range(num_x):
        pos_x = x + (i * width / (num_x - 1))
        positions.append((pos_x, y))

    # Left/Right edges: intermediate rebars only (exclude corners)
    for i in range(1, num_y - 1):
        pos_y = y + (i * height / (num_y - 1))
        positions.append((x, pos_y))          # Left edge
        positions.append((x + width, pos_y))  # Right edge

    return positions
```

### Layer 2 Bounds Calculation

```python
# Pseudocode for Layer 2 bounds
def calculate_layer2_bounds(layer1_x, layer1_y, layer1_width, layer1_height, num_x1, num_y1, dia2):
    # Calculate spacing between Layer 1 rebars
    spacing_x = layer1_width / (num_x1 - 1) if num_x1 > 1 else 0
    spacing_y = layer1_height / (num_y1 - 1) if num_y1 > 1 else 0

    # Layer 2 bounds based on second rebar positions
    layer2_x = layer1_x + spacing_x - dia2/2
    layer2_y = layer1_y + spacing_y - dia2/2
    layer2_width = layer1_width - 2*spacing_x + dia2
    layer2_height = layer1_height - 2*spacing_y + dia2

    return layer2_x, layer2_y, layer2_width, layer2_height
```

## 🐛 Error Handling

### Common Validation Errors

- **Invalid Dimensions**: Column dimensions too small for specified rebar
- **Insufficient Spacing**: Rebar spacing below minimum requirements
- **Layer Conflicts**: Layer 2 cannot fit within Layer 1 bounds
- **Geometric Constraints**: Calculated positions outside valid ranges

### Error Recovery

- **Graceful Degradation**: Returns empty lists for invalid configurations
- **Detailed Logging**: Provides specific error messages for debugging
- **Fallback Values**: Uses safe defaults when calculations fail
- **Validation Feedback**: Clear indication of validation failures with corrective guidance

## 🔧 Performance Considerations

### Optimization Strategies

- **Efficient Algorithms**: O(n) complexity for most calculations
- **Minimal Memory Usage**: Calculations performed in-place where possible
- **Caching**: Repeated calculations cached for improved performance
- **Lazy Evaluation**: Complex calculations deferred until needed

### Scalability

- **Batch Processing**: Designed to handle multiple columns efficiently
- **Memory Management**: Proper cleanup of intermediate calculations
- **Resource Usage**: Minimal CPU and memory footprint
- **Concurrent Processing**: Thread-safe implementations for parallel processing
