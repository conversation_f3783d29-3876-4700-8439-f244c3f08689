"""
Parsers Package
==============

Contains file parsing and validation components for the General Notes Drawing Generator.

This package provides parsers for:
- Excel summary files with note metadata
- DXF database files with note details
- Coordinate string parsing and validation
- Input validation and error reporting

Modules:
    - excel_parser: Excel file parsing with pandas/openpyxl
    - dxf_parser: DXF database loading using ezdxf
    - coordinate_parser: Parse "(x, y, z)" format strings
    - validation: Input validation and error reporting
"""

from .excel_parser import ExcelParser, ExcelParsingError
from .dxf_parser import DXFParser, DXFParsingError
from .coordinate_parser import CoordinateParser, CoordinateParsingError
from .validation import (
    ValidationResult,
    ValidationError,
    FileValidator,
    DataValidator,
    validate_excel_structure,
    validate_dxf_file,
    validate_coordinate_string
)

__all__ = [
    # Excel parsing
    'ExcelParser',
    'ExcelParsingError',
    
    # DXF parsing
    'DXFParser', 
    'DXFParsingError',
    
    # Coordinate parsing
    'CoordinateParser',
    'CoordinateParsingError',
    
    # Validation
    'ValidationResult',
    'ValidationError',
    'FileValidator',
    'DataValidator',
    'validate_excel_structure',
    'validate_dxf_file',
    'validate_coordinate_string'
]
