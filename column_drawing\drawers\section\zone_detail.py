"""
Zone Detail Operations
=====================

Zone detail drawing functions for section operations including zone reinforcement layers,
zone links, dimensions, labels, and scale calculations.
"""

import logging
from typing import Tuple, Optional, Dict
from ezdxf.enums import TextEntityAlignment
from ezdxf.tools.text import MTextEditor
from ...models.drawing_config import DrawingConfig
from ...models.column_config import ColumnConfig
from ...models.zone_config import ZoneConfig
from ...models.rebar_layer_data import RebarLayerData
from ...models.zone_detail_config import get_zone_detail_config
from ...calculators.geometry_calculator import GeometryCalculator

logger = logging.getLogger(__name__)


class ZoneDetailScaleCalculator:
    """
    Handles scale calculations for zone detail drawings.

    Implements real scale (1:1) approach with automatic fitting algorithm.
    """

    def __init__(self, zone_config):
        self.zone_config = zone_config

    def calculate_optimal_scale(
        self,
        column_config: ColumnConfig,
        detail_area_width: float,
        detail_area_height: float,
        zone_id: str,
        geometry_calculator: GeometryCalculator
    ) -> float:
        """
        Calculate optimal scale for zone detail using real scale (1:1) approach.

        Algorithm:
        1. Starts with real scale (1:1) as preferred
        2. Checks if it fits within zone cell boundaries
        3. If not, automatically scales down to ensure containment
        4. Applies minimum scale limits for readability

        Args:
            column_config: Column configuration
            detail_area_width: Available width for detail drawing
            detail_area_height: Available height for detail drawing  
            zone_id: Zone identifier for logging
            geometry_calculator: Geometry calculator instance

        Returns:
            float: Optimal scale factor for zone detail
        """
        try:
            scale_params = self.zone_config.get_scale_parameters()
            real_scale = scale_params['real_scale']

            # Calculate dimensions at real scale
            real_scale_width = column_config.B * real_scale
            real_scale_height = column_config.D * real_scale

            self._log_scale_analysis(zone_id, column_config, detail_area_width,
                                     detail_area_height, real_scale_width, real_scale_height)

            # Check if real scale fits
            if self._scale_fits_boundaries(real_scale_width, real_scale_height,
                                           detail_area_width, detail_area_height):
                logger.debug(
                    f"Zone {zone_id}: Real scale (1:1) fits perfectly")
                return real_scale

            # Calculate scale down factor
            return self._calculate_scale_down_factor(
                column_config, detail_area_width, detail_area_height,
                zone_id, geometry_calculator, scale_params
            )

        except Exception as e:
            logger.error(
                f"Error calculating zone detail scale for zone {zone_id}: {e}")
            return self.zone_config.get_scale_parameters()['min_scale']

    def _scale_fits_boundaries(self, width: float, height: float,
                               max_width: float, max_height: float) -> bool:
        """Check if dimensions fit within boundaries."""
        return width <= max_width and height <= max_height

    def _log_scale_analysis(self, zone_id: str, column_config: ColumnConfig,
                            detail_area_width: float, detail_area_height: float,
                            real_scale_width: float, real_scale_height: float) -> None:
        """Log scale analysis information (consolidated)."""
        # Only log if there are scaling issues
        if real_scale_width > detail_area_width or real_scale_height > detail_area_height:
            logger.warning(
                f"Zone {zone_id}: Column {column_config.B}×{column_config.D}mm exceeds available area {detail_area_width:.0f}×{detail_area_height:.0f}mm")

    def _calculate_scale_down_factor(self, column_config: ColumnConfig,
                                     detail_area_width: float, detail_area_height: float,
                                     zone_id: str, geometry_calculator: GeometryCalculator,
                                     scale_params: Dict[str, float]) -> float:
        """Calculate and validate scale down factor."""
        logger.debug(
            f"Zone {zone_id}: Real scale exceeds boundaries - calculating scale down factor")

        # Calculate optimal scale
        scale_down_factor = geometry_calculator.calculate_optimal_scale(
            column_config.B, column_config.D,
            detail_area_width, detail_area_height,
            margin=0  # No additional margin since we already have zone detail margin
        )

        # Apply constraints
        scale_down_factor = min(scale_down_factor, scale_params['max_scale'])
        final_scale = max(scale_params['min_scale'], scale_down_factor)

        self._log_scale_result(zone_id, column_config, final_scale,
                               detail_area_width, detail_area_height, scale_params)

        return final_scale

    def _log_scale_result(self, zone_id: str, column_config: ColumnConfig,
                          final_scale: float, detail_area_width: float,
                          detail_area_height: float, scale_params: Dict[str, float]) -> None:
        """Log the final scale calculation result (consolidated)."""
        # Only log if significant scaling was applied
        if final_scale < 0.8:
            final_width = column_config.B * final_scale
            final_height = column_config.D * final_scale
            logger.info(
                f"Zone {zone_id}: Scaled to {final_scale:.2f} ({final_width:.0f}×{final_height:.0f}mm)")

        if final_scale == scale_params['min_scale']:
            logger.warning(f"Zone {zone_id}: Using minimum scale {final_scale:.3f} - "
                           f"column may be too large for zone cell")


class ZoneDetailPositionCalculator:
    """
    Handles positioning calculations for zone detail drawings.
    """

    def __init__(self, zone_config):
        self.zone_config = zone_config

    def calculate_detail_position(self, zone_bounds: dict, scaled_width: float,
                                  scaled_height: float) -> Tuple[float, float]:
        """
        Calculate the position for zone detail drawing.

        Args:
            zone_bounds: Zone cell bounds
            scaled_width: Scaled column width
            scaled_height: Scaled column height

        Returns:
            Tuple[float, float]: (detail_x, detail_y) coordinates
        """
        offsets = self.zone_config.get_positioning_offsets()

        # Center the detail in the available area
        detail_center_x = zone_bounds['detail_center_x']
        detail_center_y = zone_bounds['detail_center_y'] - \
            offsets['vertical_centering']

        # Calculate detail position (bottom-left corner) from center
        detail_x = detail_center_x - scaled_width / 2
        detail_y = detail_center_y + scaled_height / 2  # Y increases upward from center

        return detail_x, detail_y

    def calculate_lowest_point(self, detail_x: float, detail_y: float,
                               scaled_width: float, scaled_height: float,
                               scale: float) -> float:
        """
        Calculate the lowest Y coordinate of all zone detail drawing elements.

        Includes column outline, vertical arrow tails, and link marks.

        Args:
            detail_x: Column detail X position
            detail_y: Column detail Y position  
            scaled_width: Scaled column width
            scaled_height: Scaled column height
            scale: Scale factor applied

        Returns:
            float: Lowest Y coordinate of all drawing elements
        """
        try:
            offsets = self.zone_config.get_positioning_offsets(scale)

            # Column outline bottom edge
            column_bottom = detail_y

            # Calculate successive offsets
            vertical_arrow_tail_y = column_bottom - offsets['arrow_tail']
            vertical_link_mark_y = vertical_arrow_tail_y - offsets['link_mark']

            logger.debug(f"Zone detail lowest point calculation:")
            logger.debug(f"  Column bottom: {column_bottom:.1f}")
            logger.debug(
                f"  Vertical arrow tail Y: {vertical_arrow_tail_y:.1f}")
            logger.debug(f"  Vertical link mark Y: {vertical_link_mark_y:.1f}")

            return vertical_link_mark_y

        except Exception as e:
            logger.error(f"Error calculating zone detail lowest point: {e}")
            return detail_y


class ZoneDetailTextRenderer:
    """
    Handles text rendering for zone detail drawings.
    """

    def __init__(self, zone_config, msp, dxf_writer, config):
        self.zone_config = zone_config
        self.msp = msp
        self.dxf_writer = dxf_writer
        self.config = config

    def add_zone_label(self, zone_config: ZoneConfig, zone_bounds: dict,
                       lowest_point_y: float, column_config: ColumnConfig) -> None:
        """
        Add zone label text below the lowest point with underline formatting.

        Args:
            zone_config: Zone configuration
            zone_bounds: Zone cell bounds
            lowest_point_y: Y coordinate of lowest drawing elements
            column_config: Column configuration for floor information
        """
        try:
            text_layer = self._get_text_layer()
            label_x = zone_bounds['detail_center_x']
            label_y = self._calculate_label_position(lowest_point_y)
            zone_text = self._get_zone_display_text(zone_config, column_config)

            self._create_underlined_text(
                zone_text, label_x, label_y, text_layer)

            logger.debug(
                f"Added zone {zone_config.zone_id} label at ({label_x:.1f}, {label_y:.1f})")

        except Exception as e:
            logger.error(f"Error adding zone label: {e}")
            raise

    def _get_text_layer(self) -> str:
        """Get appropriate layer for zone labels."""
        layers = self.zone_config.get_layer_configuration()
        text_layer = layers['text']

        if self.dxf_writer:
            text_layer = self.dxf_writer.get_layer_for_element("table_text")
            self.dxf_writer.ensure_layer_exists(text_layer)

        return text_layer

    def _calculate_label_position(self, lowest_point_y: float) -> float:
        """Calculate Y position for zone label."""
        offsets = self.zone_config.get_positioning_offsets()
        return lowest_point_y - offsets['label_spacing']

    def _get_zone_display_text(self, zone_config: ZoneConfig, column_config: ColumnConfig) -> str:
        """Get the display text for zone label with Zone C similarity note."""
        from ...models.zone_config import get_zone_display_name

        start_floor_type = ""
        if hasattr(column_config, 'start_floor_name') and column_config.start_floor_name:
            start_floor_type = column_config.start_floor_name
        elif hasattr(column_config, 'floor') and column_config.floor:
            start_floor_type = column_config.floor

        main_text = get_zone_display_name(
            zone_config.zone_id, start_floor_type)

        # Add "ZONE C SIMILAR" subtitle for Zone A after merging
        if zone_config.zone_id == 'A':
            return f"{main_text}\nZONE C SIMILAR"

        return main_text

    def _create_underlined_text(self, text: str, x: float, y: float, layer: str) -> None:
        """Create underlined MTEXT entity with support for multi-line text."""
        text_config = self.zone_config.get_text_configuration()

        # Handle multi-line text (e.g., "ZONE A\nZONE C SIMILAR")
        lines = text.split('\n')

        for i, line in enumerate(lines):
            # Calculate vertical offset for each line with increased gap
            # Increased from 1.2 to 1.6 for larger gap
            line_y = y - (i * text_config['label_height'] * 1.6)

            mtext_editor = MTextEditor()
            mtext_editor.underline(line)

            mtext = self.msp.add_mtext(
                str(mtext_editor),
                dxfattribs={
                    'layer': layer,
                    'color': text_config['color'],
                    'style': text_config['style'],
                    # Same height for both lines
                    'char_height': text_config['label_height'],
                    'attachment_point': 5  # Middle center
                }
            )

            mtext.set_location(
                insert=(x, line_y),
                rotation=0,
                attachment_point=5
            )


class ZoneDetailMixin:
    """
    Mixin class providing zone detail operations for section drawing.

    This mixin handles:
    - Drawing complete zone details with automatic scaling
    - Zone reinforcement layer drawing  
    - Zone-specific link/stirrup drawing
    - Dimension drawing with adaptive text sizing
    - Zone label rendering with underline formatting
    - Coordinate positioning and bounds calculation
    """

    def __init__(self):
        """Initialize zone detail components."""
        self.zone_detail_config = get_zone_detail_config()
        self._scale_calculator = ZoneDetailScaleCalculator(
            self.zone_detail_config)
        self._position_calculator = ZoneDetailPositionCalculator(
            self.zone_detail_config)
        self._text_renderer = None  # Initialize later when msp/dxf_writer available

    def draw_zone_detail(
        self,
        column_config: ColumnConfig,
        zone_config: ZoneConfig,
        zone_bounds: dict,
        geometry_calculator: GeometryCalculator
    ) -> Tuple[dict, RebarLayerData]:
        """
        Draw a complete column detail for a specific zone within bounds.

        Args:
            column_config: Column configuration data
            zone_config: Zone-specific configuration
            zone_bounds: Zone cell bounds from DrawingConfig.get_zone_cell_bounds()
            geometry_calculator: Geometry calculator for scaling and positioning

        Returns:
            Tuple[dict, RebarLayerData]: (detail_bounds, rebar_data)
        """
        try:
            logger.debug(
                f"Drawing zone {zone_config.zone_id} detail in bounds: {zone_bounds}")

            # Calculate optimal scale
            scale = self._calculate_zone_scale(
                column_config, zone_bounds, zone_config.zone_id, geometry_calculator
            )

            # Calculate positions and dimensions
            detail_x, detail_y, scaled_width, scaled_height = self._calculate_zone_layout(
                column_config, zone_bounds, scale
            )

            # Draw zone components
            rebar_data = self._draw_zone_components(
                column_config, zone_config, detail_x, detail_y,
                scaled_width, scaled_height, scale
            )

            # Add zone label
            lowest_point_y = self._position_calculator.calculate_lowest_point(
                detail_x, detail_y, scaled_width, scaled_height, scale
            )
            self._add_zone_label(zone_config, zone_bounds,
                                 lowest_point_y, column_config)

            # Return detail bounds
            detail_bounds = {
                'x': detail_x, 'y': detail_y,
                'width': scaled_width, 'height': scaled_height,
                'scale': scale, 'lowest_point_y': lowest_point_y
            }

            logger.debug(
                f"Successfully drew zone {zone_config.zone_id} detail")
            return detail_bounds, rebar_data

        except Exception as e:
            logger.error(
                f"Error drawing zone {zone_config.zone_id} detail: {e}")
            raise

    def _calculate_zone_scale(self, column_config: ColumnConfig, zone_bounds: dict,
                              zone_id: str, geometry_calculator: GeometryCalculator) -> float:
        """Calculate optimal scale for zone detail."""
        detail_area_width = zone_bounds['detail_area_width']
        detail_area_height = zone_bounds['detail_area_height']

        return self._scale_calculator.calculate_optimal_scale(
            column_config, detail_area_width, detail_area_height,
            zone_id, geometry_calculator
        )

    def _calculate_zone_layout(self, column_config: ColumnConfig, zone_bounds: dict,
                               scale: float) -> Tuple[float, float, float, float]:
        """Calculate zone layout dimensions and position."""
        scaled_width = column_config.B * scale
        scaled_height = column_config.D * scale

        detail_x, detail_y = self._position_calculator.calculate_detail_position(
            zone_bounds, scaled_width, scaled_height
        )

        self._log_coordinate_transformation(zone_bounds, detail_x, detail_y,
                                            scaled_width, scaled_height, scale)

        return detail_x, detail_y, scaled_width, scaled_height

    def _draw_zone_components(self, column_config: ColumnConfig, zone_config: ZoneConfig,
                              detail_x: float, detail_y: float, scaled_width: float,
                              scaled_height: float, scale: float) -> RebarLayerData:
        """Draw all zone detail components."""
        # Draw column outline
        self.draw_column_outline(
            detail_x, detail_y, scaled_width, scaled_height)

        # Draw reinforcement layers
        rebar_data = self._draw_zone_reinforcement_layers(
            column_config, detail_x, detail_y, scaled_width, scaled_height, scale
        )

        # Draw links and arrows
        self._draw_zone_links(column_config, zone_config, rebar_data, scale)
        self._draw_link_intersection_arrows(
            column_config, zone_config, rebar_data, scale,
            detail_x, detail_y, scaled_width, scaled_height
        )

        # Draw dimensions
        self._draw_zone_dimensions(
            detail_x, detail_y, scaled_width, scaled_height,
            column_config.B, column_config.D, scale
        )

        return rebar_data

    def _log_coordinate_transformation(self, zone_bounds: dict, detail_x: float,
                                       detail_y: float, scaled_width: float,
                                       scaled_height: float, scale: float) -> None:
        """Log coordinate transformation details (consolidated)."""
        from ...utils.logging_config import log_reminder

        # Only log if there are positioning issues or significant scaling
        if scale < 0.5 or detail_x < zone_bounds['x'] or detail_y < zone_bounds['y']:
            log_reminder(
                logger,
                "zone_positioning",
                f"Zone positioning: scale={scale:.2f}, position=({detail_x:.0f},{detail_y:.0f}) - this is normal behavior during layout calculation"
            )

    def _draw_zone_reinforcement_layers(
        self,
        column_config: ColumnConfig,
        section_x: float,
        section_y: float,
        section_width: float,
        section_height: float,
        scale: float
    ) -> RebarLayerData:
        """
        Draw reinforcement layers for zone detail at specified scale.

        Args:
            column_config: Column configuration
            section_x, section_y: Section position
            section_width, section_height: Scaled section dimensions
            scale: Scale factor applied

        Returns:
            RebarLayerData: Organized rebar data
        """
        try:
            # Calculate scaled dimensions
            layer1_bounds, layer1_positions = self._draw_layer1_reinforcement(
                column_config, section_x, section_y, section_width, section_height, scale
            )

            # Draw Layer 2 if present
            layer2_bounds, layer2_positions = self._draw_layer2_reinforcement(
                column_config, layer1_bounds, layer1_positions, scale
            )

            # Create organized rebar data
            return RebarLayerData.create_from_column_config(
                column_config=column_config,
                layer1_positions=layer1_positions,
                layer1_bounds=layer1_bounds,
                layer1_color=self.config.get_rebar_colors()['layer1'],
                layer2_positions=layer2_positions,
                layer2_bounds=layer2_bounds,
                layer2_color=self.config.get_rebar_colors(
                )['layer2'] if layer2_positions else None
            )

        except Exception as e:
            logger.error(f"Error drawing zone reinforcement layers: {e}")
            raise

    def _draw_layer1_reinforcement(self, column_config: ColumnConfig,
                                   section_x: float, section_y: float,
                                   section_width: float, section_height: float,
                                   scale: float) -> Tuple[tuple, list]:
        """Draw Layer 1 reinforcement and return bounds and positions."""
        actual_cover = column_config.cover * scale
        actual_dia1 = column_config.dia1 * scale

        layer1_radius = actual_dia1 / 2
        layer1_distance = actual_cover + layer1_radius

        layer1_x = section_x + layer1_distance
        layer1_y = section_y + layer1_distance
        layer1_width = section_width - 2 * layer1_distance
        layer1_height = section_height - 2 * layer1_distance

        layer1_positions = self.draw_rebar_layer(
            layer1_x, layer1_y, layer1_width, layer1_height,
            column_config.num_x1, column_config.num_y1,
            actual_dia1,
            self.config.get_rebar_colors()['layer1']
        )

        return (layer1_x, layer1_y, layer1_width, layer1_height), layer1_positions

    def _draw_layer2_reinforcement(self, column_config: ColumnConfig,
                                   layer1_bounds: tuple, layer1_positions: list,
                                   scale: float) -> Tuple[Optional[tuple], list]:
        """Draw Layer 2 reinforcement if present."""
        if not column_config.has_layer2():
            return None, []

        # Get constrained counts for Layer 2
        constrained_x2, constrained_y2, warnings = column_config.get_constrained_layer2_counts()

        if constrained_x2 <= 0 or constrained_y2 <= 0:
            return None, []

        actual_dia2 = column_config.dia2 * scale
        layer1_x, layer1_y, layer1_width, layer1_height = layer1_bounds

        # Calculate Layer 2 bounds
        layer2_x, layer2_y, layer2_width, layer2_height = \
            self.rebar_calc.calculate_layer2_bounds(
                layer1_x, layer1_y, layer1_width, layer1_height,
                column_config.num_x1, column_config.num_y1,
                actual_dia2
            )

        # Draw Layer 2 with alignment
        layer2_positions = self.draw_rebar_layer_aligned(
            layer2_x, layer2_y, layer2_width, layer2_height,
            constrained_x2, constrained_y2,
            actual_dia2,
            self.config.get_rebar_colors()['layer2'],
            layer1_positions
        )

        return (layer2_x, layer2_y, layer2_width, layer2_height), layer2_positions

    def _draw_zone_links(
        self,
        column_config: ColumnConfig,
        zone_config: ZoneConfig,
        rebar_data: RebarLayerData,
        scale: float
    ) -> None:
        """
        Draw zone-specific links/stirrups.

        Args:
            column_config: Column configuration
            zone_config: Zone-specific configuration
            rebar_data: Organized rebar data
            scale: Scale factor applied
        """
        try:
            layer1_bounds = rebar_data.layer1.bounds
            layer1_x, layer1_y, layer1_width, layer1_height = layer1_bounds

            # Scale zone-specific link parameters
            scaled_link_diameter = zone_config.outer_link_diameter * scale
            scaled_rebar_diameter = column_config.dia1 * scale

            # Draw links using zone-specific parameters
            self.draw_links(
                layer1_x, layer1_y, layer1_width, layer1_height,
                zone_config.link_legs_x, zone_config.link_legs_y,
                self.config.get_rebar_colors()['links'],
                layer1_radius=scaled_rebar_diameter / 2,
                link_radius=scaled_link_diameter / 2,
                actual_rebar_positions=rebar_data.layer1.positions,
                rebar_diameter=scaled_rebar_diameter,
                link_diameter=scaled_link_diameter,
                use_25a_links=True
            )

            logger.debug(f"Drew zone {zone_config.zone_id} links: "
                         f"Ø{zone_config.outer_link_diameter}mm, "
                         f"{zone_config.link_legs_x}×{zone_config.link_legs_y} legs")

        except Exception as e:
            logger.error(f"Error drawing zone links: {e}")
            raise

    def _draw_zone_dimensions(
        self,
        x: float,
        y: float,
        width: float,
        height: float,
        actual_width: float,
        actual_height: float,
        scale: float = 1.0
    ) -> None:
        """
        Draw dimensions for zone detail with scale-adaptive text sizing.

        Args:
            x, y: Section bottom-left corner coordinates
            width, height: Scaled section dimensions
            actual_width, actual_height: Actual column dimensions (mm)
            scale: Current scale factor for adaptive text sizing
        """
        original_height = self.config.DIMENSION_TEXT_HEIGHT

        try:
            # Calculate adaptive dimension text height
            adaptive_text_height = self.zone_detail_config.calculate_adaptive_text_height(
                scale)

            # Set adaptive dimension text height
            self.config.DIMENSION_TEXT_HEIGHT = adaptive_text_height

            logger.debug(
                f"Zone dimensions: scale={scale:.3f}, text_height={adaptive_text_height:.1f}")

            # Draw dimensions
            success = self.dimension_drawer.draw_section_dimensions(
                x, y, width, height, actual_width, actual_height
            )

            if not success:
                logger.warning("Failed to draw zone detail dimensions")

        except Exception as e:
            logger.error(f"Error drawing zone dimensions: {e}")
        finally:
            # Always restore the original dimension text height
            self.config.DIMENSION_TEXT_HEIGHT = original_height

    def _add_zone_label(self, zone_config: ZoneConfig, zone_bounds: dict,
                        lowest_point_y: float, column_config: ColumnConfig) -> None:
        """Add zone label using text renderer."""
        if self._text_renderer is None:
            self._text_renderer = ZoneDetailTextRenderer(
                self.zone_detail_config, self.msp, self.dxf_writer, self.config
            )

        self._text_renderer.add_zone_label(
            zone_config, zone_bounds, lowest_point_y, column_config
        )

    # Backward compatibility methods - delegate to new implementation
    def _calculate_zone_detail_scale(self, *args, **kwargs) -> float:
        """Backward compatibility method - delegate to scale calculator."""
        return self._scale_calculator.calculate_optimal_scale(*args, **kwargs)

    def _calculate_zone_detail_lowest_point(self, *args, **kwargs) -> float:
        """Backward compatibility method - delegate to position calculator."""
        return self._position_calculator.calculate_lowest_point(*args, **kwargs)
