"""
Selection Models
===============

Data models for managing user selection state and criteria for note details.

This module provides classes for:
- User selection state management
- Selection criteria and filtering
- Selection history and undo/redo functionality
- Batch selection operations

Author: Drawing Production System
"""

from dataclasses import dataclass, field
from typing import List, Set, Optional, Dict, Any, Callable
from datetime import datetime
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class SelectionMode(Enum):
    """Enumeration of selection modes."""
    SINGLE = "single"
    MULTIPLE = "multiple"
    RANGE = "range"


class FilterType(Enum):
    """Enumeration of filter types."""
    SEARCH = "search"
    CATEGORY = "category"
    TAG = "tag"
    SIZE = "size"
    AREA = "area"
    CUSTOM = "custom"


@dataclass
class SelectionCriteria:
    """
    Criteria for filtering and selecting note details.
    
    Attributes:
        search_term: Text search term
        categories: List of categories to include
        tags: List of tags to include
        min_area: Minimum area threshold
        max_area: Maximum area threshold
        min_width: Minimum width threshold
        max_width: Maximum width threshold
        min_height: Minimum height threshold
        max_height: Maximum height threshold
        custom_filter: Custom filter function
        case_sensitive: Whether search is case sensitive
        exact_match: Whether to use exact matching
    """
    search_term: Optional[str] = None
    categories: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    min_area: Optional[float] = None
    max_area: Optional[float] = None
    min_width: Optional[float] = None
    max_width: Optional[float] = None
    min_height: Optional[float] = None
    max_height: Optional[float] = None
    custom_filter: Optional[Callable] = None
    case_sensitive: bool = False
    exact_match: bool = False
    
    def is_empty(self) -> bool:
        """
        Check if criteria is empty (no filters applied).
        
        Returns:
            True if no criteria are set
        """
        return (
            not self.search_term and
            not self.categories and
            not self.tags and
            self.min_area is None and
            self.max_area is None and
            self.min_width is None and
            self.max_width is None and
            self.min_height is None and
            self.max_height is None and
            self.custom_filter is None
        )
    
    def clear(self) -> None:
        """Clear all selection criteria."""
        self.search_term = None
        self.categories.clear()
        self.tags.clear()
        self.min_area = None
        self.max_area = None
        self.min_width = None
        self.max_width = None
        self.min_height = None
        self.max_height = None
        self.custom_filter = None
    
    def copy(self) -> 'SelectionCriteria':
        """
        Create a copy of the selection criteria.
        
        Returns:
            Copy of the selection criteria
        """
        return SelectionCriteria(
            search_term=self.search_term,
            categories=self.categories.copy(),
            tags=self.tags.copy(),
            min_area=self.min_area,
            max_area=self.max_area,
            min_width=self.min_width,
            max_width=self.max_width,
            min_height=self.min_height,
            max_height=self.max_height,
            custom_filter=self.custom_filter,
            case_sensitive=self.case_sensitive,
            exact_match=self.exact_match
        )


@dataclass
class SelectionSnapshot:
    """
    Snapshot of selection state for undo/redo functionality.
    
    Attributes:
        selected_titles: Set of selected note titles
        criteria: Selection criteria at time of snapshot
        timestamp: When the snapshot was created
        description: Optional description of the selection action
    """
    selected_titles: Set[str]
    criteria: SelectionCriteria
    timestamp: datetime = field(default_factory=datetime.now)
    description: Optional[str] = None
    
    def copy(self) -> 'SelectionSnapshot':
        """
        Create a copy of the selection snapshot.
        
        Returns:
            Copy of the selection snapshot
        """
        return SelectionSnapshot(
            selected_titles=self.selected_titles.copy(),
            criteria=self.criteria.copy(),
            timestamp=self.timestamp,
            description=self.description
        )


@dataclass
class SelectionState:
    """
    Manages the current selection state and history for note details.
    
    Provides functionality for:
    - Tracking selected note titles
    - Managing selection criteria and filters
    - Undo/redo functionality
    - Batch selection operations
    - Selection statistics and summary
    
    Attributes:
        selected_titles: Set of currently selected note titles
        criteria: Current selection criteria
        mode: Current selection mode
        history: List of selection snapshots for undo/redo
        history_index: Current position in history
        max_history_size: Maximum number of history entries
        auto_save_snapshots: Whether to automatically save snapshots
    """
    selected_titles: Set[str] = field(default_factory=set)
    criteria: SelectionCriteria = field(default_factory=SelectionCriteria)
    mode: SelectionMode = SelectionMode.MULTIPLE
    history: List[SelectionSnapshot] = field(default_factory=list)
    history_index: int = -1
    max_history_size: int = 50
    auto_save_snapshots: bool = True
    
    def __post_init__(self):
        """Initialize selection state."""
        if self.auto_save_snapshots:
            self._save_snapshot("Initial state")
    
    def select_note(self, title: str, description: Optional[str] = None) -> None:
        """
        Select a note by title.
        
        Args:
            title: Title of note to select
            description: Optional description for history
        """
        if self.mode == SelectionMode.SINGLE:
            self.selected_titles.clear()
        
        self.selected_titles.add(title)
        
        if self.auto_save_snapshots:
            self._save_snapshot(description or f"Selected '{title}'")
    
    def deselect_note(self, title: str, description: Optional[str] = None) -> None:
        """
        Deselect a note by title.
        
        Args:
            title: Title of note to deselect
            description: Optional description for history
        """
        self.selected_titles.discard(title)
        
        if self.auto_save_snapshots:
            self._save_snapshot(description or f"Deselected '{title}'")
    
    def toggle_note(self, title: str, description: Optional[str] = None) -> bool:
        """
        Toggle selection state of a note.
        
        Args:
            title: Title of note to toggle
            description: Optional description for history
            
        Returns:
            True if note is now selected, False if deselected
        """
        if title in self.selected_titles:
            self.deselect_note(title, description)
            return False
        else:
            self.select_note(title, description)
            return True
    
    def select_all(self, available_titles: List[str], description: Optional[str] = None) -> None:
        """
        Select all available notes.
        
        Args:
            available_titles: List of all available note titles
            description: Optional description for history
        """
        self.selected_titles.update(available_titles)
        
        if self.auto_save_snapshots:
            self._save_snapshot(description or "Selected all notes")
    
    def deselect_all(self, description: Optional[str] = None) -> None:
        """
        Deselect all notes.
        
        Args:
            description: Optional description for history
        """
        self.selected_titles.clear()
        
        if self.auto_save_snapshots:
            self._save_snapshot(description or "Deselected all notes")
    
    def select_by_criteria(self, note_titles: List[str], description: Optional[str] = None) -> None:
        """
        Select notes that match current criteria.
        
        Args:
            note_titles: List of note titles that match criteria
            description: Optional description for history
        """
        if self.mode == SelectionMode.SINGLE and note_titles:
            self.selected_titles = {note_titles[0]}
        else:
            self.selected_titles.update(note_titles)
        
        if self.auto_save_snapshots:
            self._save_snapshot(description or "Selected by criteria")
    
    def invert_selection(self, available_titles: List[str], description: Optional[str] = None) -> None:
        """
        Invert the current selection.
        
        Args:
            available_titles: List of all available note titles
            description: Optional description for history
        """
        all_titles = set(available_titles)
        self.selected_titles = all_titles - self.selected_titles
        
        if self.auto_save_snapshots:
            self._save_snapshot(description or "Inverted selection")
    
    def is_selected(self, title: str) -> bool:
        """
        Check if a note is selected.
        
        Args:
            title: Note title to check
            
        Returns:
            True if note is selected
        """
        return title in self.selected_titles
    
    def get_selection_count(self) -> int:
        """
        Get the number of selected notes.
        
        Returns:
            Number of selected notes
        """
        return len(self.selected_titles)
    
    def get_selected_list(self) -> List[str]:
        """
        Get selected titles as a sorted list.
        
        Returns:
            Sorted list of selected note titles
        """
        return sorted(list(self.selected_titles))
    
    def _save_snapshot(self, description: Optional[str] = None) -> None:
        """
        Save current state as a snapshot for undo/redo.
        
        Args:
            description: Optional description of the action
        """
        snapshot = SelectionSnapshot(
            selected_titles=self.selected_titles.copy(),
            criteria=self.criteria.copy(),
            description=description
        )
        
        # Remove any history after current index
        if self.history_index < len(self.history) - 1:
            self.history = self.history[:self.history_index + 1]
        
        # Add new snapshot
        self.history.append(snapshot)
        self.history_index = len(self.history) - 1
        
        # Limit history size
        if len(self.history) > self.max_history_size:
            self.history.pop(0)
            self.history_index -= 1
    
    def can_undo(self) -> bool:
        """
        Check if undo is possible.
        
        Returns:
            True if undo is possible
        """
        return self.history_index > 0
    
    def can_redo(self) -> bool:
        """
        Check if redo is possible.
        
        Returns:
            True if redo is possible
        """
        return self.history_index < len(self.history) - 1
    
    def undo(self) -> bool:
        """
        Undo the last selection action.
        
        Returns:
            True if undo was performed
        """
        if not self.can_undo():
            return False
        
        self.history_index -= 1
        snapshot = self.history[self.history_index]
        
        # Restore state without saving new snapshot
        old_auto_save = self.auto_save_snapshots
        self.auto_save_snapshots = False
        
        self.selected_titles = snapshot.selected_titles.copy()
        self.criteria = snapshot.criteria.copy()
        
        self.auto_save_snapshots = old_auto_save
        
        logger.info(f"Undid action: {snapshot.description}")
        return True
    
    def redo(self) -> bool:
        """
        Redo the next selection action.
        
        Returns:
            True if redo was performed
        """
        if not self.can_redo():
            return False
        
        self.history_index += 1
        snapshot = self.history[self.history_index]
        
        # Restore state without saving new snapshot
        old_auto_save = self.auto_save_snapshots
        self.auto_save_snapshots = False
        
        self.selected_titles = snapshot.selected_titles.copy()
        self.criteria = snapshot.criteria.copy()
        
        self.auto_save_snapshots = old_auto_save
        
        logger.info(f"Redid action: {snapshot.description}")
        return True
    
    def get_history_summary(self) -> List[Dict[str, Any]]:
        """
        Get summary of selection history.
        
        Returns:
            List of history entries with metadata
        """
        summary = []
        for i, snapshot in enumerate(self.history):
            summary.append({
                'index': i,
                'description': snapshot.description,
                'timestamp': snapshot.timestamp,
                'selected_count': len(snapshot.selected_titles),
                'is_current': i == self.history_index
            })
        return summary
    
    def clear_history(self) -> None:
        """Clear selection history."""
        self.history.clear()
        self.history_index = -1
        
        if self.auto_save_snapshots:
            self._save_snapshot("History cleared")
    
    def get_selection_summary(self) -> Dict[str, Any]:
        """
        Get summary of current selection state.
        
        Returns:
            Dictionary with selection statistics
        """
        return {
            'selected_count': len(self.selected_titles),
            'selected_titles': self.get_selected_list(),
            'mode': self.mode.value,
            'has_criteria': not self.criteria.is_empty(),
            'can_undo': self.can_undo(),
            'can_redo': self.can_redo(),
            'history_size': len(self.history),
            'history_index': self.history_index
        }
