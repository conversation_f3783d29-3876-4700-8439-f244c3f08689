"""
Unit Tests for Models
====================

Tests for data models and configuration classes.

Author: Drawing Production System
"""

import unittest
from datetime import datetime

from ..models.boundary import BoundaryPoint, Boundary, BoundaryValidationError
from ..models.note_detail import NoteDetail, NoteDetailCollection
from ..models.configuration import GeneralNotesConfig, DatabaseConfig, LayoutConfig
from ..models.selection import SelectionState, SelectionCriteria


class TestBoundaryPoint(unittest.TestCase):
    """Test BoundaryPoint class."""
    
    def test_creation(self):
        """Test BoundaryPoint creation."""
        point = BoundaryPoint(100.0, 200.0, 0.0)
        self.assertEqual(point.x, 100.0)
        self.assertEqual(point.y, 200.0)
        self.assertEqual(point.z, 0.0)
    
    def test_from_string(self):
        """Test BoundaryPoint creation from string."""
        point = BoundaryPoint.from_string("(100, 200, 0)")
        self.assertEqual(point.x, 100.0)
        self.assertEqual(point.y, 200.0)
        self.assertEqual(point.z, 0.0)
    
    def test_invalid_string(self):
        """Test invalid string handling."""
        with self.assertRaises(BoundaryValidationError):
            BoundaryPoint.from_string("invalid")
    
    def test_distance_calculation(self):
        """Test distance calculation between points."""
        point1 = BoundaryPoint(0, 0, 0)
        point2 = BoundaryPoint(3, 4, 0)
        distance = point1.distance_to(point2)
        self.assertEqual(distance, 5.0)


class TestBoundary(unittest.TestCase):
    """Test Boundary class."""
    
    def setUp(self):
        """Set up test boundary."""
        self.point1 = BoundaryPoint(0, 0, 0)      # Bottom-left
        self.point2 = BoundaryPoint(100, 0, 0)    # Bottom-right
        self.point3 = BoundaryPoint(100, 50, 0)   # Top-right
        self.point4 = BoundaryPoint(0, 50, 0)     # Top-left
        
        self.boundary = Boundary(
            self.point1, self.point2, self.point3, self.point4,
            title="Test Boundary"
        )
    
    def test_creation(self):
        """Test Boundary creation."""
        self.assertEqual(self.boundary.title, "Test Boundary")
        self.assertEqual(self.boundary.width, 100.0)
        self.assertEqual(self.boundary.height, 50.0)
        self.assertEqual(self.boundary.area, 5000.0)
    
    def test_center_calculation(self):
        """Test center point calculation."""
        center = self.boundary.center
        self.assertEqual(center.x, 50.0)
        self.assertEqual(center.y, 25.0)
    
    def test_contains_point(self):
        """Test point containment."""
        inside_point = BoundaryPoint(50, 25, 0)
        outside_point = BoundaryPoint(150, 25, 0)
        
        self.assertTrue(self.boundary.contains_point(inside_point))
        self.assertFalse(self.boundary.contains_point(outside_point))


class TestNoteDetail(unittest.TestCase):
    """Test NoteDetail class."""
    
    def setUp(self):
        """Set up test note detail."""
        boundary = Boundary(
            BoundaryPoint(0, 0, 0),
            BoundaryPoint(100, 0, 0),
            BoundaryPoint(100, 50, 0),
            BoundaryPoint(0, 50, 0),
            title="Test Note"
        )
        
        self.note = NoteDetail(
            title="Test Note",
            boundary=boundary,
            description="Test description",
            category="Test Category",
            tags=["tag1", "tag2"]
        )
    
    def test_creation(self):
        """Test NoteDetail creation."""
        self.assertEqual(self.note.title, "Test Note")
        self.assertEqual(self.note.description, "Test description")
        self.assertEqual(self.note.category, "Test Category")
        self.assertEqual(len(self.note.tags), 2)
        self.assertFalse(self.note.is_selected)
    
    def test_area_calculation(self):
        """Test area calculation."""
        self.assertEqual(self.note.area, 5000.0)
    
    def test_aspect_ratio(self):
        """Test aspect ratio calculation."""
        self.assertEqual(self.note.aspect_ratio, 2.0)
    
    def test_search_matching(self):
        """Test search functionality."""
        self.assertTrue(self.note.matches_search("Test"))
        self.assertTrue(self.note.matches_search("description"))
        self.assertTrue(self.note.matches_search("tag1"))
        self.assertFalse(self.note.matches_search("nonexistent"))
    
    def test_tag_management(self):
        """Test tag management."""
        self.note.add_tag("new_tag")
        self.assertIn("new_tag", self.note.tags)
        
        self.note.remove_tag("tag1")
        self.assertNotIn("tag1", self.note.tags)


class TestNoteDetailCollection(unittest.TestCase):
    """Test NoteDetailCollection class."""
    
    def setUp(self):
        """Set up test collection."""
        self.collection = NoteDetailCollection()
        
        # Add test notes
        for i in range(3):
            boundary = Boundary(
                BoundaryPoint(i*100, 0, 0),
                BoundaryPoint((i+1)*100, 0, 0),
                BoundaryPoint((i+1)*100, 50, 0),
                BoundaryPoint(i*100, 50, 0),
                title=f"Note {i+1}"
            )
            
            note = NoteDetail(
                title=f"Note {i+1}",
                boundary=boundary,
                category="Category A" if i < 2 else "Category B"
            )
            
            self.collection.add_note(note)
    
    def test_collection_operations(self):
        """Test basic collection operations."""
        self.assertEqual(len(self.collection), 3)
        
        note = self.collection.get_note_by_title("Note 1")
        self.assertIsNotNone(note)
        self.assertEqual(note.title, "Note 1")
        
        removed = self.collection.remove_note("Note 2")
        self.assertTrue(removed)
        self.assertEqual(len(self.collection), 2)
    
    def test_selection_operations(self):
        """Test selection operations."""
        self.collection.select_all()
        selected = self.collection.get_selected_notes()
        self.assertEqual(len(selected), 3)
        
        self.collection.deselect_all()
        selected = self.collection.get_selected_notes()
        self.assertEqual(len(selected), 0)
    
    def test_category_filtering(self):
        """Test category filtering."""
        category_a_notes = self.collection.get_notes_by_category("Category A")
        self.assertEqual(len(category_a_notes), 2)
        
        category_b_notes = self.collection.get_notes_by_category("Category B")
        self.assertEqual(len(category_b_notes), 1)


class TestConfiguration(unittest.TestCase):
    """Test configuration classes."""
    
    def test_database_config(self):
        """Test DatabaseConfig."""
        config = DatabaseConfig()
        self.assertTrue(config.backup_enabled)
        self.assertTrue(config.file_validation_enabled)
    
    def test_layout_config(self):
        """Test LayoutConfig."""
        config = LayoutConfig()
        self.assertEqual(config.modules_per_row, 2)
        self.assertEqual(config.modules_per_column, 2)
        self.assertEqual(config.max_modules_per_space, 4)
        
        # Test module position calculation
        pos = config.get_module_position(0)
        self.assertEqual(pos, (0.0, 0.0))
        
        pos = config.get_module_position(1)
        self.assertEqual(pos, (16900.0, 0.0))
    
    def test_general_notes_config(self):
        """Test GeneralNotesConfig."""
        config = GeneralNotesConfig.create_default()
        self.assertEqual(config.application_name, "General Notes Drawing Generator")
        self.assertEqual(config.version, "1.0.0")
        self.assertFalse(config.debug_mode)
        
        dev_config = GeneralNotesConfig.create_development()
        self.assertTrue(dev_config.debug_mode)
        self.assertEqual(dev_config.log_level, "DEBUG")


class TestSelectionState(unittest.TestCase):
    """Test SelectionState class."""
    
    def setUp(self):
        """Set up test selection state."""
        self.selection = SelectionState()
        self.available_notes = ["Note 1", "Note 2", "Note 3"]
    
    def test_selection_operations(self):
        """Test basic selection operations."""
        self.selection.select_note("Note 1")
        self.assertTrue(self.selection.is_selected("Note 1"))
        self.assertEqual(self.selection.get_selection_count(), 1)
        
        self.selection.deselect_note("Note 1")
        self.assertFalse(self.selection.is_selected("Note 1"))
        self.assertEqual(self.selection.get_selection_count(), 0)
    
    def test_toggle_selection(self):
        """Test toggle selection."""
        result = self.selection.toggle_note("Note 1")
        self.assertTrue(result)
        self.assertTrue(self.selection.is_selected("Note 1"))
        
        result = self.selection.toggle_note("Note 1")
        self.assertFalse(result)
        self.assertFalse(self.selection.is_selected("Note 1"))
    
    def test_select_all(self):
        """Test select all operation."""
        self.selection.select_all(self.available_notes)
        self.assertEqual(self.selection.get_selection_count(), 3)
        
        for note in self.available_notes:
            self.assertTrue(self.selection.is_selected(note))
    
    def test_undo_redo(self):
        """Test undo/redo functionality."""
        # Initial state
        self.assertFalse(self.selection.can_undo())
        self.assertFalse(self.selection.can_redo())
        
        # Make a selection
        self.selection.select_note("Note 1")
        self.assertTrue(self.selection.can_undo())
        
        # Undo
        self.selection.undo()
        self.assertFalse(self.selection.is_selected("Note 1"))
        self.assertTrue(self.selection.can_redo())
        
        # Redo
        self.selection.redo()
        self.assertTrue(self.selection.is_selected("Note 1"))


if __name__ == '__main__':
    unittest.main()
