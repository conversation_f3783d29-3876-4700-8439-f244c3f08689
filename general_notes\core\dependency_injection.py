"""
Dependency Injection
===================

Dependency injection container for the General Notes Drawing Generator.

This module provides centralized component initialization and dependency management:
- Component registration and resolution
- Lifecycle management
- Configuration injection
- Service location pattern

Author: Drawing Production System
"""

import logging
from typing import Dict, Any, Optional, Type, TypeVar, Callable
from abc import ABC, abstractmethod

from ..models.configuration import GeneralNotesConfig

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ComponentLifecycle:
    """Enumeration of component lifecycle types."""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"


class ComponentRegistration:
    """
    Registration information for a component.
    
    Attributes:
        component_type: Type of the component
        factory: Factory function to create the component
        lifecycle: Lifecycle management type
        dependencies: List of dependency names
        instance: Cached instance for singletons
    """
    
    def __init__(self, 
                 component_type: Type[T],
                 factory: Callable[..., T],
                 lifecycle: str = ComponentLifecycle.TRANSIENT,
                 dependencies: Optional[list] = None):
        """
        Initialize component registration.
        
        Args:
            component_type: Type of the component
            factory: Factory function to create instances
            lifecycle: Lifecycle management type
            dependencies: List of dependency names
        """
        self.component_type = component_type
        self.factory = factory
        self.lifecycle = lifecycle
        self.dependencies = dependencies or []
        self.instance: Optional[T] = None


class DependencyContainer:
    """
    Dependency injection container for managing application components.
    
    Provides registration, resolution, and lifecycle management for all
    application components following the dependency injection pattern.
    """
    
    def __init__(self, config: GeneralNotesConfig):
        """
        Initialize the dependency container.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self._registrations: Dict[str, ComponentRegistration] = {}
        self._instances: Dict[str, Any] = {}
        self._initialization_order: list = []
        
        # Register core components
        self._register_core_components()
        
        logger.debug("Dependency container initialized")
    
    def register(self, 
                name: str,
                component_type: Type[T],
                factory: Callable[..., T],
                lifecycle: str = ComponentLifecycle.TRANSIENT,
                dependencies: Optional[list] = None) -> None:
        """
        Register a component with the container.
        
        Args:
            name: Unique name for the component
            component_type: Type of the component
            factory: Factory function to create instances
            lifecycle: Lifecycle management type
            dependencies: List of dependency names
        """
        if name in self._registrations:
            logger.warning(f"Component '{name}' is already registered, overwriting")
        
        registration = ComponentRegistration(
            component_type=component_type,
            factory=factory,
            lifecycle=lifecycle,
            dependencies=dependencies
        )
        
        self._registrations[name] = registration
        logger.debug(f"Registered component '{name}' with lifecycle '{lifecycle}'")
    
    def register_singleton(self,
                          name: str,
                          component_type: Type[T],
                          factory: Callable[..., T],
                          dependencies: Optional[list] = None) -> None:
        """
        Register a singleton component.
        
        Args:
            name: Unique name for the component
            component_type: Type of the component
            factory: Factory function to create the instance
            dependencies: List of dependency names
        """
        self.register(name, component_type, factory, ComponentLifecycle.SINGLETON, dependencies)
    
    def register_transient(self,
                          name: str,
                          component_type: Type[T],
                          factory: Callable[..., T],
                          dependencies: Optional[list] = None) -> None:
        """
        Register a transient component.
        
        Args:
            name: Unique name for the component
            component_type: Type of the component
            factory: Factory function to create instances
            dependencies: List of dependency names
        """
        self.register(name, component_type, factory, ComponentLifecycle.TRANSIENT, dependencies)
    
    def resolve(self, name: str) -> Any:
        """
        Resolve a component by name.
        
        Args:
            name: Name of the component to resolve
            
        Returns:
            Component instance
            
        Raises:
            KeyError: If component is not registered
            Exception: If component creation fails
        """
        if name not in self._registrations:
            raise KeyError(f"Component '{name}' is not registered")
        
        registration = self._registrations[name]
        
        # Handle singleton lifecycle
        if registration.lifecycle == ComponentLifecycle.SINGLETON:
            if registration.instance is None:
                registration.instance = self._create_instance(registration)
            return registration.instance
        
        # Handle transient lifecycle
        elif registration.lifecycle == ComponentLifecycle.TRANSIENT:
            return self._create_instance(registration)
        
        # Handle scoped lifecycle (not implemented in this basic version)
        else:
            raise NotImplementedError(f"Lifecycle '{registration.lifecycle}' not implemented")
    
    def _create_instance(self, registration: ComponentRegistration) -> Any:
        """
        Create an instance of a component.
        
        Args:
            registration: Component registration
            
        Returns:
            Component instance
        """
        try:
            # Resolve dependencies
            dependency_instances = []
            for dep_name in registration.dependencies:
                dep_instance = self.resolve(dep_name)
                dependency_instances.append(dep_instance)
            
            # Create instance using factory
            if dependency_instances:
                instance = registration.factory(*dependency_instances)
            else:
                instance = registration.factory()
            
            logger.debug(f"Created instance of '{registration.component_type.__name__}'")
            return instance
            
        except Exception as e:
            logger.error(f"Failed to create instance of '{registration.component_type.__name__}': {str(e)}")
            raise
    
    def _register_core_components(self) -> None:
        """Register core application components."""
        
        # Register configuration as singleton
        self.register_singleton(
            'config',
            GeneralNotesConfig,
            lambda: self.config
        )
        
        # Register parsers
        from ..parsers.excel_parser import ExcelParser
        from ..parsers.dxf_parser import DXFParser
        
        self.register_transient(
            'excel_parser',
            ExcelParser,
            lambda: ExcelParser()
        )
        
        self.register_transient(
            'dxf_parser',
            DXFParser,
            lambda: DXFParser()
        )
        
        # Register selection state as singleton
        from ..models.selection import SelectionState
        
        self.register_singleton(
            'selection_state',
            SelectionState,
            lambda: SelectionState()
        )
        
        logger.debug("Core components registered")
    
    def is_registered(self, name: str) -> bool:
        """
        Check if a component is registered.
        
        Args:
            name: Component name
            
        Returns:
            True if component is registered
        """
        return name in self._registrations
    
    def get_registered_components(self) -> list:
        """
        Get list of all registered component names.
        
        Returns:
            List of component names
        """
        return list(self._registrations.keys())
    
    def get_component_info(self, name: str) -> Dict[str, Any]:
        """
        Get information about a registered component.
        
        Args:
            name: Component name
            
        Returns:
            Dictionary with component information
            
        Raises:
            KeyError: If component is not registered
        """
        if name not in self._registrations:
            raise KeyError(f"Component '{name}' is not registered")
        
        registration = self._registrations[name]
        
        return {
            'name': name,
            'type': registration.component_type.__name__,
            'lifecycle': registration.lifecycle,
            'dependencies': registration.dependencies,
            'has_instance': registration.instance is not None,
            'is_singleton': registration.lifecycle == ComponentLifecycle.SINGLETON
        }
    
    def get_component_status(self) -> Dict[str, Any]:
        """
        Get status of all registered components.
        
        Returns:
            Dictionary with component status information
        """
        components = {}
        
        for name in self._registrations:
            try:
                components[name] = self.get_component_info(name)
            except Exception as e:
                components[name] = {'error': str(e)}
        
        return {
            'total_components': len(self._registrations),
            'singleton_count': sum(1 for reg in self._registrations.values() 
                                 if reg.lifecycle == ComponentLifecycle.SINGLETON),
            'transient_count': sum(1 for reg in self._registrations.values() 
                                 if reg.lifecycle == ComponentLifecycle.TRANSIENT),
            'components': components
        }
    
    def cleanup(self) -> None:
        """
        Cleanup all singleton instances and resources.
        
        Should be called when the application is shutting down.
        """
        logger.debug("Cleaning up dependency container")
        
        # Cleanup singleton instances
        for registration in self._registrations.values():
            if registration.instance is not None:
                # Call cleanup method if it exists
                if hasattr(registration.instance, 'cleanup'):
                    try:
                        registration.instance.cleanup()
                    except Exception as e:
                        logger.warning(f"Error cleaning up {registration.component_type.__name__}: {str(e)}")
                
                registration.instance = None
        
        # Clear instances cache
        self._instances.clear()
        
        logger.debug("Dependency container cleanup complete")
    
    def __contains__(self, name: str) -> bool:
        """Check if component is registered using 'in' operator."""
        return self.is_registered(name)
    
    def __getitem__(self, name: str) -> Any:
        """Resolve component using indexing syntax."""
        return self.resolve(name)


# Factory functions for common container configurations
def create_default_container(config: GeneralNotesConfig) -> DependencyContainer:
    """
    Create a dependency container with default component registrations.
    
    Args:
        config: Application configuration
        
    Returns:
        DependencyContainer with default components
    """
    container = DependencyContainer(config)
    
    # Additional component registrations can be added here
    # for specific application configurations
    
    return container


def create_test_container(config: GeneralNotesConfig) -> DependencyContainer:
    """
    Create a dependency container configured for testing.
    
    Args:
        config: Application configuration
        
    Returns:
        DependencyContainer with test-specific components
    """
    container = DependencyContainer(config)
    
    # Register mock components for testing
    # This would include test doubles and mock objects
    
    return container
