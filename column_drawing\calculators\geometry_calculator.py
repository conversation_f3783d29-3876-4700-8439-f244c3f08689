"""
Geometry Calculator
==================

Handles geometric calculations for scaling, positioning, and coordinate transformations
used in drawing generation.
"""

import math
import logging
from typing import Tuple, List
from ..models.drawing_config import DrawingConfig

logger = logging.getLogger(__name__)


class GeometryCalculator:
    """
    Calculator for geometric operations and transformations.

    This class provides utility methods for:
    - Scaling calculations for fitting sections in cells
    - Coordinate transformations
    - Distance and angle calculations
    - Geometric validation
    """

    def __init__(self, config: DrawingConfig):
        """
        Initialize the geometry calculator.

        Args:
            config: Drawing configuration object
        """
        self.config = config

    def calculate_scale_factor(
        self,
        content_width: float,
        content_height: float,
        available_width: float,
        available_height: float,
        margin: float = None
    ) -> float:
        """
        Calculate optimal scale factor to fit content within available space.

        Args:
            content_width: Original content width
            content_height: Original content height
            available_width: Available space width
            available_height: Available space height
            margin: Margin to maintain around content (uses config default if None)

        Returns:
            float: Optimal scale factor
        """
        # Use configuration defaults if not provided
        if margin is None:
            margin = self.config.CELL_MARGIN
        # Account for margins
        usable_width = available_width - 2 * margin
        usable_height = available_height - 2 * margin

        if usable_width <= 0 or usable_height <= 0:
            logger.warning("Insufficient space after accounting for margins")
            return self.config.SCALE_MIN

        # Calculate scale factors for both dimensions
        max_scale = self.config.SCALE_MAX
        scale_x = usable_width / content_width if content_width > 0 else max_scale
        scale_y = usable_height / content_height if content_height > 0 else max_scale

        # Use the smaller scale to maintain aspect ratio
        scale = min(scale_x, scale_y, max_scale)

        logger.debug(
            f"Calculated scale factor: {scale:.2f} for content {content_width}x{content_height}")
        return max(scale, self.config.SCALE_MIN)

    @staticmethod
    def center_in_rectangle(
        content_width: float,
        content_height: float,
        container_x: float,
        container_y: float,
        container_width: float,
        container_height: float
    ) -> Tuple[float, float]:
        """
        Calculate position to center content within a container rectangle.

        Args:
            content_width: Width of content to center
            content_height: Height of content to center
            container_x: Container bottom-left X coordinate
            container_y: Container bottom-left Y coordinate
            container_width: Container width
            container_height: Container height

        Returns:
            Tuple[float, float]: (x, y) position for content bottom-left corner
        """
        center_x = container_x + (container_width - content_width) / 2
        center_y = container_y + (container_height - content_height) / 2

        return center_x, center_y

    @staticmethod
    def calculate_distance(x1: float, y1: float, x2: float, y2: float) -> float:
        """
        Calculate Euclidean distance between two points.

        Args:
            x1, y1: First point coordinates
            x2, y2: Second point coordinates

        Returns:
            float: Distance between points
        """
        return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)

    @staticmethod
    def calculate_angle(x1: float, y1: float, x2: float, y2: float) -> float:
        """
        Calculate angle from first point to second point in radians.

        Args:
            x1, y1: First point coordinates
            x2, y2: Second point coordinates

        Returns:
            float: Angle in radians
        """
        return math.atan2(y2 - y1, x2 - x1)

    @staticmethod
    def rotate_point(
        x: float,
        y: float,
        center_x: float,
        center_y: float,
        angle_rad: float
    ) -> Tuple[float, float]:
        """
        Rotate a point around a center point.

        Args:
            x, y: Point to rotate
            center_x, center_y: Center of rotation
            angle_rad: Rotation angle in radians

        Returns:
            Tuple[float, float]: Rotated point coordinates
        """
        # Translate to origin
        translated_x = x - center_x
        translated_y = y - center_y

        # Rotate
        cos_angle = math.cos(angle_rad)
        sin_angle = math.sin(angle_rad)

        rotated_x = translated_x * cos_angle - translated_y * sin_angle
        rotated_y = translated_x * sin_angle + translated_y * cos_angle

        # Translate back
        final_x = rotated_x + center_x
        final_y = rotated_y + center_y

        return final_x, final_y

    @staticmethod
    def point_in_rectangle(
        point_x: float,
        point_y: float,
        rect_x: float,
        rect_y: float,
        rect_width: float,
        rect_height: float,
        tolerance: float = 0.0
    ) -> bool:
        """
        Check if a point is inside a rectangle (with optional tolerance).

        Args:
            point_x, point_y: Point coordinates
            rect_x, rect_y: Rectangle bottom-left corner
            rect_width, rect_height: Rectangle dimensions
            tolerance: Tolerance for boundary checking

        Returns:
            bool: True if point is inside rectangle
        """
        return (rect_x - tolerance <= point_x <= rect_x + rect_width + tolerance and
                rect_y - tolerance <= point_y <= rect_y + rect_height + tolerance)

    @staticmethod
    def rectangle_overlap(
        rect1_x: float, rect1_y: float, rect1_w: float, rect1_h: float,
        rect2_x: float, rect2_y: float, rect2_w: float, rect2_h: float
    ) -> bool:
        """
        Check if two rectangles overlap.

        Args:
            rect1_x, rect1_y, rect1_w, rect1_h: First rectangle parameters
            rect2_x, rect2_y, rect2_w, rect2_h: Second rectangle parameters

        Returns:
            bool: True if rectangles overlap
        """
        # Check for no overlap conditions
        if (rect1_x + rect1_w < rect2_x or  # rect1 is to the left of rect2
            rect2_x + rect2_w < rect1_x or  # rect2 is to the left of rect1
            rect1_y + rect1_h < rect2_y or  # rect1 is below rect2
                rect2_y + rect2_h < rect1_y):   # rect2 is below rect1
            return False

        return True

    @staticmethod
    def calculate_rectangle_bounds(points: List[Tuple[float, float]]) -> Tuple[float, float, float, float]:
        """
        Calculate bounding rectangle for a set of points.

        Args:
            points: List of (x, y) coordinate tuples

        Returns:
            Tuple[float, float, float, float]: (min_x, min_y, width, height)
        """
        if not points:
            return 0, 0, 0, 0

        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]

        min_x = min(x_coords)
        max_x = max(x_coords)
        min_y = min(y_coords)
        max_y = max(y_coords)

        width = max_x - min_x
        height = max_y - min_y

        return min_x, min_y, width, height

    @staticmethod
    def interpolate_point(
        start_x: float, start_y: float,
        end_x: float, end_y: float,
        t: float
    ) -> Tuple[float, float]:
        """
        Interpolate a point along a line segment.

        Args:
            start_x, start_y: Start point of line segment
            end_x, end_y: End point of line segment
            t: Interpolation parameter (0.0 = start, 1.0 = end)

        Returns:
            Tuple[float, float]: Interpolated point coordinates
        """
        x = start_x + t * (end_x - start_x)
        y = start_y + t * (end_y - start_y)
        return x, y

    @staticmethod
    def offset_rectangle(
        x: float, y: float, width: float, height: float, offset: float
    ) -> Tuple[float, float, float, float]:
        """
        Create an offset (inset/outset) rectangle.

        Args:
            x, y: Original rectangle bottom-left corner
            width, height: Original rectangle dimensions
            offset: Offset amount (positive = inset, negative = outset)

        Returns:
            Tuple[float, float, float, float]: (new_x, new_y, new_width, new_height)
        """
        new_x = x + offset
        new_y = y + offset
        new_width = width - 2 * offset
        new_height = height - 2 * offset

        # Ensure dimensions don't become negative
        new_width = max(new_width, 0)
        new_height = max(new_height, 0)

        return new_x, new_y, new_width, new_height

    @staticmethod
    def calculate_grid_positions(
        start_x: float, start_y: float,
        end_x: float, end_y: float,
        num_points: int,
        include_ends: bool = True
    ) -> List[Tuple[float, float]]:
        """
        Calculate evenly spaced positions along a line.

        Args:
            start_x, start_y: Start point of line
            end_x, end_y: End point of line
            num_points: Number of points to generate
            include_ends: Whether to include start and end points

        Returns:
            List[Tuple[float, float]]: List of evenly spaced points
        """
        if num_points < 1:
            return []

        points = []

        if num_points == 1:
            # Single point at center
            center_x = (start_x + end_x) / 2
            center_y = (start_y + end_y) / 2
            return [(center_x, center_y)]

        if include_ends:
            # Include start and end points
            for i in range(num_points):
                t = i / (num_points - 1) if num_points > 1 else 0
                x, y = GeometryCalculator.interpolate_point(
                    start_x, start_y, end_x, end_y, t)
                points.append((x, y))
        else:
            # Exclude start and end points
            for i in range(1, num_points + 1):
                t = i / (num_points + 1)
                x, y = GeometryCalculator.interpolate_point(
                    start_x, start_y, end_x, end_y, t)
                points.append((x, y))

        return points

    def calculate_optimal_scale(
        self,
        content_width: float,
        content_height: float,
        available_width: float,
        available_height: float,
        margin: float = 0
    ) -> float:
        """
        Calculate optimal scale factor to fit content within available space.

        Args:
            content_width: Width of content to be scaled
            content_height: Height of content to be scaled
            available_width: Available width in target area
            available_height: Available height in target area
            margin: Additional margin to subtract from available space

        Returns:
            float: Optimal scale factor
        """
        try:
            # Adjust available space for margin
            effective_width = available_width - (2 * margin)
            effective_height = available_height - (2 * margin)

            # Ensure we have positive dimensions
            if content_width <= 0 or content_height <= 0:
                logger.warning(
                    f"Invalid content dimensions: {content_width}x{content_height}")
                return 1.0

            if effective_width <= 0 or effective_height <= 0:
                logger.warning(
                    f"Invalid available dimensions: {effective_width}x{effective_height}")
                return 1.0

            # Calculate scale factors for both dimensions
            scale_x = effective_width / content_width
            scale_y = effective_height / content_height

            # Use the smaller scale to ensure content fits in both dimensions
            optimal_scale = min(scale_x, scale_y)

            # Only log if significant scaling is required
            if optimal_scale < 0.8:
                logger.info(
                    f"Content scaled to {optimal_scale:.2f} to fit {content_width:.0f}x{content_height:.0f} in {effective_width:.0f}x{effective_height:.0f}")

            return optimal_scale

        except Exception as e:
            logger.error(f"Error calculating optimal scale: {e}")
            return 1.0
