"""
DXF Layer Manager
================

Manages DXF layers following AIA standards for structural engineering drawings.
Handles layer creation, property assignment, and layer state management.

Organizes layers for ASD table drawings containing:
- Table structure layers: Grid lines, cell boundaries, table framework
- Content layers: Text in TITLE_*/VALUE_*/ELEVATION_* cells
- Zone detail layers: Technical drawings within ZONE_*_DETAIL cells
- Annotation layers: Dimensions, arrows, and link marks in zone details
"""

import logging
from typing import Dict, List, Optional, Set
import ezdxf
from ezdxf.document import Drawing
from ezdxf.entities import Layer

from ..models.layer_config import StructuralLayerConfig, LayerProperties, LayerStatus

logger = logging.getLogger(__name__)


class LayerManager:
    """
    Manager for DXF layers following AIA standards.

    This class handles:
    - Layer creation with proper properties
    - Layer state management (freeze/thaw, on/off, lock/unlock)
    - Layer validation and organization
    - Automatic layer assignment for drawing elements
    """

    def __init__(self, doc: Drawing, layer_config: StructuralLayerConfig = None):
        """
        Initialize the layer manager.

        Args:
            doc: DXF document
            layer_config: Layer configuration (uses default if None)
        """
        self.doc = doc
        self.layer_config = layer_config or StructuralLayerConfig()
        self._created_layers: Set[str] = set()
        self._layer_mapping = self.layer_config.get_layer_mapping()

        # Create line types if they don't exist
        self._ensure_line_types()

        logger.info("Layer manager initialized with AIA standards")

    def _ensure_line_types(self) -> None:
        """
        Ensure required line types exist in the document.

        Creates a pure DASHED line type following ezdxf format:
        pattern = [total_pattern_length, elem1, elem2, ...]

        For DASHED: [250.0, 200.0, -50.0]
        - 250.0 = total pattern length (200 + 50)
        - 200.0 = dash length (positive = line segment)
        - -50.0 = gap length (negative = gap)

        Based on ezdxf CENTER example: [2.0, 1.25, -0.25, 0.25, -0.25]
        """
        try:
            linetypes = self.doc.linetypes

            # Create DASHED line type with correct ezdxf pattern format
            if "DASHED" not in linetypes:
                try:
                    # Correct pattern: [total_length, dash, gap]
                    # 200mm dash + 50mm gap = 250mm total
                    linetypes.add(
                        name="DASHED",
                        description="Dashed line ---- ---- ---- ---- ---- ----",
                        # total_length, dash, gap
                        pattern=[250.0, 200.0, -50.0]
                    )
                    logger.info(
                        "Created DASHED line type: [250.0, 200.0, -50.0] - total 250mm, 200mm dash, 50mm gap")
                except Exception as e:
                    logger.error(f"Could not create DASHED line type: {e}")
            else:
                logger.debug("DASHED line type already exists")

            # Ensure other basic line types exist with correct patterns
            basic_linetypes = {
                "CONTINUOUS": ("Continuous line", []),
                # total 150mm, 100mm dash, 50mm gap
                "HIDDEN": ("Hidden line", [150.0, 100.0, -50.0]),
                # Like ezdxf example but scaled up
                "CENTER": ("Center line", [250.0, 200.0, -50.0, 25.0, -50.0])
            }

            for name, (description, pattern) in basic_linetypes.items():
                if name not in linetypes:
                    try:
                        linetypes.add(
                            name=name,
                            description=description,
                            pattern=pattern
                        )
                        logger.debug(
                            f"Created line type: {name} with pattern {pattern}")
                    except Exception as e:
                        logger.warning(
                            f"Could not create line type {name}: {e}")

        except Exception as e:
            logger.error(f"Error ensuring line types: {e}")

    def create_layer(self, layer_props: LayerProperties) -> Layer:
        """
        Create a DXF layer with the specified properties for ASD table elements.

        Creates layers used for:
        - Table cells: Content in TITLE_*, VALUE_*, ZONE_*_DETAIL, ELEVATION_* cells
        - Technical drawings: Rebar, links, dimensions within zone detail cells
        - Annotations: Arrow marks, text labels, and dimensional information

        Args:
            layer_props: Layer properties to apply

        Returns:
            Layer: Created DXF layer with AIA-compliant properties
        """
        try:
            # Check if layer already exists, if not create it
            if layer_props.name in self.doc.layers:
                layer = self.doc.layers.get(layer_props.name)
            else:
                layer = self.doc.layers.add(layer_props.name)

            # Set layer properties
            layer.color = layer_props.color
            layer.lineweight = self._convert_lineweight(layer_props.lineweight)
            layer.linetype = layer_props.linetype
            layer.plot = layer_props.plottable

            # Set layer state
            if layer_props.frozen:
                layer.freeze()
            else:
                layer.thaw()

            if layer_props.locked:
                layer.lock()
            else:
                layer.unlock()

            if layer_props.on:
                layer.on()
            else:
                layer.off()

            # Track created layer
            self._created_layers.add(layer_props.name)

            return layer

        except Exception as e:
            logger.error(f"Error creating layer {layer_props.name}: {e}")
            raise

    def _convert_lineweight(self, lineweight_mm: float) -> int:
        """
        Convert lineweight from mm to AutoCAD lineweight enum.

        Args:
            lineweight_mm: Line weight in millimeters

        Returns:
            int: AutoCAD lineweight enum value
        """
        # AutoCAD lineweight mapping (mm to enum)
        lineweight_map = {
            0.00: -3,   # ByLayer
            0.05: 5,    # 0.05mm
            0.09: 9,    # 0.09mm
            0.13: 13,   # 0.13mm
            0.15: 15,   # 0.15mm
            0.18: 18,   # 0.18mm
            0.20: 20,   # 0.20mm
            0.25: 25,   # 0.25mm
            0.30: 30,   # 0.30mm
            0.35: 35,   # 0.35mm
            0.40: 40,   # 0.40mm
            0.50: 50,   # 0.50mm
            0.53: 53,   # 0.53mm
            0.60: 60,   # 0.60mm
            0.70: 70,   # 0.70mm
            0.80: 80,   # 0.80mm
            0.90: 90,   # 0.90mm
            1.00: 100,  # 1.00mm
            1.06: 106,  # 1.06mm
            1.20: 120,  # 1.20mm
            1.40: 140,  # 1.40mm
            1.58: 158,  # 1.58mm
            2.00: 200,  # 2.00mm
            2.11: 211,  # 2.11mm
        }

        # Find closest match
        closest_weight = min(lineweight_map.keys(),
                             key=lambda x: abs(x - lineweight_mm))
        return lineweight_map[closest_weight]

    def create_all_standard_layers(self) -> Dict[str, Layer]:
        """
        Create all standard layers defined in the layer configuration.

        Returns:
            Dict[str, Layer]: Dictionary of created layers
        """
        created_layers = {}

        try:
            all_layers = self.layer_config.get_all_layers()

            for layer_name, layer_props in all_layers.items():
                layer = self.create_layer(layer_props)
                created_layers[layer_name] = layer

            logger.info(f"Created {len(created_layers)} standard layers")
            return created_layers

        except Exception as e:
            logger.error(f"Error creating standard layers: {e}")
            raise

    def get_layer_for_element(self, element_type: str) -> str:
        """
        Get the appropriate AIA-standard layer name for an ASD table drawing element.

        Maps element types to layers for:
        - table_text -> Table content in VALUE_* and TITLE_* cells
        - column_outline -> Column boundaries in ZONE_*_DETAIL cells
        - reinforcement -> Rebar circles in zone detail drawings
        - dimensions -> Dimension lines in zone detail drawings
        - links -> BS8666 link shapes in zone detail drawings

        Args:
            element_type: Type of drawing element (table_text, reinforcement, etc.)

        Returns:
            str: AIA-compliant layer name for the element type
        """
        layer_name = self._layer_mapping.get(element_type)

        if layer_name is None:
            logger.warning(
                f"No layer mapping found for element type: {element_type}")
            return "0"  # Default layer

        # Ensure the layer exists
        self.ensure_layer_exists(layer_name)

        return layer_name

    def ensure_layer_exists(self, layer_name: str) -> Layer:
        """
        Ensure a layer exists, creating it if necessary.

        Args:
            layer_name: Name of the layer

        Returns:
            Layer: The layer object
        """
        if layer_name in self._created_layers:
            return self.doc.layers.get(layer_name)

        # Check if it's a standard layer
        layer_props = self.layer_config.get_layer(layer_name)
        if layer_props:
            return self.create_layer(layer_props)

        # Check if layer already exists in document
        if layer_name in self.doc.layers:
            existing_layer = self.doc.layers.get(layer_name)
            self._created_layers.add(layer_name)
            return existing_layer

        # Create a basic layer with default properties
        logger.warning(f"Creating non-standard layer: {layer_name}")
        layer = self.doc.layers.add(layer_name)
        self._created_layers.add(layer_name)
        return layer

    def create_status_layers(self, base_layers: List[str], statuses: List[LayerStatus]) -> Dict[str, Layer]:
        """
        Create status variants of base layers.

        Args:
            base_layers: List of base layer names
            statuses: List of status variants to create

        Returns:
            Dict[str, Layer]: Dictionary of created status layers
        """
        created_layers = {}

        try:
            for base_layer in base_layers:
                for status in statuses:
                    if status == LayerStatus.NONE:
                        continue

                    status_props = self.layer_config.create_status_variant(
                        base_layer, status)
                    layer = self.create_layer(status_props)
                    created_layers[status_props.name] = layer

            logger.info(f"Created {len(created_layers)} status variant layers")
            return created_layers

        except Exception as e:
            logger.error(f"Error creating status layers: {e}")
            raise

    def freeze_layers(self, layer_names: List[str]) -> None:
        """
        Freeze specified layers.

        Args:
            layer_names: List of layer names to freeze
        """
        for layer_name in layer_names:
            try:
                layer = self.doc.layers.get(layer_name)
                if layer:
                    layer.freeze()
                    logger.debug(f"Frozen layer: {layer_name}")
                else:
                    logger.warning(
                        f"Layer not found for freezing: {layer_name}")
            except Exception as e:
                logger.error(f"Error freezing layer {layer_name}: {e}")

    def thaw_layers(self, layer_names: List[str]) -> None:
        """
        Thaw specified layers.

        Args:
            layer_names: List of layer names to thaw
        """
        for layer_name in layer_names:
            try:
                layer = self.doc.layers.get(layer_name)
                if layer:
                    layer.thaw()
                    logger.debug(f"Thawed layer: {layer_name}")
                else:
                    logger.warning(
                        f"Layer not found for thawing: {layer_name}")
            except Exception as e:
                logger.error(f"Error thawing layer {layer_name}: {e}")

    def lock_layers(self, layer_names: List[str]) -> None:
        """
        Lock specified layers.

        Args:
            layer_names: List of layer names to lock
        """
        for layer_name in layer_names:
            try:
                layer = self.doc.layers.get(layer_name)
                if layer:
                    layer.lock()
                    logger.debug(f"Locked layer: {layer_name}")
                else:
                    logger.warning(
                        f"Layer not found for locking: {layer_name}")
            except Exception as e:
                logger.error(f"Error locking layer {layer_name}: {e}")

    def unlock_layers(self, layer_names: List[str]) -> None:
        """
        Unlock specified layers.

        Args:
            layer_names: List of layer names to unlock
        """
        for layer_name in layer_names:
            try:
                layer = self.doc.layers.get(layer_name)
                if layer:
                    layer.unlock()
                    logger.debug(f"Unlocked layer: {layer_name}")
                else:
                    logger.warning(
                        f"Layer not found for unlocking: {layer_name}")
            except Exception as e:
                logger.error(f"Error unlocking layer {layer_name}: {e}")

    def turn_on_layers(self, layer_names: List[str]) -> None:
        """
        Turn on specified layers.

        Args:
            layer_names: List of layer names to turn on
        """
        for layer_name in layer_names:
            try:
                layer = self.doc.layers.get(layer_name)
                if layer:
                    layer.on()
                    logger.debug(f"Turned on layer: {layer_name}")
                else:
                    logger.warning(
                        f"Layer not found for turning on: {layer_name}")
            except Exception as e:
                logger.error(f"Error turning on layer {layer_name}: {e}")

    def turn_off_layers(self, layer_names: List[str]) -> None:
        """
        Turn off specified layers.

        Args:
            layer_names: List of layer names to turn off
        """
        for layer_name in layer_names:
            try:
                layer = self.doc.layers.get(layer_name)
                if layer:
                    layer.off()
                    logger.debug(f"Turned off layer: {layer_name}")
                else:
                    logger.warning(
                        f"Layer not found for turning off: {layer_name}")
            except Exception as e:
                logger.error(f"Error turning off layer {layer_name}: {e}")

    def get_layer_info(self) -> Dict[str, Dict]:
        """
        Get information about all layers in the document.

        Returns:
            Dict[str, Dict]: Layer information
        """
        layer_info = {}

        try:
            for layer in self.doc.layers:
                layer_info[layer.dxf.name] = {
                    "color": layer.dxf.color,
                    "lineweight": layer.dxf.lineweight,
                    "linetype": layer.dxf.linetype,
                    "plot": layer.dxf.plot,
                    "flags": layer.dxf.flags,
                    "is_frozen": layer.is_frozen(),
                    "is_locked": layer.is_locked(),
                    "is_on": layer.is_on(),
                }

            return layer_info

        except Exception as e:
            logger.error(f"Error getting layer info: {e}")
            return {}

    def validate_layers(self) -> Dict[str, List[str]]:
        """
        Validate all layers against AIA standards.

        Returns:
            Dict[str, List[str]]: Validation results with issues
        """
        issues = {
            "invalid_names": [],
            "missing_properties": [],
            "non_standard_colors": [],
            "non_standard_lineweights": []
        }

        try:
            for layer in self.doc.layers:
                layer_name = layer.dxf.name

                # Skip default layer
                if layer_name == "0":
                    continue

                # Validate layer name
                if not self.layer_config.validate_layer_name(layer_name):
                    issues["invalid_names"].append(layer_name)

                # Check for missing properties
                if not hasattr(layer.dxf, 'color') or layer.dxf.color is None:
                    issues["missing_properties"].append(
                        f"{layer_name}: missing color")

                if not hasattr(layer.dxf, 'lineweight') or layer.dxf.lineweight is None:
                    issues["missing_properties"].append(
                        f"{layer_name}: missing lineweight")

            return issues

        except Exception as e:
            logger.error(f"Error validating layers: {e}")
            return issues

    def get_statistics(self) -> Dict[str, int]:
        """
        Get statistics about the layer system.

        Returns:
            Dict[str, int]: Layer statistics
        """
        try:
            total_layers = len(list(self.doc.layers))
            created_layers = len(self._created_layers)
            standard_layers = len(self.layer_config.get_all_layers())

            return {
                "total_layers": total_layers,
                "created_layers": created_layers,
                "standard_layers": standard_layers,
                "custom_layers": total_layers - created_layers,
            }

        except Exception as e:
            logger.error(f"Error getting layer statistics: {e}")
            return {}
