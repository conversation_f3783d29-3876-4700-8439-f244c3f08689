"""
Unit Tests for Orchestrators
============================

Tests for workflow orchestration components.

Author: Drawing Production System
"""

import unittest
import tempfile
import os
from unittest.mock import Mock, patch

from ..core.application_core import ApplicationCore
from ..orchestrators.workflow_orchestrator import WorkflowOrchestrator, WorkflowError
from ..orchestrators.note_selection_orchestrator import NoteSelectionOrchestrator
from ..orchestrators.generation_orchestrator import GenerationOrchestrator
from ..models.configuration import GeneralNotesConfig
from ..models.note_detail import NoteDetail, NoteDetailCollection
from ..models.boundary import Boundary, BoundaryPoint


class TestWorkflowOrchestrator(unittest.TestCase):
    """Test WorkflowOrchestrator class."""
    
    def setUp(self):
        """Set up test workflow orchestrator."""
        self.config = GeneralNotesConfig.create_development()
        self.core = ApplicationCore(self.config)
        self.orchestrator = WorkflowOrchestrator(self.core)
    
    def test_initialization(self):
        """Test WorkflowOrchestrator initialization."""
        self.assertIsNotNone(self.orchestrator.core)
        self.assertIsNotNone(self.orchestrator.selection_orchestrator)
        self.assertIsNotNone(self.orchestrator.generation_orchestrator)
        self.assertEqual(self.orchestrator._current_step, "")
        self.assertEqual(self.orchestrator._total_steps, 0)
        self.assertEqual(self.orchestrator._completed_steps, 0)
    
    def test_workflow_status(self):
        """Test workflow status reporting."""
        status = self.orchestrator.get_workflow_status()
        
        self.assertIn('prerequisites', status)
        self.assertIn('note_collection', status)
        self.assertIn('dxf_database', status)
        self.assertIn('entity_catalog', status)
        self.assertIn('selection', status)
        self.assertIn('configuration', status)
        self.assertIn('orchestrator', status)
        
        # Check orchestrator-specific status
        orchestrator_status = status['orchestrator']
        self.assertIn('current_step', orchestrator_status)
        self.assertIn('total_steps', orchestrator_status)
        self.assertIn('completed_steps', orchestrator_status)
        self.assertIn('progress_percentage', orchestrator_status)
    
    def test_interactive_workflow(self):
        """Test interactive workflow execution."""
        result = self.orchestrator.execute_interactive_workflow()
        
        self.assertIn('status', result)
        self.assertIn('current_state', result)
        self.assertIn('next_steps', result)
        self.assertIn('can_proceed', result)
        
        # Initially should be ready but can't proceed without files
        self.assertEqual(result['status'], 'ready')
        self.assertFalse(result['can_proceed'])
    
    def test_reset_workflow(self):
        """Test workflow reset."""
        # Set some state
        self.orchestrator._current_step = "test_step"
        self.orchestrator._total_steps = 5
        self.orchestrator._completed_steps = 2
        
        # Reset
        self.orchestrator.reset_workflow()
        
        # Verify reset
        self.assertEqual(self.orchestrator._current_step, "")
        self.assertEqual(self.orchestrator._total_steps, 0)
        self.assertEqual(self.orchestrator._completed_steps, 0)
    
    def test_progress_callback(self):
        """Test progress callback functionality."""
        callback_calls = []
        
        def progress_callback(message, percentage):
            callback_calls.append((message, percentage))
        
        orchestrator = WorkflowOrchestrator(self.core, progress_callback)
        
        # Simulate progress update
        orchestrator._initialize_workflow_progress(4)
        orchestrator._update_progress("Test step", 1)
        
        # Verify callback was called
        self.assertEqual(len(callback_calls), 1)
        self.assertEqual(callback_calls[0][0], "Test step")
        self.assertEqual(callback_calls[0][1], 25.0)  # 1/4 * 100
    
    @patch('general_notes.core.application_core.ApplicationCore.load_note_collection')
    @patch('general_notes.core.application_core.ApplicationCore.load_dxf_database')
    def test_get_available_notes(self, mock_load_dxf, mock_load_excel):
        """Test getting available notes."""
        # Mock the core methods
        mock_load_excel.return_value = None
        mock_load_dxf.return_value = None
        
        # Mock the note collection
        boundary = Boundary(
            BoundaryPoint(0, 0, 0),
            BoundaryPoint(100, 0, 0),
            BoundaryPoint(100, 50, 0),
            BoundaryPoint(0, 50, 0)
        )
        note = NoteDetail(title="Test Note", boundary=boundary)
        collection = NoteDetailCollection([note])
        
        self.core._note_collection = collection
        
        # Test getting available notes
        notes = self.orchestrator.get_available_notes("test.dxf", "test.xlsx")
        self.assertEqual(len(notes), 1)
        self.assertEqual(notes[0], "Test Note")


class TestNoteSelectionOrchestrator(unittest.TestCase):
    """Test NoteSelectionOrchestrator class."""
    
    def setUp(self):
        """Set up test note selection orchestrator."""
        self.config = GeneralNotesConfig.create_development()
        self.core = ApplicationCore(self.config)
        self.orchestrator = NoteSelectionOrchestrator(self.core)
        
        # Set up mock note collection
        boundary = Boundary(
            BoundaryPoint(0, 0, 0),
            BoundaryPoint(100, 0, 0),
            BoundaryPoint(100, 50, 0),
            BoundaryPoint(0, 50, 0)
        )
        notes = [
            NoteDetail(title="Note 1", boundary=boundary),
            NoteDetail(title="Note 2", boundary=boundary),
            NoteDetail(title="Note 3", boundary=boundary)
        ]
        self.core._note_collection = NoteDetailCollection(notes)
    
    def test_process_selection_with_specific_notes(self):
        """Test processing selection with specific notes."""
        selected_notes = ["Note 1", "Note 2"]
        
        result = self.orchestrator.process_selection(selected_notes)
        
        self.assertIn('selected_count', result)
        self.assertEqual(result['selected_count'], 2)
        self.assertIn('selected_titles', result)
        self.assertEqual(set(result['selected_titles']), set(selected_notes))
    
    def test_process_selection_all_notes(self):
        """Test processing selection with all notes."""
        result = self.orchestrator.process_selection(None)
        
        self.assertIn('selected_count', result)
        self.assertEqual(result['selected_count'], 3)  # All notes selected
    
    def test_process_selection_invalid_notes(self):
        """Test processing selection with invalid notes."""
        selected_notes = ["Invalid Note"]
        
        with self.assertRaises(Exception):
            self.orchestrator.process_selection(selected_notes)


class TestGenerationOrchestrator(unittest.TestCase):
    """Test GenerationOrchestrator class."""
    
    def setUp(self):
        """Set up test generation orchestrator."""
        self.config = GeneralNotesConfig.create_development()
        self.core = ApplicationCore(self.config)
        self.orchestrator = GenerationOrchestrator(self.core)
        
        # Set up selection state
        self.core.selection_state.select_note("Test Note")
    
    def test_generate_output_with_selection(self):
        """Test output generation with selected notes."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            output_path = os.path.join(tmp_dir, "output.dxf")
            
            # Should succeed (mock implementation returns True)
            result = self.orchestrator.generate_output(output_path)
            self.assertTrue(result)
    
    def test_generate_output_no_selection(self):
        """Test output generation with no selected notes."""
        # Clear selection
        self.core.selection_state.deselect_all()
        
        with tempfile.TemporaryDirectory() as tmp_dir:
            output_path = os.path.join(tmp_dir, "output.dxf")
            
            with self.assertRaises(ValueError):
                self.orchestrator.generate_output(output_path)
    
    def test_generate_output_invalid_path(self):
        """Test output generation with invalid path."""
        invalid_path = "/nonexistent/directory/output.dxf"
        
        with self.assertRaises(Exception):
            self.orchestrator.generate_output(invalid_path)


class TestWorkflowIntegration(unittest.TestCase):
    """Test integration between orchestrators."""
    
    def setUp(self):
        """Set up test integration environment."""
        self.config = GeneralNotesConfig.create_development()
        self.core = ApplicationCore(self.config)
        self.workflow_orchestrator = WorkflowOrchestrator(self.core)
        
        # Set up mock data
        boundary = Boundary(
            BoundaryPoint(0, 0, 0),
            BoundaryPoint(100, 0, 0),
            BoundaryPoint(100, 50, 0),
            BoundaryPoint(0, 50, 0)
        )
        notes = [
            NoteDetail(title="Note 1", boundary=boundary),
            NoteDetail(title="Note 2", boundary=boundary)
        ]
        self.core._note_collection = NoteDetailCollection(notes)
    
    def test_selection_to_generation_workflow(self):
        """Test workflow from selection to generation."""
        # Step 1: Process selection
        selection_result = self.workflow_orchestrator.selection_step(["Note 1"])
        self.assertEqual(selection_result['status'], 'success')
        
        # Step 2: Generate output
        with tempfile.TemporaryDirectory() as tmp_dir:
            output_path = os.path.join(tmp_dir, "output.dxf")
            
            generation_result = self.workflow_orchestrator.generation_step(output_path)
            self.assertEqual(generation_result['status'], 'success')
    
    def test_workflow_state_consistency(self):
        """Test that workflow state remains consistent across operations."""
        # Initial state
        initial_status = self.workflow_orchestrator.get_workflow_status()
        
        # Process selection
        self.workflow_orchestrator.selection_step(["Note 1"])
        
        # Check state after selection
        after_selection_status = self.workflow_orchestrator.get_workflow_status()
        self.assertEqual(after_selection_status['selection']['selected_count'], 1)
        
        # Reset workflow
        self.workflow_orchestrator.reset_workflow()
        
        # Check state after reset
        after_reset_status = self.workflow_orchestrator.get_workflow_status()
        self.assertEqual(after_reset_status['selection']['selected_count'], 0)


if __name__ == '__main__':
    unittest.main()
