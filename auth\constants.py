"""
Authentication Constants Module
============================

This module contains all authentication-related constants used across the application.
Centralizes configuration values for security, UI layout, and service endpoints.

Author: Drawing Production System
Date: 2025
"""

from version_config import APP_VERSION_V_PREFIX as APP_VERSION

# Security Settings
MAX_LOGIN_ATTEMPTS = 5
LOGIN_TIMEOUT_MINUTES = 60
SESSION_TOKEN_BYTES = 32
LICENSE_FILE_ID = "1lzlpGVnnEz5tBLkhFojFz08Hv3926YRF"
URL_TIMEOUT = 10  # Timeout for network requests in seconds

# Email Configuration
EMAIL_SENDER = '<EMAIL>'
EMAIL_PASSWORD = 'nunwcsgerkfetpii'

# Developer Mode Configuration
DEVELOPER_MODE = True  # Set to False in production

# UI Constants
FONT_TITLE = ("Arial", 16, "bold")
FONT_SUBTITLE = ("Arial", 12)
FONT_NORMAL = ("Arial", 10)
FONT_SMALL = ("Arial", 8, "italic")
BUTTON_WIDTH_LARGE = 20
BUTTON_HEIGHT_LARGE = 2
BUTTON_WIDTH_MEDIUM = 15
BUTTON_HEIGHT_MEDIUM = 2
ENTRY_WIDTH = 40

# Application Information
APP_VERSION = APP_VERSION