"""
Command Line Interface Module
==============================

Handles the command-line interface for the column drawing application.
This module separates CLI logic from core business logic to prepare
for future GUI integration.
"""

import time
from ..main import ColumnDrawingGenerator


class CLIInterface:
    """
    Command-line interface for the column drawing application.

    This class provides a clean separation between the CLI and the core
    business logic, making it easier to add GUI interfaces later.
    """

    def __init__(self):
        """Initialize the CLI interface."""
        # We'll initialize generators when needed to ensure clean DXF documents
        pass

    def run(self):
        """Main CLI entry point."""
        try:
            print("Starting Column Drawing Generator")

            # Initialize a fresh generator instance
            generator = ColumnDrawingGenerator()

            # Configuration
            csv_input_file = "example/Rect Column Rebar Table (ASD).csv"
            output_dxf_file = f"column_rc_detail_{time.strftime('%Y%m%d_%H%M%S')}.dxf"

            # Generate drawings with drawing space organization
            successful_drawings_count = generator.generate_drawings_with_spaces(
                csv_input_file,
                output_dxf_file,
                use_zone_details=True
            )

            # Display results
            self._display_results(successful_drawings_count,
                                  output_dxf_file, generator)

        except FileNotFoundError as e:
            self._handle_file_not_found_error(e)

        except Exception as e:
            self._handle_unexpected_error(e)

    def run_with_drawing_spaces(self):
        """CLI entry point with drawing space organization."""
        try:
            print("Starting Column Drawing Generator with Drawing Space Organization")
            print("=" * 60)
            print("Drawing Space Specifications:")
            print("  - A1 size at 1:50 scale")
            print("  - Maximum 4 tables per row, 2 rows per space")
            print("  - Automatic overflow to additional drawing spaces")
            print("=" * 60)

            # Initialize a fresh generator instance
            generator = ColumnDrawingGenerator()

            # Configuration
            csv_input_file = "example/Rect Column Rebar Table (ASD).csv"
            output_dxf_file = f"column_rc_detail_spaces_{time.strftime('%Y%m%d_%H%M%S')}.dxf"

            # Generate drawings with drawing space organization
            successful_drawings_count = generator.generate_drawings_with_spaces(
                csv_input_file,
                output_dxf_file,
                use_zone_details=True
            )

            # Display results
            self._display_results_with_spaces(
                successful_drawings_count, output_dxf_file, generator)

        except FileNotFoundError as e:
            self._handle_file_not_found_error(e)

        except Exception as e:
            self._handle_unexpected_error(e)

    def run_dual_generation(self):
        """CLI entry point that generates both standard and enhanced DXF files with drawing space organization."""
        try:
            print("Starting Dual Column Drawing Generator")
            print("=" * 60)
            print("Generating TWO DXF files:")
            print("  1. Standard output (with drawing space organization)")
            print("  2. Enhanced output (with optimized drawing space structure)")
            print("=" * 60)

            # Initialize generator
            self.generator = ColumnDrawingGenerator()

            # Configuration
            csv_input_file = "example/Rect Column Rebar Table (ASD).csv"
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            original_dxf_file = f"column_rc_detail_{timestamp}.dxf"
            organized_dxf_file = f"column_rc_detail_spaces_{timestamp}.dxf"

            print(f"
1. Generating standard output with drawing spaces: {original_dxf_file}")
            print("-" * 50)

            # Generate original drawings with first generator instance (now with drawing spaces)
            generator1 = ColumnDrawingGenerator()
            original_count = generator1.generate_drawings_with_spaces(
                csv_input_file,
                original_dxf_file,
                use_zone_details=True
            )

            print(f"
2. Generating enhanced output with optimized drawing spaces: {organized_dxf_file}")
            print("-" * 50)

            # Generate organized drawings with a fresh generator instance
            # This ensures a clean DXF document without carrying over previous content
            generator2 = ColumnDrawingGenerator()
            organized_count = generator2.generate_drawings_with_spaces(
                csv_input_file,
                organized_dxf_file,
                use_zone_details=True
            )

            # Display combined results
            self._display_dual_results(
                original_count, organized_count, original_dxf_file, organized_dxf_file)

        except FileNotFoundError as e:
            self._handle_file_not_found_error(e)

        except Exception as e:
            self._handle_unexpected_error(e)

    def _display_results_with_spaces(self, successful_count: int, output_file: str, generator=None):
        """
        Display the results of the drawing generation with drawing spaces.

        Args:
            successful_count: Number of successfully generated drawings
            output_file: Path to the output DXF file
            generator: The ColumnDrawingGenerator instance used
        """
        from ..utils.logging_config import get_message_counter

        print(f"\n{'='*60}")
        print("DRAWING SPACE GENERATION COMPLETE")
        print(f"{'='*60}")
        print(f"Generated: {successful_count} column drawings")
        print(f"Organized: Into structured A1 drawing spaces")
        print(f"Output: {output_file}")
        print(f"Log: column_drawing.log")

        # Show consolidation summary if messages were consolidated
        message_counter = get_message_counter()
        if message_counter.message_counts:
            print(f"\nNote: Some routine messages were consolidated in the log file.")
            print(
                f"Messages like 'Zone positioning' and 'No existing mark found' are normal")
            print(f"during the drawing generation process.")

        print(f"\nFeatures:")
        print(f"  - Tables organized into A1 drawing spaces at 1:50 scale")
        print(f"  - Maximum 8 tables per drawing space (4×2 grid)")
        print(f"  - Automatic overflow to additional spaces")
        print(f"  - Drawing space boundaries and titles")

        print(f"\nOpen {output_file} in AutoCAD or any DXF viewer")
        print(f"{'='*60}")

    def _display_dual_results(self, original_count: int, organized_count: int, original_file: str, organized_file: str):
        """
        Display the results of dual DXF generation.

        Args:
            original_count: Number of successfully generated drawings in original file
            organized_count: Number of successfully generated drawings in organized file
            original_file: Path to the original DXF file
            organized_file: Path to the organized DXF file
        """
        from ..utils.logging_config import get_message_counter

        print(f"\n{'='*60}")
        print("DUAL GENERATION COMPLETE")
        print(f"{'='*60}")
        print(f"Original Layout:")
        print(f"  Generated: {original_count} column drawings")
        print(f"  Output: {original_file}")
        print(f"  Format: Traditional layout")
        print()
        print(f"Organized Layout:")
        print(f"  Generated: {organized_count} column drawings")
        print(f"  Output: {organized_file}")
        print(f"  Format: A1 drawing spaces at 1:50 scale")
        print(f"  Features: Boundaries, titles, and structured organization")
        print()
        print(f"Log: column_drawing.log")

        # Show consolidation summary if messages were consolidated
        message_counter = get_message_counter()
        if message_counter.message_counts:
            print(f"\nNote: Some routine messages were consolidated in the log file.")
            print(
                f"Messages like 'Zone positioning' and 'No existing mark found' are normal")
            print(f"during the drawing generation process.")

        print(f"\nRecommendation:")
        print(f"  - Use {original_file} for traditional CAD workflows")
        print(f"  - Use {organized_file} for structured drawing sheets")
        print(f"  - Both files contain identical drawing content")
        print(f"\nOpen either file in AutoCAD or any DXF viewer")
        print(f"{'='*60}")

    def run_interactive(self):
        """Interactive CLI with mode selection."""
        try:
            print("Column Drawing Generator")
            print("=" * 50)
            print("Select drawing mode:")
            print("1. Traditional Layout (current)")
            print("2. Drawing Space Organization (new)")
            print("3. Exit")
            print()

            while True:
                try:
                    choice = input("Enter choice (1-3): ").strip()

                    if choice == '1':
                        print("\nSelected: Traditional Layout")
                        print("-" * 30)
                        self.run()
                        break
                    elif choice == '2':
                        print("\nSelected: Drawing Space Organization")
                        print("-" * 40)
                        self.run_with_drawing_spaces()
                        break
                    elif choice == '3':
                        print("Exiting...")
                        break
                    else:
                        print("Invalid choice. Please enter 1, 2, or 3.")

                except KeyboardInterrupt:
                    print("\nExiting...")
                    break
                except Exception as e:
                    print(f"Input error: {e}")

        except Exception as e:
            self._handle_unexpected_error(e)

    def _display_results(self, successful_count: int, output_file: str, generator=None):
        """
        Display the results of the drawing generation.

        Args:
            successful_count: Number of successfully generated drawings
            output_file: Path to the output DXF file
            generator: The ColumnDrawingGenerator instance used
        """
        from ..utils.logging_config import get_message_counter

        print(f"\n{'='*50}")
        print("GENERATION COMPLETE")
        print(f"{'='*50}")
        print(f"Generated: {successful_count} column drawings")
        print(f"Output: {output_file}")
        print(f"Log: column_drawing.log")

        # Show consolidation summary if messages were consolidated
        message_counter = get_message_counter()
        if message_counter.message_counts:
            print(f"\nNote: Some routine messages were consolidated in the log file.")
            print(
                f"Messages like 'Zone positioning' and 'No existing mark found' are normal")
            print(f"during the drawing generation process.")

        print(f"\nOpen {output_file} in AutoCAD or any DXF viewer")
        print(f"{'='*50}")

    def _handle_file_not_found_error(self, error: FileNotFoundError):
        """
        Handle file not found errors.

        Args:
            error: The FileNotFoundError that occurred
        """
        print(f"ERROR File not found: {error}")
        print("Please ensure the CSV file exists in the current directory.")

    def _handle_unexpected_error(self, error: Exception):
        """
        Handle unexpected errors.

        Args:
            error: The unexpected error that occurred
        """
        print(f"ERROR Unexpected error: {error}")
        print("Check the log file 'column_drawing.log' for details.")

        # Log the error
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error: {error}")


def main():
    """Main function to run the dual column drawing generator CLI."""
    cli = CLIInterface()
    cli.run_dual_generation()


if __name__ == "__main__":
    main()
