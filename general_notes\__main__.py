"""
General Notes Drawing Generator - Entry Point
============================================

Entry point for standalone execution of the General Notes Drawing Generator.

This module provides the command-line interface for running the application
as a standalone package.

Usage:
    python -m general_notes
    python -m general_notes --gui
    python -m general_notes --cli
"""

import sys
import logging
from typing import Optional

from .main import main


def setup_logging(level: str = "INFO") -> None:
    """
    Configure logging for the application.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
    """
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def parse_arguments() -> dict:
    """
    Parse command line arguments.
    
    Returns:
        Dictionary of parsed arguments
    """
    args = {
        'mode': 'gui',  # Default to GUI mode
        'log_level': 'INFO'
    }
    
    if '--cli' in sys.argv:
        args['mode'] = 'cli'
    elif '--gui' in sys.argv:
        args['mode'] = 'gui'
    
    if '--debug' in sys.argv:
        args['log_level'] = 'DEBUG'
    elif '--verbose' in sys.argv:
        args['log_level'] = 'INFO'
    elif '--quiet' in sys.argv:
        args['log_level'] = 'WARNING'
    
    return args


def main_entry() -> None:
    """
    Main entry point for the package.
    
    Handles command line argument parsing and delegates to the main function.
    """
    try:
        # Parse command line arguments
        args = parse_arguments()
        
        # Setup logging
        setup_logging(args['log_level'])
        
        # Log startup information
        logger = logging.getLogger(__name__)
        logger.info("Starting General Notes Drawing Generator")
        logger.info(f"Mode: {args['mode']}")
        logger.info(f"Log Level: {args['log_level']}")
        
        # Run the main application
        main(mode=args['mode'])
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        logging.error(f"Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main_entry()
