# Input/Output Package

Input/output handling modules for reading CSV data and writing DXF files in the Drawing-Production application.

## 📋 Overview

The I/O package provides robust input/output capabilities for the Drawing-Production application, handling CSV data parsing with comprehensive validation and DXF file creation with professional AutoCAD compatibility. These modules ensure reliable data flow from input specifications to final technical drawings.

## 🏗️ Package Components

### CSVReader (`csv_reader.py`)

**Purpose**: <PERSON>les reading and parsing column configuration data from CSV files, mapping CSV data to column configurations and zone detail configurations for use in ASD table cell generation and zone detail drawing.

**Key Features**:

- Robust CSV parsing with comprehensive validation
- ASD table format compliance with required and optional headers
- Zone configuration extraction for detailed drawings
- Error reporting with specific row and column information
- Encoding support for international character sets
- Graceful error handling with detailed validation feedback

**ASD Table Format Headers**:

**Required Headers**:

- `Start Floor` (str): Starting floor level → VALUE_FLOOR_MARK cell
- `End Floor` (str): Ending floor level → VALUE_FLOOR_MARK cell
- `Column Mark` (str): Column identifier → VALUE_COLUMN_MARK cell
- `B (mm)` (float): Column width → VALUE_SIZE cell
- `D (mm)` (float): Column depth → VALUE_SIZE cell
- `Cover (mm)` (float): Concrete cover distance → VALUE_MAIN_BAR cell
- `Dia1 (mm)` (float): Layer 1 rebar diameter → VALUE_MAIN_BAR cell
- `Num X1` (int): Layer 1 rebars in X direction → VALUE_MAIN_BAR cell
- `Num Y1` (int): Layer 1 rebars in Y direction → VALUE_MAIN_BAR cell

**Optional Headers**:

- Layer 2: `Dia2 (mm)`, `Num X2`, `Num Y2`
- Zone A: `A_Dia_Links (mm)`, `A_Spacing (mm)`, `A_Num_Legs_X`, `A_Num_Legs_Y`
- Zone B: `B_Dia_Links (mm)`, `B_Spacing (mm)`, `B_Num_Legs_X`, `B_Num_Legs_Y`
- Zone C: `C_Dia_Links (mm)`, `C_Spacing (mm)`, `C_Num_Legs_X`, `C_Num_Legs_Y`
- Zone D: `D_Dia_Links (mm)`, `D_Spacing (mm)`, `D_Num_Legs_X`, `D_Num_Legs_Y`

**Main Methods**:

```python
read_column_data(filename: str) -> List[ColumnConfig]
read_column_data_with_zones(filename: str) -> List[Tuple[ColumnConfig, ZoneConfigSet]]
validate_file_format(filename: str) -> bool
get_file_info(filename: str) -> Dict[str, Any]
get_validation_errors() -> List[str]
```

### DXFWriter (`dxf_writer.py`)

**Purpose**: Handles DXF document creation, setup, and file operations with AutoCAD compatibility and professional layer management following AIA standards.

**Key Features**:

- DXF document initialization with AutoCAD R2018 compatibility
- Professional layer management following AIA standards
- Text and dimension style setup for engineering drawings
- Document validation and saving with error handling
- Backup operations and file integrity checking
- Encoding support for international compatibility

**AIA Layer Standards**:

- **S-CONC-RBAR**: Reinforcement bars and rebar elements
- **S-CONC-STIR**: Stirrups, links, and connection elements
- **S-CONC-DIMS**: Dimensions, annotations, and measurements
- **S-TABL-BORD**: Table borders, grid lines, and structural elements

**Main Methods**:

```python
get_document() -> Drawing
get_modelspace() -> Modelspace
save_document(filename: str, create_backup: bool = True) -> bool
validate_document() -> bool
get_layer_for_element(element_type: str) -> str
ensure_layer_exists(layer_name: str) -> Layer
```

**AutoCAD Compatibility Features**:

- R2018 format with proper version headers
- Professional text styles and dimension formatting
- Standardized layer properties and colors
- Proper encoding for international character support
- Units set to millimeters for engineering precision

## 🔧 Dependencies

### Internal Dependencies

- `models.column_config.ColumnConfig`: Column specification data structure
- `models.zone_config.ZoneConfig, ZoneConfigSet`: Zone configuration data
- `models.drawing_config.DrawingConfig`: Drawing configuration parameters
- `models.layer_config.StructuralLayerConfig`: Layer management configuration
- `managers.layer_manager.LayerManager`: Professional layer management

### External Dependencies

- `ezdxf`: DXF file creation and manipulation
- `csv`: CSV file parsing (Python standard library)
- `os`: File system operations (Python standard library)
- `logging`: Error reporting and debugging

## 🚀 Usage Examples

### Basic CSV Reading

```python
from column_drawing.io.csv_reader import CSVReader

# Initialize CSV reader
reader = CSVReader()

# Read column data
try:
    columns = reader.read_column_data("input.csv")
    print(f"Successfully loaded {len(columns)} columns")

    for column in columns:
        print(f"Column {column.name}: {column.B}x{column.D}mm")

except FileNotFoundError:
    print("CSV file not found")
except ValueError as e:
    print(f"CSV format error: {e}")

# Check for validation errors
errors = reader.get_validation_errors()
if errors:
    print("Validation errors:")
    for error in errors:
        print(f"  - {error}")
```

### CSV Reading with Zone Data

```python
# Read column data with zone configurations
try:
    columns_with_zones = reader.read_column_data_with_zones("input.csv")

    for column_config, zone_config_set in columns_with_zones:
        print(f"Column {column_config.name}:")
        print(f"  Dimensions: {column_config.B}x{column_config.D}mm")

        # Access zone configurations
        zone_a = zone_config_set.get_zone("A")
        print(f"  Zone A: {zone_a.outer_link_diameter}mm links @ {zone_a.link_spacing}mm")

except Exception as e:
    print(f"Error reading CSV with zones: {e}")
```

### File Validation

```python
# Validate file format before processing
filename = "column_data.csv"

if reader.validate_file_format(filename):
    print("File format is valid")

    # Get file information
    file_info = reader.get_file_info(filename)
    print(f"File size: {file_info['size_bytes']} bytes")
    print(f"Row count: {file_info['row_count']}")
    print(f"Headers: {file_info['headers']}")
else:
    print("File format validation failed")
```

### DXF Document Creation

```python
from column_drawing.io.dxf_writer import DXFWriter
from column_drawing.models.drawing_config import DrawingConfig
from column_drawing.models.layer_config import StructuralLayerConfig

# Initialize DXF writer with configuration
config = DrawingConfig()
layer_config = StructuralLayerConfig()
dxf_writer = DXFWriter(dxf_version='R2018', config=config, layer_config=layer_config)

# Get document and modelspace for drawing
doc = dxf_writer.get_document()
msp = dxf_writer.get_modelspace()

# Add drawing elements (example)
# ... drawing operations ...

# Save document
success = dxf_writer.save_document("output.dxf", create_backup=True)
if success:
    print("DXF file saved successfully")
else:
    print("Error saving DXF file")
```

### Layer Management

```python
# Ensure layers exist for drawing elements
rebar_layer = dxf_writer.get_layer_for_element("reinforcement")
dimension_layer = dxf_writer.get_layer_for_element("dimensions")
table_layer = dxf_writer.get_layer_for_element("table_borders")

print(f"Rebar layer: {rebar_layer}")
print(f"Dimension layer: {dimension_layer}")
print(f"Table layer: {table_layer}")

# Validate document before saving
if dxf_writer.validate_document():
    print("Document validation passed")
else:
    print("Document validation failed - check logs")
```

## 📊 Data Validation

### CSV Validation Features

**Header Validation**:

- Checks for all required headers
- Validates header naming conventions
- Reports missing or misspelled headers

**Data Type Validation**:

- Ensures numeric fields contain valid numbers
- Validates string fields for proper formatting
- Checks for empty or null values in required fields

**Engineering Validation**:

- Validates column dimensions are positive and reasonable
- Checks rebar specifications are within engineering limits
- Ensures cover distances are appropriate for column sizes
- Validates link configurations are structurally sound

**Row-Level Validation**:

- Provides specific row numbers for validation errors
- Continues processing valid rows even if some fail
- Accumulates all validation errors for comprehensive reporting

### DXF Validation Features

**Document Structure Validation**:

- Checks for required DXF sections and tables
- Validates layer definitions and properties
- Ensures text and dimension styles are properly defined

**Content Validation**:

- Validates entity properties and coordinates
- Checks for valid layer assignments
- Ensures proper entity relationships

**AutoCAD Compatibility Validation**:

- Verifies version compatibility settings
- Checks encoding and character set support
- Validates units and precision settings

## 🎯 Integration Points

### With Data Models

- **ColumnConfig**: Primary output of CSV reading operations
- **ZoneConfigSet**: Zone-specific configurations extracted from CSV
- **DrawingConfig**: Configuration parameters for DXF output
- **LayerConfig**: Layer management configuration for professional output

### With Drawing Components

- **All Drawers**: Use DXFWriter for creating drawing entities
- **LayerManager**: Coordinates with DXFWriter for consistent layer usage
- **ValidationSystems**: Use CSV validation for input checking

### With Application Core

- **DataProcessor**: Uses CSVReader for input data processing
- **DrawingOrchestrator**: Uses DXFWriter for final output generation
- **ErrorHandling**: Integrates validation errors into application error reporting

## 🔍 Error Handling

### CSV Reading Errors

**File-Level Errors**:

- File not found or access denied
- Invalid file encoding or format
- Corrupted or truncated files

**Format-Level Errors**:

- Missing required headers
- Invalid header names or order
- Inconsistent row lengths

**Data-Level Errors**:

- Invalid data types in numeric fields
- Out-of-range values for engineering parameters
- Missing data in required fields
- Inconsistent zone configurations

**Error Recovery**:

- Continues processing valid rows after errors
- Provides detailed error messages with row/column information
- Accumulates all errors for comprehensive reporting
- Offers suggestions for correcting common errors

### DXF Writing Errors

**Document Creation Errors**:

- Invalid DXF version specifications
- Layer creation failures
- Text/dimension style setup errors

**Content Errors**:

- Invalid entity coordinates or properties
- Layer assignment errors
- Entity relationship problems

**File System Errors**:

- Write permission denied
- Disk space insufficient
- File locking conflicts

**Error Recovery**:

- Backup creation before overwriting existing files
- Validation before saving to catch errors early
- Graceful degradation with partial output when possible
- Detailed error logging for debugging

## 🔧 Performance Considerations

### CSV Reading Optimization

**Memory Efficiency**:

- Streaming CSV reading for large files
- Minimal memory footprint during parsing
- Efficient data structure creation

**Processing Speed**:

- Optimized validation algorithms
- Batch processing of multiple rows
- Lazy evaluation of complex validations

**Scalability**:

- Handles large CSV files efficiently
- Supports concurrent reading operations
- Memory usage scales linearly with file size

### DXF Writing Optimization

**Document Efficiency**:

- Minimal DXF file size through optimized entity creation
- Efficient layer and style management
- Proper entity grouping and organization

**Writing Performance**:

- Batch entity creation for improved speed
- Optimized coordinate precision for file size
- Efficient text and dimension handling

**Resource Management**:

- Proper cleanup of temporary objects
- Memory-efficient document building
- Minimal CPU usage during file operations

## 🔧 Configuration Options

### CSV Reader Configuration

```python
# Custom encoding support
reader = CSVReader(encoding='utf-8-sig')  # For Excel CSV files

# Custom validation settings
reader.set_validation_strict(True)  # Strict validation mode
reader.set_error_limit(10)  # Maximum errors before stopping
```

### DXF Writer Configuration

```python
# Custom DXF version
dxf_writer = DXFWriter(dxf_version='R2018')

# Custom layer configuration
custom_layer_config = StructuralLayerConfig()
custom_layer_config.set_color_scheme('monochrome')
dxf_writer = DXFWriter(layer_config=custom_layer_config)

# Custom drawing configuration
custom_config = DrawingConfig()
custom_config.TEXT_HEIGHT_BASE = 120
dxf_writer = DXFWriter(config=custom_config)
```
