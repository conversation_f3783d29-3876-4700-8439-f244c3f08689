"""
Interfaces Package
=================

User interface components for the General Notes Drawing Generator.

This package provides GUI components using tkinter:
- Main application window
- Note selection checklist widget
- Progress indication dialogs
- Error reporting interfaces

Modules:
    - main_window: Main GUI window using tkinter
    - checklist_widget: Note selection checklist
    - progress_dialog: Progress indication
    - error_dialog: Error reporting interface
"""

from .main_window import MainWindow
from .checklist_widget import ChecklistWidget
from .progress_dialog import ProgressDialog
from .error_dialog import ErrorDialog

__all__ = [
    'MainWindow',
    'ChecklistWidget',
    'ProgressDialog',
    'ErrorDialog'
]
