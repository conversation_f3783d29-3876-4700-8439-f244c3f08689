"""
Coordinate Parser
================

Utilities for parsing and validating coordinate strings in "(x, y, z)" format.

This module provides functionality for:
- Parsing coordinate strings from Excel cells
- Validating coordinate format and values
- Converting between string and numeric coordinate representations
- Handling coordinate precision and rounding

Author: Drawing Production System
"""

import re
import logging
from typing import Tuple, List, Optional, Union
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class CoordinateParsingError(Exception):
    """Raised when coordinate parsing fails."""
    pass


@dataclass
class ParsedCoordinate:
    """
    Represents a parsed coordinate with metadata.
    
    Attributes:
        x: X coordinate value
        y: Y coordinate value
        z: Z coordinate value
        original_string: Original string that was parsed
        precision: Number of decimal places in original
    """
    x: float
    y: float
    z: float
    original_string: str
    precision: int = 6
    
    def to_tuple(self) -> Tuple[float, float, float]:
        """Convert to coordinate tuple."""
        return (self.x, self.y, self.z)
    
    def to_2d_tuple(self) -> Tuple[float, float]:
        """Convert to 2D coordinate tuple."""
        return (self.x, self.y)


class CoordinateParser:
    """
    Parser for coordinate strings in various formats.
    
    Supports parsing coordinates in formats like:
    - "(x, y, z)"
    - "(x,y,z)"
    - "x, y, z"
    - "x y z"
    - And variations with different whitespace
    """
    
    # Regular expression patterns for different coordinate formats
    PATTERNS = [
        # Standard format: "(x, y, z)"
        r'\(\s*([+-]?\d*\.?\d+)\s*,\s*([+-]?\d*\.?\d+)\s*,\s*([+-]?\d*\.?\d+)\s*\)',
        
        # Without parentheses: "x, y, z"
        r'([+-]?\d*\.?\d+)\s*,\s*([+-]?\d*\.?\d+)\s*,\s*([+-]?\d*\.?\d+)',
        
        # Space separated: "x y z"
        r'([+-]?\d*\.?\d+)\s+([+-]?\d*\.?\d+)\s+([+-]?\d*\.?\d+)',
        
        # Tab separated: "x\ty\tz"
        r'([+-]?\d*\.?\d+)\s*\t\s*([+-]?\d*\.?\d+)\s*\t\s*([+-]?\d*\.?\d+)',
    ]
    
    def __init__(self, default_precision: int = 6, strict_mode: bool = False):
        """
        Initialize the coordinate parser.
        
        Args:
            default_precision: Default decimal precision for coordinates
            strict_mode: Whether to use strict parsing (only standard format)
        """
        self.default_precision = default_precision
        self.strict_mode = strict_mode
        self.compiled_patterns = [re.compile(pattern) for pattern in self.PATTERNS]
    
    def parse_coordinate(self, coord_str: str) -> ParsedCoordinate:
        """
        Parse a coordinate string into numeric values.
        
        Args:
            coord_str: Coordinate string to parse
            
        Returns:
            ParsedCoordinate object
            
        Raises:
            CoordinateParsingError: If parsing fails
        """
        if not coord_str or not isinstance(coord_str, str):
            raise CoordinateParsingError("Coordinate string cannot be empty or non-string")
        
        # Clean the input string
        clean_str = coord_str.strip()
        
        # Try each pattern
        patterns_to_try = [self.compiled_patterns[0]] if self.strict_mode else self.compiled_patterns
        
        for pattern in patterns_to_try:
            match = pattern.match(clean_str)
            if match:
                try:
                    x, y, z = map(float, match.groups())
                    
                    # Determine precision from original string
                    precision = self._determine_precision(clean_str)
                    
                    # Validate coordinate values
                    self._validate_coordinate_values(x, y, z)
                    
                    return ParsedCoordinate(
                        x=x, y=y, z=z,
                        original_string=coord_str,
                        precision=precision
                    )
                    
                except ValueError as e:
                    continue  # Try next pattern
        
        # If no pattern matched, raise error
        raise CoordinateParsingError(f"Unable to parse coordinate string: '{coord_str}'")
    
    def parse_coordinate_list(self, coord_strings: List[str]) -> List[ParsedCoordinate]:
        """
        Parse a list of coordinate strings.
        
        Args:
            coord_strings: List of coordinate strings
            
        Returns:
            List of ParsedCoordinate objects
            
        Raises:
            CoordinateParsingError: If any parsing fails
        """
        results = []
        errors = []
        
        for i, coord_str in enumerate(coord_strings):
            try:
                result = self.parse_coordinate(coord_str)
                results.append(result)
            except CoordinateParsingError as e:
                errors.append(f"Index {i}: {str(e)}")
        
        if errors:
            raise CoordinateParsingError(f"Failed to parse {len(errors)} coordinates: {'; '.join(errors)}")
        
        return results
    
    def _determine_precision(self, coord_str: str) -> int:
        """
        Determine the decimal precision from the coordinate string.
        
        Args:
            coord_str: Original coordinate string
            
        Returns:
            Number of decimal places
        """
        # Find all decimal numbers in the string
        decimal_pattern = r'(\d+\.\d+)'
        matches = re.findall(decimal_pattern, coord_str)
        
        if not matches:
            return 0  # No decimal places found
        
        # Find maximum decimal places
        max_precision = 0
        for match in matches:
            decimal_places = len(match.split('.')[1])
            max_precision = max(max_precision, decimal_places)
        
        return min(max_precision, 10)  # Cap at 10 decimal places
    
    def _validate_coordinate_values(self, x: float, y: float, z: float) -> None:
        """
        Validate coordinate values for reasonableness.
        
        Args:
            x, y, z: Coordinate values to validate
            
        Raises:
            CoordinateParsingError: If values are invalid
        """
        # Check for NaN or infinite values
        for coord, name in [(x, 'x'), (y, 'y'), (z, 'z')]:
            if not isinstance(coord, (int, float)) or coord != coord:  # NaN check
                raise CoordinateParsingError(f"Invalid {name} coordinate: {coord}")
            
            if abs(coord) == float('inf'):
                raise CoordinateParsingError(f"Infinite {name} coordinate: {coord}")
        
        # Check for reasonable coordinate ranges (configurable)
        max_coord = 1e8  # 100 million units
        for coord, name in [(x, 'x'), (y, 'y'), (z, 'z')]:
            if abs(coord) > max_coord:
                logger.warning(f"Very large {name} coordinate: {coord}")
    
    def format_coordinate(self, x: float, y: float, z: float, precision: Optional[int] = None) -> str:
        """
        Format coordinates back to string representation.
        
        Args:
            x, y, z: Coordinate values
            precision: Decimal precision (uses default if None)
            
        Returns:
            Formatted coordinate string
        """
        if precision is None:
            precision = self.default_precision
        
        format_str = f"{{:.{precision}f}}"
        return f"({format_str.format(x)}, {format_str.format(y)}, {format_str.format(z)})"
    
    def validate_coordinate_string(self, coord_str: str) -> bool:
        """
        Validate a coordinate string without parsing.
        
        Args:
            coord_str: Coordinate string to validate
            
        Returns:
            True if string is valid coordinate format
        """
        try:
            self.parse_coordinate(coord_str)
            return True
        except CoordinateParsingError:
            return False
    
    @classmethod
    def create_strict_parser(cls) -> 'CoordinateParser':
        """
        Create a parser that only accepts standard "(x, y, z)" format.
        
        Returns:
            CoordinateParser in strict mode
        """
        return cls(strict_mode=True)
    
    @classmethod
    def create_lenient_parser(cls) -> 'CoordinateParser':
        """
        Create a parser that accepts various coordinate formats.
        
        Returns:
            CoordinateParser in lenient mode
        """
        return cls(strict_mode=False)


# Convenience functions for common operations
def parse_coordinate_string(coord_str: str, strict: bool = False) -> Tuple[float, float, float]:
    """
    Parse a coordinate string and return tuple.
    
    Args:
        coord_str: Coordinate string to parse
        strict: Whether to use strict parsing
        
    Returns:
        Tuple of (x, y, z) coordinates
        
    Raises:
        CoordinateParsingError: If parsing fails
    """
    parser = CoordinateParser(strict_mode=strict)
    result = parser.parse_coordinate(coord_str)
    return result.to_tuple()


def validate_coordinate_format(coord_str: str, strict: bool = False) -> bool:
    """
    Validate coordinate string format.
    
    Args:
        coord_str: Coordinate string to validate
        strict: Whether to use strict validation
        
    Returns:
        True if format is valid
    """
    parser = CoordinateParser(strict_mode=strict)
    return parser.validate_coordinate_string(coord_str)


def format_coordinates(x: float, y: float, z: float, precision: int = 6) -> str:
    """
    Format coordinates to standard string representation.
    
    Args:
        x, y, z: Coordinate values
        precision: Decimal precision
        
    Returns:
        Formatted coordinate string
    """
    parser = CoordinateParser(default_precision=precision)
    return parser.format_coordinate(x, y, z)


# Example usage and testing
if __name__ == "__main__":
    # Test the parser with various formats
    test_coordinates = [
        "(0, -57400, 0)",
        "(33800, -57400, 0)",
        "33800.0, -71500.0, 0.0",
        "0 -71500 0",
        "(1234.567, -8901.234, 0.000)"
    ]
    
    parser = CoordinateParser()
    
    for coord_str in test_coordinates:
        try:
            result = parser.parse_coordinate(coord_str)
            print(f"'{coord_str}' -> {result.to_tuple()} (precision: {result.precision})")
        except CoordinateParsingError as e:
            print(f"Failed to parse '{coord_str}': {e}")
