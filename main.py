#!/usr/bin/env python3
"""
Main Application Entry Point with Authentication
===============================================

This module provides the main entry point for the Column Drawing Generator
application with integrated user authentication.

The application follows a two-stage process:
1. User authentication via email-based login system
2. Access to the main column drawing GUI application

Features:
    - Secure email-based authentication system
    - User license verification
    - Session management with timeout
    - Integration with column drawing GUI
    - Professional application window management
    - Comprehensive error handling and logging

Usage:
    python main.py

Author: Generated for Drawing Production System
Date: 2025
"""

import logging
import os
import sys
import time
import tkinter as tk
from tkinter import messagebox
from typing import Optional

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import authentication components from the auth package
from auth import (
    # Authentication functions
    send_password_email,
    send_email_log,
    check_user_license,
    generate_password_with_salt,
    validate_password,
    clear_password_data,
    
    # Session management
    SessionManager,
    create_session_manager,
    
    # UI components
    create_menu_bar,
    show_error_message,
    show_info_message,
    show_warning_message,
    
    # Constants
    MAX_LOGIN_ATTEMPTS,
    DEVELOPER_MODE,
    FONT_TITLE,
    FONT_NORMAL,
    FONT_SMALL,
    BUTTON_WIDTH_LARGE,
    BUTTON_HEIGHT_LARGE,
    BUTTON_WIDTH_MEDIUM,
    BUTTON_HEIGHT_MEDIUM,
    ENTRY_WIDTH
)

# Import the column drawing GUI
from column_drawing.column_drawing_gui import ColumnDrawingGUI
from version_config import APP_NAME, APP_TITLE


class AuthenticatedColumnDrawingApp:
    """
    Main application class that integrates authentication with column drawing GUI.
    
    This class manages the complete application lifecycle:
    1. Initial authentication screen
    2. User verification and license checking
    3. Transition to main application GUI
    4. Session management and logout functionality
    
    Attributes:
        root (tk.Tk): Root window for the application
        login_status (bool): Current authentication status
        user_name (str): Authenticated username
        user_email (str): User's email address
        session_manager (SessionManager): Session management instance
        login_attempts (int): Number of failed login attempts
        main_app (ColumnDrawingGUI): Main application instance
        password_hash (str): Hashed password for validation
        password_salt (str): Salt used for password hashing
    """
    
    def __init__(self) -> None:
        """
        Initialize the authenticated application.
        
        Sets up the root window, initializes authentication state,
        and displays the login screen.
        """
        # Create main application window
        self.root = tk.Tk()
        self.root.title(APP_TITLE)
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Try to set application icon
        try:
            self.root.iconbitmap('AIS.ico')
        except Exception as e:
            logging.debug(f"Application icon not loaded: {str(e)}")
        
        # Initialize authentication state
        self.login_status = False
        self.user_name = ""
        self.user_email = ""
        self.login_attempts = 0
        self.main_app: Optional[ColumnDrawingGUI] = None
        
        # Authentication data (will be set during password generation)
        self.password_hash = ""
        self.password_salt = ""
        
        # Session management
        self.session_manager = create_session_manager()
        
        # Create menu bar
        self.create_menu_bar()
        
        # Show login screen
        self.show_login_screen()
        
        # Configure window close protocol
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
    
    def send_password(self) -> None:
        """
        Generate and send password to user's email.
        
        This method is called by the login system when user requests password.
        """
        # Validate username
        self.user_name = self.username_entry.get().strip()
        if not self.user_name:
            show_error_message("Error", "Please enter your username")
            return

        # Store the full email address
        self.user_email = f"{self.user_name}@asiainfrasolutions.com"

        # First check if the user is in the approved license list
        if not check_user_license(self.user_name):
            show_error_message("Unauthorized",
                               "Sorry, your username is not in the approved license list. Please contact support.")
            # Log the unauthorized attempt
            send_email_log(
                user_name=self.user_name,
                case="Unauthorized Login Attempt",
                user_email=self.user_email
            )
            return

        # Reset login attempts when requesting new password
        self.login_attempts = 0
        
        # Generate password, salt, and compute hash
        try:
            plain_password, self.password_salt, self.password_hash = generate_password_with_salt()

            # Send the plain password via email
            success = send_password_email(self.user_name, plain_password)
            if success:
                send_email_log(
                    user_name=self.user_name,
                    case="Password Request - Column Drawing Application",
                    user_email=self.user_email
                )
                show_info_message("Success", f"Password sent to {self.user_email}")
            else:
                show_error_message("Error", "Failed to send password email. Please check your internet connection.")
        except Exception as e:
            logging.error(f"Password generation error: {str(e)}")
            show_error_message("Error", f"Failed to send password: {str(e)}")
    
    def login(self) -> None:
        """
        Authenticate user with provided password.
        
        Uses password hash validation to authenticate user.
        """
        # Get the password input
        input_password = self.password_entry.get().strip()

        # Verify password hash exists
        if not self.password_hash:
            show_error_message("Error", "Please request a password first")
            return

        # Validate the password
        is_valid = validate_password(input_password, self.password_salt, self.password_hash)

        if is_valid:
            # Reset login attempts counter on successful login
            self.login_attempts = 0
            self.login_status = True
            
            # Start session
            self.session_manager.start_session()

            # Securely clear password data from memory
            self.password_hash, self.password_salt = clear_password_data(self.password_hash, self.password_salt)
            self.password_entry.delete(0, 'end')

            show_info_message("Success", f"Welcome, {self.user_name}")
            self.show_main_frame()
        else:
            # Increment login attempts counter on failed login
            self.login_attempts += 1

            # Check if maximum attempts reached
            if self.login_attempts >= MAX_LOGIN_ATTEMPTS:
                # Log the security event
                send_email_log(
                    user_name=self.user_name,
                    case="Security Alert: Maximum Login Attempts Exceeded",
                    user_email=self.user_email
                )

                # Show error message
                show_error_message("Security Alert",
                                  f"Maximum login attempts ({MAX_LOGIN_ATTEMPTS}) exceeded. "
                                  "Application will exit for security reasons.")

                # Force exit the application
                self.root.after(200, self.force_security_exit)
            else:
                # Show regular error with attempts remaining
                remaining = MAX_LOGIN_ATTEMPTS - self.login_attempts
                show_error_message("Error", f"Wrong Password! Please try again. "
                                            f"Attempts remaining: {remaining}")

    def developer_login(self) -> None:
        """
        Developer login method that bypasses authentication for quick testing.
        
        This method is only available when DEVELOPER_MODE is True and provides
        a quick way to access the main application during development without
        going through the full authentication process.
        """
        if not DEVELOPER_MODE:
            show_error_message("Error", "Developer mode is not enabled")
            return
        
        # Set up developer authentication state
        self.user_name = "developer"
        self.user_email = "<EMAIL>"
        self.login_status = True
        self.login_attempts = 0
        
        # Start session
        self.session_manager.start_session()
        
        # Log developer mode usage
        logging.info("Developer mode login initiated")
        
        # Show success message
        show_info_message("Developer Mode", "Developer login successful!")
        
        # Launch main application
        self.show_main_frame()
    
    def force_security_exit(self) -> None:
        """
        Force application exit for security reasons.
        
        Called when maximum login attempts exceeded.
        """
        logging.warning(f"Security exit triggered after {MAX_LOGIN_ATTEMPTS} failed login attempts")
        try:
            self.root.destroy()
            os._exit(1)  # Use exit code 1 for abnormal exit
        except Exception as e:
            logging.error(f"Error during security exit: {e}")
            sys.exit(1)  # Fallback exit method
    
    def create_menu_bar(self) -> None:
        """
        Create the application menu bar.
        
        Provides standard menu options including File and Help menus
        with appropriate authentication-aware options.
        """
        create_menu_bar(
            self.root,
            is_logged_in=self.login_status,
            logout_callback=self.logout if self.login_status else None,
            exit_callback=self.on_window_close,
            about_callback=self.show_about
        )
    
    def show_login_screen(self) -> None:
        """
        Display the login authentication screen.
        
        Uses the auth package to provide secure user authentication 
        with email-based password delivery.
        """
        # Clear the window (except menu)
        for widget in self.root.winfo_children():
            if not isinstance(widget, tk.Menu):
                widget.destroy()
        
        # Recreate the menu bar
        self.create_menu_bar()
        
        # Try to set window icon if available
        try:
            self.root.iconbitmap('AIS.ico')
        except Exception as e:
            logging.debug(f"Icon not loaded: {str(e)}")
        
        # Create header
        header_frame = tk.Frame(self.root)
        header_frame.pack(fill="x", padx=20, pady=10)
        
        title_label = tk.Label(header_frame, text="Drafting Agent", font=FONT_TITLE)
        title_label.pack(anchor="w")
        
        separator = tk.Frame(self.root, height=2, bg="gray")
        separator.pack(fill="x", padx=20, pady=5)
        
        # Create login form
        login_frame = tk.Frame(self.root)
        login_frame.pack(expand=True, fill="both", padx=40, pady=20)
        
        # Username entry
        tk.Label(login_frame, text="User Name (e.g. john.doe):", font=FONT_NORMAL).pack(anchor="w", pady=(10, 0))
        tk.Label(login_frame, text="User Name = <EMAIL>", font=FONT_SMALL).pack(anchor="w")
        
        self.username_entry = tk.Entry(login_frame, width=ENTRY_WIDTH)
        self.username_entry.pack(pady=(5, 20), fill="x")
        self.username_entry.focus()  # Set focus on username field
        
        # Get password button
        get_password_btn = tk.Button(login_frame, text="Send Password to Email",
                                   command=self.send_password, 
                                   width=BUTTON_WIDTH_LARGE, 
                                   height=BUTTON_HEIGHT_LARGE)
        get_password_btn.pack(pady=10)
        
        # Password entry
        tk.Label(login_frame, text="Enter the 30-unit password:", font=FONT_NORMAL).pack(anchor="w", pady=(10, 0))
        
        self.password_entry = tk.Entry(login_frame, width=ENTRY_WIDTH, show="*")
        self.password_entry.pack(pady=(5, 20), fill="x")
        
        # Bind Enter key to login
        self.password_entry.bind('<Return>', lambda event: self.login())
        
        # Login button
        login_btn = tk.Button(login_frame, text="Login", 
                            command=self.login, 
                            width=BUTTON_WIDTH_LARGE, 
                            height=BUTTON_HEIGHT_LARGE)
        login_btn.pack(pady=10)
        
        # Developer button (only shown in developer mode)
        if DEVELOPER_MODE:
            dev_login_btn = tk.Button(login_frame, text="Developer Login", 
                                    command=self.developer_login,
                                    width=BUTTON_WIDTH_MEDIUM,
                                    height=BUTTON_HEIGHT_MEDIUM)
            dev_login_btn.pack(pady=10)
    
    def show_main_frame(self) -> None:
        """
        Display the main column drawing application after successful authentication.
        
        This method is called by the login system after successful user verification.
        It transitions from the login screen to the main application GUI.
        """
        try:
            # Log successful login
            send_email_log(
                user_name=self.user_name,
                case="Successful Login - Column Drawing Application",
                user_email=self.user_email
            )
            
            # Clear the login screen
            for widget in self.root.winfo_children():
                if not isinstance(widget, tk.Menu):
                    widget.destroy()
            
            # Update window title to include user information
            title_suffix = f" - User: {self.user_name}"
            if DEVELOPER_MODE and self.user_name == "developer":
                title_suffix += " [DEVELOPER MODE]"
            self.root.title(f"{APP_TITLE}{title_suffix}")
            
            # Create status bar with timer
            status_frame = tk.Frame(self.root)
            status_frame.pack(fill="x", side="bottom")
            
            # User info label
            user_label = tk.Label(status_frame, text=f"Logged in as: {self.user_name}", 
                                font=FONT_NORMAL)
            user_label.pack(side="left", padx=5)
            
            # Timer label
            self.time_label = tk.Label(status_frame, text="Session time: 15:00", 
                                     font=FONT_NORMAL)
            self.time_label.pack(side="right", padx=5)
            
            # Create main application frame
            main_frame = tk.Frame(self.root)
            main_frame.pack(fill="both", expand=True)
            
            # Hide the main window since ColumnDrawingGUI creates its own window
            self.root.withdraw()
            
            # Initialize the column drawing GUI with user context
            self.main_app = ColumnDrawingGUI(
                parent=self.root,
                user_name=self.user_name,
                user_email=self.user_email
            )
            
            # Configure the main app window to show our authenticated window when closed
            self.main_app.window.protocol("WM_DELETE_WINDOW", self.on_main_app_close)
            
            # Start session timer monitoring
            self.monitor_session()
            
            # Update menu bar for logged-in state
            self.create_menu_bar()
            
            logging.info(f"Main application loaded successfully for user: {self.user_name}")
            
        except Exception as e:
            logging.error(f"Error loading main application: {str(e)}")
            show_error_message("Error", f"Failed to load main application: {str(e)}")
            self.show_login_screen()
    
    def monitor_session(self) -> None:
        """
        Monitor session timeout and update timer display.
        """
        if self.login_status and self.session_manager.is_session_valid():
            # Update timer display
            remaining_time = self.session_manager.format_remaining_time()
            if hasattr(self, 'time_label') and self.time_label.winfo_exists():
                self.time_label.config(text=f"Session: {remaining_time}")
            
            # Check for timeout warnings
            remaining_seconds = self.session_manager.get_remaining_time()
            if remaining_seconds <= 120 and remaining_seconds > 60:  # 2 minutes warning
                if not hasattr(self, 'warning_shown'):
                    self.warning_shown = True
                    show_warning_message("Session Warning", 
                                       "Your session will expire in 2 minutes! Please save your work.")
            
            # Schedule next check
            self.root.after(1000, self.monitor_session)
        else:
            # Session expired
            if self.login_status:
                self.logout()
    
    def logout(self) -> None:
        """
        Log out the current user and return to login screen.
        
        Clears authentication state, logs the logout event,
        and returns to the authentication screen.
        """
        if self.login_status:
            # Log the logout event
            send_email_log(
                user_name=self.user_name,
                case="User Logout - Column Drawing Application",
                user_email=self.user_email
            )
            
            # Clear authentication state
            self.login_status = False
            self.session_manager.end_session()
            
            # Destroy main app if it exists
            if self.main_app and hasattr(self.main_app, 'window'):
                try:
                    self.main_app.window.destroy()
                except Exception:
                    pass
                self.main_app = None
            
            # Show the main authentication window
            self.root.deiconify()
            
            # Show confirmation
            show_info_message("Logged Out", f"User {self.user_name} has been logged out.")
            
            # Reset user information
            self.user_name = ""
            self.user_email = ""
            
            # Return to login screen
            self.show_login_screen()
    
    def on_window_close(self) -> None:
        """
        Handle application window close event.
        
        Ensures proper cleanup and logging when the application is closed.
        """
        if self.login_status:
            # Log the application close event
            send_email_log(
                user_name=self.user_name,
                case="Application Exit - Column Drawing Application",
                user_email=self.user_email
            )
        
        # Close the application
        try:
            self.root.destroy()
        except Exception:
            pass
        
        sys.exit(0)
    
    def show_about(self) -> None:
        """
        Display application information dialog.
        
        Shows version information, copyright, and application details.
        """
        from version_config import APP_VERSION_V_PREFIX as APP_VERSION
        
        about_text = f"""
{APP_NAME}
Version: {APP_VERSION}

A professional technical drawing generator for 
reinforced concrete column design documentation.

Features:
• Automated DXF drawing generation
• BS8666 compliant rebar representations  
• Professional engineering documentation

Copyright © 2025 Alex Sze
All rights reserved.
        """
        
        show_info_message("About", about_text)
    
    def on_main_app_close(self) -> None:
        """
        Handle main application window close event.
        
        Called when user closes the main column drawing GUI window.
        Returns to login screen or exits application based on user choice.
        """
        # Ask user if they want to logout or exit
        result = messagebox.askyesnocancel(
            "Close Application",
            "Do you want to:\n"
            "Yes - Logout and return to login screen\n"
            "No - Exit application completely\n"
            "Cancel - Continue using application"
        )
        
        if result is True:  # Yes - Logout
            # Show the main authentication window again
            self.root.deiconify()
            self.logout()
        elif result is False:  # No - Exit completely
            self.on_window_close()
        # Cancel - do nothing, continue using application
    
    def run(self) -> None:
        """
        Start the application main event loop.
        
        Begins the tkinter event loop for user interaction.
        This method blocks until the application is closed.
        """
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logging.info("Application interrupted by user")
        except Exception as e:
            logging.error(f"Unexpected error in main loop: {str(e)}")
        finally:
            # Ensure proper cleanup
            if self.login_status:
                send_email_log(
                    user_name=self.user_name,
                    case="Application Terminated - Column Drawing Application",
                    user_email=self.user_email
                )


def main() -> None:
    """
    Main entry point for the authenticated column drawing application.
    
    Initializes logging, creates the application instance, and starts
    the main event loop.
    
    Usage:
        python main.py
    """
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    from version_config import APP_VERSION_V_PREFIX as APP_VERSION
    
    logging.info("Starting Column Drawing Application with Authentication")
    logging.info(f"Application Version: {APP_VERSION}")
    
    try:
        app = AuthenticatedColumnDrawingApp()
        app.run()
    except Exception as e:
        logging.error(f"Fatal error starting application: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()