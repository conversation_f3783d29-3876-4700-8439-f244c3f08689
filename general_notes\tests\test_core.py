"""
Unit Tests for Core Components
==============================

Tests for core application functionality including ApplicationCore,
BusinessLogic, and DependencyContainer.

Author: Drawing Production System
"""

import unittest
import tempfile
import os
from pathlib import Path

from ..core.application_core import ApplicationCore
from ..core.business_logic import BusinessLogic, BusinessLogicError
from ..core.dependency_injection import DependencyContainer, ComponentLifecycle
from ..core.constants import APPLICATION_NAME, APPLICATION_VERSION
from ..models.configuration import GeneralNotesConfig
from ..models.note_detail import NoteDetail, NoteDetailCollection
from ..models.boundary import Boundary, BoundaryPoint


class TestApplicationCore(unittest.TestCase):
    """Test ApplicationCore class."""
    
    def setUp(self):
        """Set up test application core."""
        self.config = GeneralNotesConfig.create_development()
        self.core = ApplicationCore(self.config)
    
    def test_initialization(self):
        """Test ApplicationCore initialization."""
        self.assertIsNotNone(self.core.config)
        self.assertIsNotNone(self.core.business_logic)
        self.assertIsNotNone(self.core.dependencies)
        self.assertIsNotNone(self.core.excel_parser)
        self.assertIsNotNone(self.core.dxf_parser)
        self.assertIsNotNone(self.core.selection_state)
    
    def test_workflow_prerequisites(self):
        """Test workflow prerequisites validation."""
        prerequisites = self.core.validate_workflow_prerequisites()
        
        # Initially, nothing should be loaded
        self.assertFalse(prerequisites['note_collection_loaded'])
        self.assertFalse(prerequisites['dxf_document_loaded'])
        self.assertFalse(prerequisites['entity_catalog_created'])
        self.assertFalse(prerequisites['has_selected_notes'])
        self.assertFalse(prerequisites['all_prerequisites_met'])
    
    def test_workflow_status(self):
        """Test workflow status reporting."""
        status = self.core.get_workflow_status()
        
        self.assertIn('prerequisites', status)
        self.assertIn('note_collection', status)
        self.assertIn('dxf_database', status)
        self.assertIn('entity_catalog', status)
        self.assertIn('selection', status)
        self.assertIn('configuration', status)
    
    def test_system_info(self):
        """Test system information reporting."""
        info = self.core.get_system_info()
        
        self.assertEqual(info['application']['name'], APPLICATION_NAME)
        self.assertEqual(info['application']['version'], APPLICATION_VERSION)
        self.assertIn('configuration', info)
        self.assertIn('workflow_status', info)
        self.assertIn('dependencies', info)
    
    def test_reset_workflow(self):
        """Test workflow reset functionality."""
        # Set some state
        self.core.selection_state.select_note("test_note")
        
        # Reset
        self.core.reset_workflow()
        
        # Verify reset
        self.assertEqual(len(self.core.selection_state.selected_titles), 0)
        self.assertIsNone(self.core.get_note_collection())
        self.assertIsNone(self.core.get_entity_catalog())
    
    def test_context_manager(self):
        """Test context manager functionality."""
        with ApplicationCore(self.config) as core:
            self.assertIsNotNone(core)
            # Context manager should work without errors


class TestBusinessLogic(unittest.TestCase):
    """Test BusinessLogic class."""
    
    def setUp(self):
        """Set up test business logic."""
        self.config = GeneralNotesConfig.create_default()
        self.business_logic = BusinessLogic(self.config)
    
    def test_file_path_validation(self):
        """Test file path validation."""
        # Test non-existent file
        with self.assertRaises(BusinessLogicError):
            self.business_logic.validate_excel_file_path("nonexistent.xlsx")
        
        with self.assertRaises(BusinessLogicError):
            self.business_logic.validate_dxf_file_path("nonexistent.dxf")
    
    def test_note_collection_validation(self):
        """Test note collection validation."""
        # Test empty collection
        empty_collection = NoteDetailCollection()
        with self.assertRaises(BusinessLogicError):
            self.business_logic.validate_note_collection(empty_collection)
        
        # Test valid collection
        boundary = Boundary(
            BoundaryPoint(0, 0, 0),
            BoundaryPoint(100, 0, 0),
            BoundaryPoint(100, 50, 0),
            BoundaryPoint(0, 50, 0),
            title="Test Note"
        )
        
        note = NoteDetail(title="Test Note", boundary=boundary)
        collection = NoteDetailCollection([note])
        
        # Should not raise exception
        try:
            self.business_logic.validate_note_collection(collection)
        except BusinessLogicError:
            self.fail("Valid collection should not raise BusinessLogicError")
    
    def test_selection_validation(self):
        """Test selection validation."""
        available_titles = ["Note 1", "Note 2", "Note 3"]
        
        # Test valid selection
        selected_titles = ["Note 1", "Note 2"]
        try:
            self.business_logic.validate_selection(selected_titles, available_titles)
        except BusinessLogicError:
            self.fail("Valid selection should not raise BusinessLogicError")
        
        # Test empty selection
        with self.assertRaises(BusinessLogicError):
            self.business_logic.validate_selection([], available_titles)
        
        # Test invalid selection
        with self.assertRaises(BusinessLogicError):
            self.business_logic.validate_selection(["Invalid Note"], available_titles)
    
    def test_output_path_validation(self):
        """Test output path validation."""
        # Test with temporary directory
        with tempfile.TemporaryDirectory() as tmp_dir:
            valid_path = os.path.join(tmp_dir, "output.dxf")
            
            try:
                self.business_logic.validate_output_path(valid_path)
            except BusinessLogicError:
                self.fail("Valid output path should not raise BusinessLogicError")
        
        # Test invalid directory
        invalid_path = "/nonexistent/directory/output.dxf"
        with self.assertRaises(BusinessLogicError):
            self.business_logic.validate_output_path(invalid_path)
    
    def test_business_rules_summary(self):
        """Test business rules summary."""
        summary = self.business_logic.get_business_rules_summary()
        
        self.assertIn('file_constraints', summary)
        self.assertIn('note_constraints', summary)
        self.assertIn('selection_constraints', summary)
        self.assertIn('system_constraints', summary)


class TestDependencyContainer(unittest.TestCase):
    """Test DependencyContainer class."""
    
    def setUp(self):
        """Set up test dependency container."""
        self.config = GeneralNotesConfig.create_default()
        self.container = DependencyContainer(self.config)
    
    def test_core_components_registered(self):
        """Test that core components are registered."""
        self.assertTrue(self.container.is_registered('config'))
        self.assertTrue(self.container.is_registered('excel_parser'))
        self.assertTrue(self.container.is_registered('dxf_parser'))
        self.assertTrue(self.container.is_registered('selection_state'))
    
    def test_component_resolution(self):
        """Test component resolution."""
        config = self.container.resolve('config')
        self.assertIsInstance(config, GeneralNotesConfig)
        
        excel_parser = self.container.resolve('excel_parser')
        self.assertIsNotNone(excel_parser)
    
    def test_singleton_lifecycle(self):
        """Test singleton lifecycle management."""
        config1 = self.container.resolve('config')
        config2 = self.container.resolve('config')
        
        # Should be the same instance
        self.assertIs(config1, config2)
    
    def test_transient_lifecycle(self):
        """Test transient lifecycle management."""
        parser1 = self.container.resolve('excel_parser')
        parser2 = self.container.resolve('excel_parser')
        
        # Should be different instances
        self.assertIsNot(parser1, parser2)
    
    def test_custom_registration(self):
        """Test custom component registration."""
        # Register a test component
        def test_factory():
            return "test_component"
        
        self.container.register_singleton(
            'test_component',
            str,
            test_factory
        )
        
        # Resolve and test
        component = self.container.resolve('test_component')
        self.assertEqual(component, "test_component")
    
    def test_component_info(self):
        """Test component information retrieval."""
        info = self.container.get_component_info('config')
        
        self.assertEqual(info['name'], 'config')
        self.assertEqual(info['lifecycle'], ComponentLifecycle.SINGLETON)
        self.assertTrue(info['is_singleton'])
    
    def test_component_status(self):
        """Test component status reporting."""
        status = self.container.get_component_status()
        
        self.assertIn('total_components', status)
        self.assertIn('singleton_count', status)
        self.assertIn('transient_count', status)
        self.assertIn('components', status)
        
        self.assertGreater(status['total_components'], 0)
    
    def test_container_cleanup(self):
        """Test container cleanup."""
        # Resolve a singleton to create instance
        config = self.container.resolve('config')
        self.assertIsNotNone(config)
        
        # Cleanup
        self.container.cleanup()
        
        # Verify cleanup (this is implementation-dependent)
        # The main thing is that cleanup doesn't raise exceptions
    
    def test_missing_component(self):
        """Test resolution of missing component."""
        with self.assertRaises(KeyError):
            self.container.resolve('nonexistent_component')
    
    def test_container_contains(self):
        """Test container 'in' operator."""
        self.assertTrue('config' in self.container)
        self.assertFalse('nonexistent' in self.container)
    
    def test_container_indexing(self):
        """Test container indexing syntax."""
        config = self.container['config']
        self.assertIsInstance(config, GeneralNotesConfig)


if __name__ == '__main__':
    unittest.main()
