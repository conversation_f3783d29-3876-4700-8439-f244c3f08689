"""
Specialized Arrow Drawers
========================

Specialized classes for drawing different types of arrows in section drawings,
including 52 link arrows, 25A link arrows, and their corresponding annotations.
"""

import logging
from typing import List, Tuple, Optional
from abc import ABC, abstractmethod
from ezdxf.enums import TextEntityAlignment

from ...models.column_config import ColumnConfig
from ...models.zone_config import ZoneConfig
from ...models.rebar_layer_data import RebarLayerData
from ...models.arrow_config import get_arrow_config
from .arrow_utils import ArrowPositionCalculator, LinkMarkProvider, ArrowGeometry, CoordinateProcessor

logger = logging.getLogger(__name__)


class BaseArrowDrawer(ABC):
    """
    Base class for arrow drawing operations.

    Provides common functionality for all arrow types including
    layer management, DXF writing, and basic arrow geometry.
    """

    def __init__(self, msp, dxf_writer, drawing_config):
        """
        Initialize base arrow drawer.

        Args:
            msp: DXF model space for drawing
            dxf_writer: DXF writer instance
            drawing_config: Drawing configuration
        """
        self.msp = msp
        self.dxf_writer = dxf_writer
        self.config = drawing_config
        self.arrow_config = get_arrow_config()
        self.geometry = ArrowGeometry()

    def _get_arrow_layer(self) -> str:
        """Get the appropriate layer for arrow drawing."""
        if self.dxf_writer:
            layer = self.dxf_writer.get_layer_for_element("dimensions")
            self.dxf_writer.ensure_layer_exists(layer)
            return layer
        return self.arrow_config.layers.default_layer

    def _draw_arrow_shaft(self, start_pos: Tuple[float, float], end_pos: Tuple[float, float], layer: str) -> None:
        """Draw arrow shaft line."""
        self.msp.add_line(
            start_pos,
            end_pos,
            dxfattribs={
                'layer': layer,
                'color': self.arrow_config.layers.color,
                'lineweight': self.arrow_config.dimensions.shaft_lineweight
            }
        )

    def _draw_arrow_head(self, points: List[Tuple[float, float]], layer: str) -> None:
        """Draw arrow head triangle."""
        self.msp.add_lwpolyline(
            points,
            dxfattribs={
                'layer': layer,
                'color': self.arrow_config.layers.color,
                'lineweight': self.arrow_config.dimensions.shaft_lineweight
            }
        )

    def _add_arrow_text(
        self,
        position: Tuple[float, float],
        text: str,
        scale: float,
        layer: str,
        alignment: TextEntityAlignment
    ) -> None:
        """Add text annotation to arrow."""
        text_height = self.arrow_config.calculate_adaptive_text_height(scale)

        self.msp.add_text(
            text,
            height=text_height,
            dxfattribs={
                'layer': layer,
                'color': self.arrow_config.text.text_color,
                'style': self.arrow_config.text.text_style
            }
        ).set_placement(position, align=alignment)

    @abstractmethod
    def draw_arrows(
        self,
        column_config: ColumnConfig,
        zone_config: ZoneConfig,
        rebar_data: RebarLayerData,
        scale: float,
        detail_x: float,
        detail_y: float,
        scaled_width: float,
        scaled_height: float
    ) -> int:
        """
        Draw arrows for the specific arrow type.

        Returns:
            Number of arrows drawn
        """
        pass


class Link52ArrowDrawer(BaseArrowDrawer):
    """Drawer for 52 link arrows (rectangular stirrups)."""

    def draw_arrows(
        self,
        column_config: ColumnConfig,
        zone_config: ZoneConfig,
        rebar_data: RebarLayerData,
        scale: float,
        detail_x: float,
        detail_y: float,
        scaled_width: float,
        scaled_height: float
    ) -> int:
        """
        Draw horizontal arrows for 52 link vertical lines.

        Draws 2 arrows (left and right vertical lines of rectangular stirrups)
        positioned at Y level between 1st and 2nd rebar rows.
        """
        try:
            coord_processor = CoordinateProcessor()
            x_coords, y_coords = coord_processor.extract_unique_coordinates(
                rebar_data)

            if len(x_coords) < 2 or len(y_coords) < 2:
                logger.debug("Insufficient coordinates for 52 link arrows")
                return 0

            # Calculate arrow Y position (between 1st and 2nd rebar rows)
            y_level_ratio = self.arrow_config.get_y_level_ratios('52')
            arrow_y = coord_processor.calculate_y_level_position(
                y_coords, y_level_ratio)

            if arrow_y is None:
                logger.debug("Cannot calculate Y position for 52 link arrows")
                return 0

            # Calculate arrow positions
            position_calc = ArrowPositionCalculator(column_config, zone_config)
            link_x_positions = position_calc.calculate_52_link_positions(
                rebar_data)

            if not link_x_positions:
                logger.debug("No 52 link positions calculated")
                return 0

            # Get link mark
            mark_provider = LinkMarkProvider(column_config, zone_config)
            link_mark = mark_provider.get_52_link_mark()

            # Calculate arrow tail position
            offsets = self.arrow_config.get_arrow_offsets(scale)
            arrow_tail_x = detail_x + scaled_width + offsets['tail_horizontal']

            # Draw arrows
            layer = self._get_arrow_layer()
            arrows_drawn = 0

            for link_x in link_x_positions:
                self._draw_horizontal_arrow(
                    head_x=link_x,
                    head_y=arrow_y,
                    tail_x=arrow_tail_x,
                    tail_y=arrow_y,
                    scale=scale,
                    layer=layer,
                    link_mark=link_mark
                )
                arrows_drawn += 1
                logger.debug(
                    f"Drew 52 link arrow: head=({link_x:.1f}, {arrow_y:.1f})")

            return arrows_drawn

        except Exception as e:
            logger.error(f"Error drawing 52 link arrows: {e}")
            return 0

    def _draw_horizontal_arrow(
        self,
        head_x: float,
        head_y: float,
        tail_x: float,
        tail_y: float,
        scale: float,
        layer: str,
        link_mark: Optional[str] = None
    ) -> None:
        """Draw a single horizontal arrow with optional link mark."""
        # Draw arrow shaft
        self._draw_arrow_shaft((tail_x, tail_y), (head_x, head_y), layer)

        # Draw arrow head
        dimensions = self.arrow_config.get_arrow_dimensions(scale)
        head_points = self.geometry.calculate_horizontal_arrowhead_points(
            head_x, head_y, dimensions['head_length'], dimensions['head_width']
        )
        self._draw_arrow_head(head_points, layer)

        # Add link mark if provided
        if link_mark:
            offsets = self.arrow_config.get_arrow_offsets(scale)
            text_pos = self.geometry.calculate_text_position(
                tail_x, tail_y, offsets['text_horizontal'], 0, is_horizontal=True
            )
            self._add_arrow_text(
                text_pos, link_mark, scale, layer, TextEntityAlignment.MIDDLE_LEFT
            )


class Link25AXArrowDrawer(BaseArrowDrawer):
    """Drawer for 25A X-direction link arrows (vertical connecting links)."""

    def draw_arrows(
        self,
        column_config: ColumnConfig,
        zone_config: ZoneConfig,
        rebar_data: RebarLayerData,
        scale: float,
        detail_x: float,
        detail_y: float,
        scaled_width: float,
        scaled_height: float
    ) -> int:
        """
        Draw horizontal arrows for 25A X-direction link connecting lines.

        Draws arrows for intermediate vertical connecting lines
        positioned at Y level between 2nd and 3rd rebar rows.
        """
        try:
            if not zone_config.has_inner_links():
                logger.debug("No inner link diameter - skipping 25A X arrows")
                return 0

            coord_processor = CoordinateProcessor()
            x_coords, y_coords = coord_processor.extract_unique_coordinates(
                rebar_data)

            if len(x_coords) < 3 or len(y_coords) < 3:
                logger.debug("Insufficient coordinates for 25A X arrows")
                return 0

            # Calculate arrow Y position (between 2nd and 3rd rebar rows)
            y_level_ratio = self.arrow_config.get_y_level_ratios('25A_X')
            arrow_y = coord_processor.calculate_y_level_position(
                y_coords, y_level_ratio)

            if arrow_y is None:
                logger.debug("Cannot calculate Y position for 25A X arrows")
                return 0

            # Calculate arrow positions
            position_calc = ArrowPositionCalculator(column_config, zone_config)
            link_x_positions = position_calc.calculate_25a_x_link_positions(
                rebar_data)

            if not link_x_positions:
                logger.debug("No 25A X link positions calculated")
                return 0

            # Get link mark
            mark_provider = LinkMarkProvider(column_config, zone_config)
            link_mark = mark_provider.get_25a_x_link_mark()

            # Calculate arrow tail position
            offsets = self.arrow_config.get_arrow_offsets(scale)
            arrow_tail_x = detail_x + scaled_width + offsets['tail_horizontal']

            # Draw arrows
            layer = self._get_arrow_layer()
            arrows_drawn = 0

            for link_x in link_x_positions:
                self._draw_horizontal_arrow(
                    head_x=link_x,
                    head_y=arrow_y,
                    tail_x=arrow_tail_x,
                    tail_y=arrow_y,
                    scale=scale,
                    layer=layer,
                    link_mark=link_mark
                )
                arrows_drawn += 1
                logger.debug(
                    f"Drew 25A X arrow: head=({link_x:.1f}, {arrow_y:.1f})")

            return arrows_drawn

        except Exception as e:
            logger.error(f"Error drawing 25A X arrows: {e}")
            return 0

    def _draw_horizontal_arrow(
        self,
        head_x: float,
        head_y: float,
        tail_x: float,
        tail_y: float,
        scale: float,
        layer: str,
        link_mark: Optional[str] = None
    ) -> None:
        """Draw a single horizontal arrow with optional link mark."""
        # Draw arrow shaft
        self._draw_arrow_shaft((tail_x, tail_y), (head_x, head_y), layer)

        # Draw arrow head
        dimensions = self.arrow_config.get_arrow_dimensions(scale)
        head_points = self.geometry.calculate_horizontal_arrowhead_points(
            head_x, head_y, dimensions['head_length'], dimensions['head_width']
        )
        self._draw_arrow_head(head_points, layer)

        # Add link mark if provided
        if link_mark:
            offsets = self.arrow_config.get_arrow_offsets(scale)
            text_pos = self.geometry.calculate_text_position(
                tail_x, tail_y, offsets['text_horizontal'], 0, is_horizontal=True
            )
            self._add_arrow_text(
                text_pos, link_mark, scale, layer, TextEntityAlignment.MIDDLE_LEFT
            )


class Link25AYArrowDrawer(BaseArrowDrawer):
    """Drawer for 25A Y-direction link arrows (horizontal connecting links)."""

    def draw_arrows(
        self,
        column_config: ColumnConfig,
        zone_config: ZoneConfig,
        rebar_data: RebarLayerData,
        scale: float,
        detail_x: float,
        detail_y: float,
        scaled_width: float,
        scaled_height: float
    ) -> int:
        """
        Draw vertical arrows for 25A Y-direction link connecting lines.

        Draws arrows pointing upward to horizontal connecting lines
        positioned at middle between 1st and 2nd left rebar columns.
        """
        try:
            if not zone_config.has_inner_links():
                logger.debug("No inner link diameter - skipping 25A Y arrows")
                return 0

            coord_processor = CoordinateProcessor()
            x_coords, y_coords = coord_processor.extract_unique_coordinates(
                rebar_data)

            if len(x_coords) < 3 or len(y_coords) < 3:
                logger.debug("Insufficient coordinates for 25A Y arrows")
                return 0

            # Calculate arrow positions
            position_calc = ArrowPositionCalculator(column_config, zone_config)
            link_y_positions = position_calc.calculate_25a_y_link_positions(
                rebar_data)

            if not link_y_positions:
                logger.debug("No 25A Y link positions calculated")
                return 0

            # Calculate arrow X position (between 1st and 2nd left rebar)
            if len(x_coords) >= 2:
                arrow_x = (x_coords[0] + x_coords[1]) / 2
            else:
                arrow_x = detail_x + 10  # Fallback position

            # Get link mark
            mark_provider = LinkMarkProvider(column_config, zone_config)
            link_mark = mark_provider.get_25a_y_link_mark()

            # Calculate arrow tail position
            offsets = self.arrow_config.get_arrow_offsets(scale)
            arrow_tail_y = detail_y - offsets['tail_vertical']

            # Draw arrows
            layer = self._get_arrow_layer()
            arrows_drawn = 0

            for link_y in link_y_positions:
                self._draw_vertical_arrow(
                    head_x=arrow_x,
                    head_y=link_y,
                    tail_x=arrow_x,
                    tail_y=arrow_tail_y,
                    scale=scale,
                    layer=layer,
                    link_mark=link_mark
                )
                arrows_drawn += 1
                logger.debug(
                    f"Drew 25A Y arrow: head=({arrow_x:.1f}, {link_y:.1f})")

            return arrows_drawn

        except Exception as e:
            logger.error(f"Error drawing 25A Y arrows: {e}")
            return 0

    def _draw_vertical_arrow(
        self,
        head_x: float,
        head_y: float,
        tail_x: float,
        tail_y: float,
        scale: float,
        layer: str,
        link_mark: Optional[str] = None
    ) -> None:
        """Draw a single vertical arrow with optional link mark."""
        # Draw arrow shaft
        self._draw_arrow_shaft((tail_x, tail_y), (head_x, head_y), layer)

        # Draw arrow head
        dimensions = self.arrow_config.get_arrow_dimensions(scale)
        head_points = self.geometry.calculate_vertical_arrowhead_points(
            head_x, head_y, dimensions['head_length'], dimensions['head_width']
        )
        self._draw_arrow_head(head_points, layer)

        # Add link mark if provided
        if link_mark:
            offsets = self.arrow_config.get_arrow_offsets(scale)
            text_pos = self.geometry.calculate_text_position(
                tail_x, tail_y, 0, -offsets['text_vertical'], is_horizontal=False
            )
            self._add_arrow_text(
                text_pos, link_mark, scale, layer, TextEntityAlignment.MIDDLE_CENTER
            )


class ArrowDrawerFactory:
    """Factory for creating appropriate arrow drawer instances."""

    @staticmethod
    def create_arrow_drawers(msp, dxf_writer, drawing_config) -> List[BaseArrowDrawer]:
        """
        Create all arrow drawer instances.

        Args:
            msp: DXF model space
            dxf_writer: DXF writer instance
            drawing_config: Drawing configuration

        Returns:
            List of arrow drawer instances
        """
        return [
            Link52ArrowDrawer(msp, dxf_writer, drawing_config),
            Link25AXArrowDrawer(msp, dxf_writer, drawing_config),
            Link25AYArrowDrawer(msp, dxf_writer, drawing_config)
        ]
