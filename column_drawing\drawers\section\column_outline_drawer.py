"""
Column Outline Drawing Module
============================

Specialized module for drawing rectangular column boundaries on appropriate AIA-standard layers.
Handles column outline rectangles with proper layer management and line weights.
"""

import logging
from typing import TYPE_CHECKING, List, Tuple
from ezdxf import const

if TYPE_CHECKING:
    from ezdxf.layouts import Modelspace
    from ...models.drawing_config import DrawingConfig
    from ...io.dxf_writer import DXFWriter

logger = logging.getLogger(__name__)


class ColumnOutlineDrawer:
    """
    Specialized drawer for column outline rectangles.

    This class handles drawing rectangular column boundaries with:
    - Proper AIA layer management (S-CONC-OTLN)
    - Appropriate line weights and colors
    - Error handling and logging

    Follows single responsibility principle by focusing solely on column outline drawing.
    """

    def __init__(self, msp: 'Modelspace', dxf_writer: 'DXFWriter', config: 'DrawingConfig'):
        """
        Initialize the column outline drawer.

        Args:
            msp: DXF modelspace for drawing operations
            dxf_writer: DXF writer with layer management capabilities
            config: Drawing configuration for styling parameters
        """
        self.msp = msp
        self.dxf_writer = dxf_writer
        self.config = config

        logger.debug("Initialized ColumnOutlineDrawer")

    def draw_column_outline(self, x: float, y: float, width: float, height: float) -> None:
        """
        Draw a rectangular column outline on the appropriate AIA-standard layer.

        Creates a closed polyline rectangle representing the column boundary
        using proper layer management and styling consistent with AIA standards.

        Args:
            x: Bottom-left corner X coordinate (mm)
            y: Bottom-left corner Y coordinate (mm)
            width: Column width dimension (mm)
            height: Column height dimension (mm)

        Raises:
            Exception: If drawing operation fails
        """
        try:
            # Get appropriate layer for column outline using layer management system
            outline_layer = self._get_outline_layer()

            # Create closed polyline for column outline
            outline_points = [
                (x, y),                      # Bottom-left
                (x + width, y),              # Bottom-right
                (x + width, y + height),     # Top-right
                (x, y + height),             # Top-left
                (x, y)                       # Close the rectangle
            ]

            # Draw the column outline with ByLayer properties
            self.msp.add_lwpolyline(
                outline_points,
                dxfattribs={
                    'layer': outline_layer,
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'
                }
            )

            logger.debug(
                f"Drew column outline: {width:.1f}x{height:.1f} mm at ({x:.1f}, {y:.1f}) "
                f"on layer {outline_layer}"
            )

        except Exception as e:
            logger.error(f"Error drawing column outline: {e}")
            raise

    def draw_multiple_column_outlines(self, outline_specs: List[Tuple[float, float, float, float]]) -> None:
        """
        Draw multiple column outlines efficiently.

        Batch drawing operation for multiple column outlines using consistent
        layer and styling parameters.

        Args:
            outline_specs: List of (x, y, width, height) tuples for each outline

        Raises:
            Exception: If any drawing operation fails
        """
        try:
            outline_layer = self._get_outline_layer()

            for i, (x, y, width, height) in enumerate(outline_specs):
                outline_points = [
                    (x, y),
                    (x + width, y),
                    (x + width, y + height),
                    (x, y + height),
                    (x, y)
                ]

                self.msp.add_lwpolyline(
                    outline_points,
                    dxfattribs={
                        'layer': outline_layer,
                        'color': const.BYLAYER,
                        'lineweight': const.LINEWEIGHT_BYLAYER,
                        'linetype': 'ByLayer'
                    }
                )

            logger.debug(
                f"Drew {len(outline_specs)} column outlines on layer {outline_layer}")

        except Exception as e:
            logger.error(f"Error drawing multiple column outlines: {e}")
            raise

    def _get_outline_layer(self) -> str:
        """
        Get the appropriate layer name for column outlines.

        Uses the DXF writer's layer management system to get the correct
        AIA-standard layer name for column outline elements.

        Returns:
            str: Layer name for column outlines (typically "S-CONC-OTLN")
        """
        try:
            # Use layer management system to get appropriate layer
            if self.dxf_writer:
                outline_layer = self.dxf_writer.get_layer_for_element(
                    "column_outline")
                self.dxf_writer.ensure_layer_exists(outline_layer)
                return outline_layer
            else:
                # Fallback to standard layer name if no DXF writer available
                logger.warning(
                    "No DXF writer available, using fallback layer name")
                return "AIS210__"

        except Exception as e:
            logger.error(f"Error getting outline layer: {e}")
            # Return fallback layer name
            return "AIS210__"
