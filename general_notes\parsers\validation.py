"""
Validation Module
================

Input validation and error reporting for the General Notes Drawing Generator.

This module provides comprehensive validation for:
- File format and structure validation
- Data content validation
- Coordinate and boundary validation
- Error collection and reporting

Author: Drawing Production System
"""

import logging
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from enum import Enum
import pandas as pd
import ezdxf

from .coordinate_parser import CoordinateParser, CoordinateParsingError

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """Validation severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ValidationError(Exception):
    """Raised when validation fails."""
    pass


@dataclass
class ValidationMessage:
    """
    Represents a single validation message.
    
    Attributes:
        level: Severity level of the message
        message: Description of the validation issue
        field: Field or location where issue was found
        value: Value that caused the issue
        suggestion: Suggested fix for the issue
    """
    level: ValidationLevel
    message: str
    field: Optional[str] = None
    value: Optional[Any] = None
    suggestion: Optional[str] = None
    
    def __str__(self) -> str:
        """String representation of the validation message."""
        parts = [f"{self.level.value.upper()}: {self.message}"]
        
        if self.field:
            parts.append(f"Field: {self.field}")
        
        if self.value is not None:
            parts.append(f"Value: {self.value}")
        
        if self.suggestion:
            parts.append(f"Suggestion: {self.suggestion}")
        
        return " | ".join(parts)


@dataclass
class ValidationResult:
    """
    Results of a validation operation.
    
    Attributes:
        is_valid: Whether validation passed
        messages: List of validation messages
        errors: List of error messages
        warnings: List of warning messages
        metadata: Additional validation metadata
    """
    is_valid: bool = True
    messages: List[ValidationMessage] = field(default_factory=list)
    errors: List[ValidationMessage] = field(default_factory=list)
    warnings: List[ValidationMessage] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_message(self, level: ValidationLevel, message: str, 
                   field: Optional[str] = None, value: Optional[Any] = None,
                   suggestion: Optional[str] = None) -> None:
        """
        Add a validation message.
        
        Args:
            level: Severity level
            message: Message description
            field: Field name (optional)
            value: Value that caused issue (optional)
            suggestion: Suggested fix (optional)
        """
        msg = ValidationMessage(level, message, field, value, suggestion)
        self.messages.append(msg)
        
        if level == ValidationLevel.ERROR or level == ValidationLevel.CRITICAL:
            self.errors.append(msg)
            self.is_valid = False
        elif level == ValidationLevel.WARNING:
            self.warnings.append(msg)
    
    def add_error(self, message: str, field: Optional[str] = None, 
                 value: Optional[Any] = None, suggestion: Optional[str] = None) -> None:
        """Add an error message."""
        self.add_message(ValidationLevel.ERROR, message, field, value, suggestion)
    
    def add_warning(self, message: str, field: Optional[str] = None,
                   value: Optional[Any] = None, suggestion: Optional[str] = None) -> None:
        """Add a warning message."""
        self.add_message(ValidationLevel.WARNING, message, field, value, suggestion)
    
    def add_info(self, message: str, field: Optional[str] = None,
                value: Optional[Any] = None, suggestion: Optional[str] = None) -> None:
        """Add an info message."""
        self.add_message(ValidationLevel.INFO, message, field, value, suggestion)
    
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """Check if there are any warnings."""
        return len(self.warnings) > 0
    
    def get_error_summary(self) -> str:
        """Get a summary of all errors."""
        if not self.has_errors():
            return "No errors found."
        
        return f"Found {len(self.errors)} error(s):\n" + "\n".join(str(error) for error in self.errors)
    
    def get_warning_summary(self) -> str:
        """Get a summary of all warnings."""
        if not self.has_warnings():
            return "No warnings found."
        
        return f"Found {len(self.warnings)} warning(s):\n" + "\n".join(str(warning) for warning in self.warnings)
    
    def merge(self, other: 'ValidationResult') -> None:
        """
        Merge another validation result into this one.
        
        Args:
            other: ValidationResult to merge
        """
        self.messages.extend(other.messages)
        self.errors.extend(other.errors)
        self.warnings.extend(other.warnings)
        self.metadata.update(other.metadata)
        
        if not other.is_valid:
            self.is_valid = False


class FileValidator:
    """Validator for file existence and format."""
    
    @staticmethod
    def validate_file_exists(file_path: Union[str, Path]) -> ValidationResult:
        """
        Validate that a file exists.
        
        Args:
            file_path: Path to file to check
            
        Returns:
            ValidationResult
        """
        result = ValidationResult()
        path = Path(file_path)
        
        if not path.exists():
            result.add_error(
                f"File does not exist: {file_path}",
                suggestion="Check the file path and ensure the file exists"
            )
        elif not path.is_file():
            result.add_error(
                f"Path is not a file: {file_path}",
                suggestion="Ensure the path points to a file, not a directory"
            )
        else:
            result.add_info(f"File exists: {file_path}")
        
        return result
    
    @staticmethod
    def validate_excel_file(file_path: Union[str, Path]) -> ValidationResult:
        """
        Validate Excel file format and accessibility.
        
        Args:
            file_path: Path to Excel file
            
        Returns:
            ValidationResult
        """
        result = FileValidator.validate_file_exists(file_path)
        
        if not result.is_valid:
            return result
        
        path = Path(file_path)
        
        # Check file extension
        if path.suffix.lower() not in ['.xlsx', '.xls']:
            result.add_warning(
                f"File does not have Excel extension: {path.suffix}",
                suggestion="Use .xlsx or .xls extension for Excel files"
            )
        
        # Try to read the file
        try:
            pd.read_excel(file_path, nrows=0)  # Just read headers
            result.add_info("Excel file is readable")
        except Exception as e:
            result.add_error(
                f"Cannot read Excel file: {str(e)}",
                suggestion="Ensure the file is a valid Excel format and not corrupted"
            )
        
        return result
    
    @staticmethod
    def validate_dxf_file(file_path: Union[str, Path]) -> ValidationResult:
        """
        Validate DXF file format and accessibility.
        
        Args:
            file_path: Path to DXF file
            
        Returns:
            ValidationResult
        """
        result = FileValidator.validate_file_exists(file_path)
        
        if not result.is_valid:
            return result
        
        path = Path(file_path)
        
        # Check file extension
        if path.suffix.lower() != '.dxf':
            result.add_warning(
                f"File does not have .dxf extension: {path.suffix}",
                suggestion="Use .dxf extension for DXF files"
            )
        
        # Try to read the file
        try:
            doc = ezdxf.readfile(file_path)
            result.add_info(f"DXF file is readable (version: {doc.dxfversion})")
            result.metadata['dxf_version'] = doc.dxfversion
            result.metadata['entity_count'] = len(list(doc.modelspace()))
        except Exception as e:
            result.add_error(
                f"Cannot read DXF file: {str(e)}",
                suggestion="Ensure the file is a valid DXF format and not corrupted"
            )
        
        return result


class DataValidator:
    """Validator for data content and structure."""
    
    def __init__(self):
        """Initialize the data validator."""
        self.coordinate_parser = CoordinateParser()
    
    def validate_excel_structure(self, df: pd.DataFrame, required_columns: List[str]) -> ValidationResult:
        """
        Validate Excel DataFrame structure.
        
        Args:
            df: DataFrame to validate
            required_columns: List of required column names
            
        Returns:
            ValidationResult
        """
        result = ValidationResult()
        
        # Check if DataFrame is empty
        if df.empty:
            result.add_error("Excel file contains no data")
            return result
        
        # Check for required columns
        missing_columns = []
        for col in required_columns:
            if col not in df.columns:
                missing_columns.append(col)
        
        if missing_columns:
            result.add_error(
                f"Missing required columns: {', '.join(missing_columns)}",
                suggestion=f"Ensure Excel file has columns: {', '.join(required_columns)}"
            )
        
        # Check for duplicate column names
        duplicate_columns = df.columns[df.columns.duplicated()].tolist()
        if duplicate_columns:
            result.add_error(
                f"Duplicate column names found: {', '.join(duplicate_columns)}",
                suggestion="Remove duplicate column names from Excel file"
            )
        
        # Check for empty rows
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            result.add_warning(
                f"Found {empty_rows} empty rows",
                suggestion="Remove empty rows from Excel file"
            )
        
        result.metadata['row_count'] = len(df)
        result.metadata['column_count'] = len(df.columns)
        result.metadata['columns'] = df.columns.tolist()
        
        return result
    
    def validate_note_data(self, df: pd.DataFrame) -> ValidationResult:
        """
        Validate note detail data in DataFrame.
        
        Args:
            df: DataFrame with note data
            
        Returns:
            ValidationResult
        """
        result = ValidationResult()
        
        # Required columns for note details
        required_columns = ['Title', 'Point1', 'Point2', 'Point3', 'Point4']
        structure_result = self.validate_excel_structure(df, required_columns)
        result.merge(structure_result)
        
        if not structure_result.is_valid:
            return result
        
        # Validate each row
        for index, row in df.iterrows():
            row_result = self._validate_note_row(row, index)
            result.merge(row_result)
        
        # Check for duplicate titles
        if 'Title' in df.columns:
            duplicate_titles = df[df['Title'].duplicated()]['Title'].tolist()
            if duplicate_titles:
                result.add_error(
                    f"Duplicate note titles found: {', '.join(duplicate_titles)}",
                    suggestion="Ensure all note titles are unique"
                )
        
        return result
    
    def _validate_note_row(self, row: pd.Series, row_index: int) -> ValidationResult:
        """
        Validate a single note detail row.
        
        Args:
            row: DataFrame row to validate
            row_index: Index of the row
            
        Returns:
            ValidationResult
        """
        result = ValidationResult()
        row_prefix = f"Row {row_index + 1}"
        
        # Validate title
        title = row.get('Title', '')
        if pd.isna(title) or not str(title).strip():
            result.add_error(
                f"{row_prefix}: Title is empty",
                field='Title',
                suggestion="Provide a non-empty title for each note"
            )
        
        # Validate coordinate points
        coordinate_columns = ['Point1', 'Point2', 'Point3', 'Point4']
        coordinates = []
        
        for col in coordinate_columns:
            coord_str = row.get(col, '')
            if pd.isna(coord_str) or not str(coord_str).strip():
                result.add_error(
                    f"{row_prefix}: {col} is empty",
                    field=col,
                    suggestion=f"Provide coordinate value for {col}"
                )
                continue
            
            # Validate coordinate format
            try:
                parsed_coord = self.coordinate_parser.parse_coordinate(str(coord_str))
                coordinates.append(parsed_coord)
            except CoordinateParsingError as e:
                result.add_error(
                    f"{row_prefix}: Invalid {col} format: {str(e)}",
                    field=col,
                    value=coord_str,
                    suggestion="Use format '(x, y, z)' for coordinates"
                )
        
        # Validate boundary if all coordinates are valid
        if len(coordinates) == 4:
            boundary_result = self._validate_boundary_coordinates(coordinates, row_prefix)
            result.merge(boundary_result)
        
        return result
    
    def _validate_boundary_coordinates(self, coordinates: List, row_prefix: str) -> ValidationResult:
        """
        Validate that coordinates form a valid boundary.
        
        Args:
            coordinates: List of parsed coordinates
            row_prefix: Prefix for error messages
            
        Returns:
            ValidationResult
        """
        result = ValidationResult()
        
        # Check that coordinates form a rectangle
        points = [coord.to_2d_tuple() for coord in coordinates]
        
        # Calculate dimensions
        width = abs(points[1][0] - points[0][0])  # Point2.x - Point1.x
        height = abs(points[3][1] - points[0][1])  # Point4.y - Point1.y
        
        # Check for reasonable dimensions
        if width <= 0:
            result.add_error(
                f"{row_prefix}: Boundary has zero or negative width",
                suggestion="Ensure Point2.x > Point1.x"
            )
        
        if height <= 0:
            result.add_error(
                f"{row_prefix}: Boundary has zero or negative height",
                suggestion="Ensure Point4.y > Point1.y"
            )
        
        # Check for very small boundaries
        min_size = 1.0
        if width < min_size or height < min_size:
            result.add_warning(
                f"{row_prefix}: Very small boundary ({width:.1f} × {height:.1f})",
                suggestion="Check if boundary dimensions are correct"
            )
        
        # Check for very large boundaries
        max_size = 100000.0
        if width > max_size or height > max_size:
            result.add_warning(
                f"{row_prefix}: Very large boundary ({width:.1f} × {height:.1f})",
                suggestion="Check if boundary dimensions are correct"
            )
        
        return result


# Convenience functions
def validate_excel_structure(file_path: Union[str, Path], 
                           required_columns: List[str]) -> ValidationResult:
    """
    Validate Excel file structure.
    
    Args:
        file_path: Path to Excel file
        required_columns: List of required column names
        
    Returns:
        ValidationResult
    """
    # First validate file
    file_result = FileValidator.validate_excel_file(file_path)
    if not file_result.is_valid:
        return file_result
    
    # Then validate structure
    try:
        df = pd.read_excel(file_path)
        validator = DataValidator()
        structure_result = validator.validate_excel_structure(df, required_columns)
        file_result.merge(structure_result)
        return file_result
    except Exception as e:
        file_result.add_error(f"Error reading Excel file: {str(e)}")
        return file_result


def validate_dxf_file(file_path: Union[str, Path]) -> ValidationResult:
    """
    Validate DXF file.
    
    Args:
        file_path: Path to DXF file
        
    Returns:
        ValidationResult
    """
    return FileValidator.validate_dxf_file(file_path)


def validate_coordinate_string(coord_str: str, strict: bool = False) -> ValidationResult:
    """
    Validate coordinate string format.
    
    Args:
        coord_str: Coordinate string to validate
        strict: Whether to use strict validation
        
    Returns:
        ValidationResult
    """
    result = ValidationResult()
    parser = CoordinateParser(strict_mode=strict)
    
    try:
        parsed = parser.parse_coordinate(coord_str)
        result.add_info(f"Valid coordinate: {parsed.to_tuple()}")
    except CoordinateParsingError as e:
        result.add_error(
            f"Invalid coordinate format: {str(e)}",
            value=coord_str,
            suggestion="Use format '(x, y, z)' for coordinates"
        )
    
    return result
