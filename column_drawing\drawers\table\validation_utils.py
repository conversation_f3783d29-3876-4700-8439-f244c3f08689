"""
Table Validation Utilities
==========================

Validation utilities for table drawing operations in the Drawing-Production application.
This module provides consistent validation patterns and error handling across all table
utility classes to improve robustness and maintainability.

Key Features:
- Input parameter validation for coordinates, dimensions, and configurations
- Consistent error handling patterns with proper logging
- Type checking and range validation
- Configuration validation for table components
- Professional error reporting with detailed context

This module provides standardized validation that can be used across all table
utility classes to ensure consistent error handling and input validation.
"""

import logging
from typing import Any, Optional, Tuple, List, Union
from ...models.column_config import ColumnConfig
from ...models.zone_config import ZoneConfigSet
from ...models.table_config import TableConfig
from ...io.dxf_writer import DXFWriter

logger = logging.getLogger(__name__)


class TableValidationError(Exception):
    """Custom exception for table validation errors."""
    pass


class TableValidationUtils:
    """
    Utility class providing consistent validation methods for table operations.

    This class provides standardized validation methods that can be used across
    all table utility classes to ensure consistent error handling and input validation.
    """

    @staticmethod
    def validate_coordinates(x: float, y: float, context: str = "coordinates") -> None:
        """
        Validate coordinate values.

        Args:
            x: X coordinate value
            y: Y coordinate value
            context: Context description for error messages

        Raises:
            TableValidationError: If coordinates are invalid
        """
        try:
            if not isinstance(x, (int, float)) or not isinstance(y, (int, float)):
                raise TableValidationError(
                    f"Invalid {context}: coordinates must be numeric (got x={type(x)}, y={type(y)})")

            if not (-1000000 <= x <= 1000000) or not (-1000000 <= y <= 1000000):
                raise TableValidationError(
                    f"Invalid {context}: coordinates out of reasonable range (x={x}, y={y})")

        except Exception as e:
            logger.error(f"Error validating {context}: {e}")
            raise

    @staticmethod
    def validate_dimensions(width: float, height: float, context: str = "dimensions") -> None:
        """
        Validate dimension values.

        Args:
            width: Width value
            height: Height value
            context: Context description for error messages

        Raises:
            TableValidationError: If dimensions are invalid
        """
        try:
            if not isinstance(width, (int, float)) or not isinstance(height, (int, float)):
                raise TableValidationError(
                    f"Invalid {context}: dimensions must be numeric (got width={type(width)}, height={type(height)})")

            if width <= 0 or height <= 0:
                raise TableValidationError(
                    f"Invalid {context}: dimensions must be positive (width={width}, height={height})")

            if width > 100000 or height > 100000:
                raise TableValidationError(
                    f"Invalid {context}: dimensions too large (width={width}, height={height})")

        except Exception as e:
            logger.error(f"Error validating {context}: {e}")
            raise

    @staticmethod
    def validate_column_config(column_config: ColumnConfig, context: str = "column configuration") -> None:
        """
        Validate column configuration object.

        Args:
            column_config: Column configuration to validate
            context: Context description for error messages

        Raises:
            TableValidationError: If configuration is invalid
        """
        try:
            if column_config is None:
                raise TableValidationError(
                    f"Invalid {context}: configuration is None")

            if not hasattr(column_config, 'name') or not column_config.name:
                raise TableValidationError(
                    f"Invalid {context}: missing or empty name")

            if not hasattr(column_config, 'floor') or not column_config.floor:
                raise TableValidationError(
                    f"Invalid {context}: missing or empty floor")

        except Exception as e:
            logger.error(f"Error validating {context}: {e}")
            raise

    @staticmethod
    def validate_table_config(table_config: TableConfig, context: str = "table configuration") -> None:
        """
        Validate table configuration object.

        Args:
            table_config: Table configuration to validate
            context: Context description for error messages

        Raises:
            TableValidationError: If configuration is invalid
        """
        try:
            if table_config is None:
                raise TableValidationError(
                    f"Invalid {context}: configuration is None")

        except Exception as e:
            logger.error(f"Error validating {context}: {e}")
            raise

    @staticmethod
    def validate_modelspace(modelspace: Any, context: str = "modelspace") -> None:
        """
        Validate DXF modelspace object.

        Args:
            modelspace: DXF modelspace to validate
            context: Context description for error messages

        Raises:
            TableValidationError: If modelspace is invalid
        """
        try:
            if modelspace is None:
                raise TableValidationError(
                    f"Invalid {context}: modelspace is None")

        except Exception as e:
            logger.error(f"Error validating {context}: {e}")
            raise

    @staticmethod
    def validate_text_content(text: str, context: str = "text content") -> None:
        """
        Validate text content.

        Args:
            text: Text content to validate
            context: Context description for error messages

        Raises:
            TableValidationError: If text is invalid
        """
        try:
            if text is None:
                raise TableValidationError(f"Invalid {context}: text is None")

            if not isinstance(text, str):
                raise TableValidationError(
                    f"Invalid {context}: text must be string (got {type(text)})")

        except Exception as e:
            logger.error(f"Error validating {context}: {e}")
            raise

    @staticmethod
    def validate_list_input(data: List, min_length: int = 0, context: str = "list input") -> None:
        """
        Validate list input.

        Args:
            data: List data to validate
            min_length: Minimum required length
            context: Context description for error messages

        Raises:
            TableValidationError: If list is invalid
        """
        try:
            if data is None:
                raise TableValidationError(f"Invalid {context}: data is None")

            if not isinstance(data, list):
                raise TableValidationError(
                    f"Invalid {context}: data must be list (got {type(data)})")

            if len(data) < min_length:
                raise TableValidationError(
                    f"Invalid {context}: insufficient data (got {len(data)}, need {min_length})")

        except Exception as e:
            logger.error(f"Error validating {context}: {e}")
            raise

    @staticmethod
    def validate_optional_config(config: Optional[Any], config_type: str, context: str = "optional configuration") -> None:
        """
        Validate optional configuration object.

        Args:
            config: Configuration object to validate (can be None)
            config_type: Type description for error messages
            context: Context description for error messages

        Raises:
            TableValidationError: If configuration is invalid (when not None)
        """
        try:
            if config is not None:
                # Only validate if not None - None is acceptable for optional configs
                pass

        except Exception as e:
            logger.error(f"Error validating {context} ({config_type}): {e}")
            raise

    @staticmethod
    def safe_execute(operation_name: str, operation_func, *args, **kwargs) -> Any:
        """
        Safely execute an operation with consistent error handling.

        Args:
            operation_name: Name of the operation for logging
            operation_func: Function to execute
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            Result of the operation

        Raises:
            TableValidationError: If operation fails
        """
        try:
            logger.debug(f"Starting {operation_name}")
            result = operation_func(*args, **kwargs)
            logger.debug(f"Completed {operation_name} successfully")
            return result

        except TableValidationError:
            # Re-raise validation errors as-is
            raise
        except Exception as e:
            logger.error(f"Error in {operation_name}: {e}")
            raise TableValidationError(
                f"Failed to execute {operation_name}: {e}")

    @staticmethod
    def log_validation_success(context: str, details: str = "") -> None:
        """
        Log successful validation (consolidated - only logs failures).

        Args:
            context: Context of the validation
            details: Additional details to log
        """
        # Only log validation successes in debug mode - reduces noise
        pass

    @staticmethod
    def log_validation_warning(context: str, warning: str) -> None:
        """
        Log validation warning.

        Args:
            context: Context of the validation
            warning: Warning message
        """
        logger.warning(f"Validation warning in {context}: {warning}")

    @staticmethod
    def create_validation_context(operation: str, **kwargs) -> str:
        """
        Create a descriptive context string for validation operations.

        Args:
            operation: Name of the operation
            **kwargs: Additional context parameters

        Returns:
            Formatted context string
        """
        context_parts = [operation]
        for key, value in kwargs.items():
            if value is not None:
                context_parts.append(f"{key}={value}")
        return " ".join(context_parts)
