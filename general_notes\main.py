"""
General Notes Drawing Generator - Main Application
=================================================

Main application logic and entry point for the General Notes Drawing Generator.
Provides both CLI and GUI modes for note selection and drawing generation.

This module serves as the primary interface for the application, coordinating
between authentication, user interface, and drawing generation components.

Features:
    - Integrated authentication via auth package
    - GUI and CLI mode selection
    - Professional error handling and logging
    - Integration with drawing_spaces package
    - Workflow orchestration and coordination

Author: Drawing Production System
Version: 1.0.0
"""

import logging
import sys
from typing import Optional, List
from pathlib import Path

# Import authentication components
from auth import (
    create_session_manager,
    check_user_license,
    show_error_message,
    show_info_message,
    requires_login_decorator
)

# Import core application components
from .core.application_core import ApplicationCore
from .models.configuration import GeneralNotesConfig
from .orchestrators.workflow_orchestrator import WorkflowOrchestrator

logger = logging.getLogger(__name__)


class GeneralNotesGenerator:
    """
    Main application class for the General Notes Drawing Generator.
    
    This class provides the primary interface for generating technical drawings
    from predefined note details stored in a DXF database.
    
    Attributes:
        core: Application core instance
        config: Application configuration
        workflow_orchestrator: Main workflow coordinator
        session_manager: User session management
    """
    
    def __init__(self, config: Optional[GeneralNotesConfig] = None):
        """
        Initialize the General Notes Generator.
        
        Args:
            config: Application configuration (uses default if None)
        """
        # Initialize core components
        self.core = ApplicationCore(config)
        self.config = self.core.config
        self.workflow_orchestrator = WorkflowOrchestrator(self.core)
        
        # Initialize session management
        self.session_manager = create_session_manager()
        
        logger.info("General Notes Generator initialized")
    
    @requires_login_decorator
    def generate_drawings_gui(self) -> bool:
        """
        Generate drawings using the GUI interface.
        
        Returns:
            True if generation was successful, False otherwise
        """
        try:
            logger.info("Starting GUI-based drawing generation")
            
            # Launch GUI interface
            from .interfaces.main_window import MainWindow
            
            window = MainWindow(self.workflow_orchestrator)
            result = window.run()
            
            if result:
                show_info_message(
                    "Generation Complete",
                    "General notes drawings have been generated successfully."
                )
                logger.info("Drawing generation completed successfully")
                return True
            else:
                logger.info("Drawing generation cancelled by user")
                return False
                
        except Exception as e:
            error_msg = f"Error during GUI drawing generation: {str(e)}"
            logger.error(error_msg)
            show_error_message("Generation Error", error_msg)
            return False
    
    @requires_login_decorator
    def generate_drawings_cli(
        self,
        database_path: str,
        summary_path: str,
        output_path: str,
        selected_notes: Optional[List[str]] = None
    ) -> bool:
        """
        Generate drawings using the CLI interface.
        
        Args:
            database_path: Path to the DXF database file
            summary_path: Path to the Excel summary file
            output_path: Path for the output DXF file
            selected_notes: List of note titles to include (all if None)
            
        Returns:
            True if generation was successful, False otherwise
        """
        try:
            logger.info("Starting CLI-based drawing generation")
            logger.info(f"Database: {database_path}")
            logger.info(f"Summary: {summary_path}")
            logger.info(f"Output: {output_path}")
            
            # Validate input files
            if not Path(database_path).exists():
                raise FileNotFoundError(f"Database file not found: {database_path}")
            if not Path(summary_path).exists():
                raise FileNotFoundError(f"Summary file not found: {summary_path}")
            
            # Execute workflow
            result = self.workflow_orchestrator.execute_workflow(
                database_path=database_path,
                summary_path=summary_path,
                output_path=output_path,
                selected_notes=selected_notes
            )
            
            if result:
                logger.info("Drawing generation completed successfully")
                print(f"Generated drawings saved to: {output_path}")
                return True
            else:
                logger.error("Drawing generation failed")
                print("Drawing generation failed. Check logs for details.")
                return False
                
        except Exception as e:
            error_msg = f"Error during CLI drawing generation: {str(e)}"
            logger.error(error_msg)
            print(f"Error: {error_msg}")
            return False
    
    def get_available_notes(self, database_path: str, summary_path: str) -> List[str]:
        """
        Get list of available note titles from the summary file.
        
        Args:
            database_path: Path to the DXF database file
            summary_path: Path to the Excel summary file
            
        Returns:
            List of available note titles
        """
        try:
            return self.workflow_orchestrator.get_available_notes(
                database_path, summary_path
            )
        except Exception as e:
            logger.error(f"Error getting available notes: {str(e)}")
            return []


def main(mode: str = "gui") -> None:
    """
    Main entry point for the General Notes Drawing Generator.
    
    Args:
        mode: Application mode ("gui" or "cli")
    """
    try:
        # Check user license
        # Note: In a real implementation, you would get the username from login
        username = "current_user"  # This would come from authentication
        if not check_user_license(username):
            logger.error("User license validation failed")
            show_error_message(
                "License Error",
                "Your license could not be validated. Please contact support."
            )
            return
        
        # Initialize application
        generator = GeneralNotesGenerator()
        
        if mode.lower() == "gui":
            # Run GUI mode
            generator.generate_drawings_gui()
        elif mode.lower() == "cli":
            # Run CLI mode with default paths (for demonstration)
            # In a real CLI implementation, these would come from command line arguments
            database_path = generator.config.default_database_path
            summary_path = generator.config.default_summary_path
            output_path = "output_general_notes.dxf"
            
            generator.generate_drawings_cli(
                database_path=database_path,
                summary_path=summary_path,
                output_path=output_path
            )
        else:
            raise ValueError(f"Unknown mode: {mode}. Use 'gui' or 'cli'.")
            
    except Exception as e:
        logger.error(f"Fatal error in main: {str(e)}")
        if mode.lower() == "gui":
            show_error_message("Fatal Error", str(e))
        else:
            print(f"Fatal Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
