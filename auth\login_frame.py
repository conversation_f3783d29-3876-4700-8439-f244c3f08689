"""
Login and Authentication Module
=============================

This module provides backward compatibility for existing code that imports from login_frame.py.
All authentication functionality has been moved to the organized auth package structure.

This module now serves as a compatibility layer that re-exports the main authentication components.

Author: Drawing Production System  
Date: 2025

DEPRECATED: This module is deprecated. Please import directly from the auth package instead.
"""

import warnings
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Issue deprecation warning
warnings.warn(
    "auth.login_frame is deprecated. Please import directly from the auth package instead.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export all authentication components from the organized auth package
from auth import (
    # Constants
    MAX_LOGIN_ATTEMPTS,
    LOGIN_TIMEOUT_MINUTES,
    SESSION_TOKEN_BYTES,
    DEVELOPER_MODE,
    FONT_TITLE,
    FONT_SUBTITLE,
    FONT_NORMAL,
    FONT_SMALL,
    BUTTON_WIDTH_LARGE,
    BUTTON_HEIGHT_LARGE,
    BUTTON_WIDTH_MEDIUM,
    BUTTON_HEIGHT_MEDIUM,
    ENTRY_WIDTH,
    APP_VERSION,
    
    # Email service functions
    send_password_email,
    send_email_log,
    send_email_log_sync,
    send_drawing_completion_log,
    send_drawing_completion_log_sync,
    log_security_event,
    
    # Password service functions
    generate_password_key,
    generate_password_with_salt,
    validate_password,
    clear_password_data,
    
    # License service functions
    check_user_license,
    is_developer_mode_enabled,
    
    # Session management
    SessionManager,
    create_session_manager,
    
    # UI components
    create_login_screen,
    create_timer_display,
    create_menu_bar,
    show_error_message,
    show_info_message,
    show_warning_message,
    validate_username_input,
    validate_password_input,
    requires_login_decorator
)

# Backward compatibility aliases for commonly used functions
requires_login = requires_login_decorator
check_license = check_user_license


# Legacy function stubs for backward compatibility
def show_login_frame(app_instance):
    """
    DEPRECATED: Use auth.ui_components.create_login_screen instead.
    """
    warnings.warn(
        "show_login_frame is deprecated. Use auth.ui_components.create_login_screen instead.",
        DeprecationWarning,
        stacklevel=2
    )
    # For backward compatibility, we'll implement a basic version
    pass


def send_password(app_instance):
    """
    DEPRECATED: This function should be implemented in the main application class.
    """
    warnings.warn(
        "send_password from login_frame is deprecated. Implement in your main application class.",
        DeprecationWarning,
        stacklevel=2
    )
    pass


def login(app_instance):
    """
    DEPRECATED: This function should be implemented in the main application class.
    """
    warnings.warn(
        "login from login_frame is deprecated. Implement in your main application class.",
        DeprecationWarning,
        stacklevel=2
    )
    pass


def logout(app_instance):
    """
    DEPRECATED: This function should be implemented in the main application class.
    """
    warnings.warn(
        "logout from login_frame is deprecated. Implement in your main application class.",
        DeprecationWarning,
        stacklevel=2
    )
    pass


def update_timer(app_instance):
    """
    DEPRECATED: Use auth.session_manager.SessionManager instead.
    """
    warnings.warn(
        "update_timer from login_frame is deprecated. Use auth.session_manager.SessionManager instead.",
        DeprecationWarning,
        stacklevel=2
    )
    pass


def force_security_exit(app_instance):
    """
    DEPRECATED: This function should be implemented in the main application class.
    """
    warnings.warn(
        "force_security_exit from login_frame is deprecated. Implement in your main application class.",
        DeprecationWarning,
        stacklevel=2
    )
    pass


# Log the compatibility layer usage
logging.info("Authentication compatibility layer loaded. Consider migrating to direct auth package imports.")