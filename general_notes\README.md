# General Notes Drawing Generator

A professional Python package for generating technical drawings by selecting and copying predefined note details from a template DXF database.

## Overview

The General Notes Drawing Generator automates the creation of technical drawings by:
- Reading note metadata from Excel summary files
- Loading predefined note details from DXF database files
- Providing a user-friendly interface for note selection
- Intelligently arranging selected notes in drawing spaces
- Generating professional DXF output with preserved entity properties

## Features

### Core Functionality
- **Excel-based Metadata**: Note titles and boundary coordinates defined in Excel
- **DXF Database Integration**: Load and parse complex DXF database files
- **Boundary-based Selection**: Precise entity selection using coordinate boundaries
- **Intelligent Layout**: Automatic arrangement in optimized drawing spaces
- **Property Preservation**: Maintain all original entity properties and layers

### User Interface
- **GUI Mode**: User-friendly checklist interface with search and filtering
- **CLI Mode**: Command-line interface for automated workflows
- **Progress Tracking**: Real-time progress indication during generation
- **Error Handling**: Comprehensive error reporting and validation

### Integration
- **Authentication**: Integrated with auth package for user management
- **Drawing Spaces**: Uses drawing_spaces package for layout management
- **Professional Output**: AutoCAD-compatible DXF format (R2018+)

## Installation

The package is part of the Drawing-Production project and requires:

```bash
pip install ezdxf pandas openpyxl tkinter
```

## Quick Start

### GUI Mode (Recommended)

```python
from general_notes import GeneralNotesGenerator

# Initialize generator
generator = GeneralNotesGenerator()

# Run GUI interface
generator.generate_drawings_gui()
```

### CLI Mode

```python
from general_notes import GeneralNotesGenerator

# Initialize generator
generator = GeneralNotesGenerator()

# Generate with specific files
generator.generate_drawings_cli(
    database_path="path/to/database.dxf",
    summary_path="path/to/summary.xlsx", 
    output_path="output.dxf",
    selected_notes=["NOTE_1", "NOTE_2"]  # Optional
)
```

### Standalone Execution

```bash
# GUI mode
python -m general_notes

# CLI mode  
python -m general_notes --cli

# With debug logging
python -m general_notes --debug
```

## File Formats

### Excel Summary File

Required columns:
- **Title**: Unique note identifier
- **Point1**: Bottom-left boundary coordinate "(x, y, z)"
- **Point2**: Bottom-right boundary coordinate "(x, y, z)"
- **Point3**: Top-right boundary coordinate "(x, y, z)"
- **Point4**: Top-left boundary coordinate "(x, y, z)"

Example:
```
Title                    | Point1              | Point2              | Point3              | Point4
WELDED JOINT FOR H-PILE | (0, -57400, 0)     | (33800, -57400, 0) | (33800, -71500, 0) | (0, -71500, 0)
```

### DXF Database File

- **Format**: AutoCAD-compatible DXF (R2018+)
- **Coordinate System**: AutoCAD standard with origin at (0,0)
- **Entity Types**: Lines, text, circles, polylines, blocks, dimensions
- **Layer Structure**: Preserves original layer names and properties

### Output DXF File

- **Drawing Spaces**: 67,600 × 56,400 units (4 modules per space)
- **Module Size**: 16,900 × 14,100 units each
- **Layout**: Automatic grid arrangement with optimal spacing
- **Entity Preservation**: All original properties maintained

## Architecture

The package follows established patterns from the Drawing-Production project:

```
general_notes/
├── models/          # Data structures and configuration
├── parsers/         # File parsing and validation  
├── processors/      # Data processing and entity selection
├── generators/      # Output generation and layout
├── orchestrators/   # High-level workflow coordination
├── managers/        # System management and coordination
├── interfaces/      # GUI components and user interaction
├── utils/          # Utility functions and helpers
└── core/           # Application core and business logic
```

## Configuration

### Default Paths

The application uses configurable default paths:

```python
from general_notes.models.configuration import GeneralNotesConfig

config = GeneralNotesConfig(
    default_database_path="path/to/database.dxf",
    default_summary_path="path/to/summary.xlsx",
    drawing_space_width=67600,
    drawing_space_height=56400,
    module_width=16900,
    module_height=14100
)
```

### Coordinate System

- **Origin**: Bottom-left of drawing space (0,0)
- **Units**: Drawing units (typically millimeters)
- **Module Grid**: 2×2 modules per drawing space
- **Translation**: Automatic positioning with module boundaries

## Error Handling

The package provides comprehensive error handling:

- **File Validation**: Checks for file existence and format
- **Coordinate Validation**: Validates boundary coordinate format
- **Entity Validation**: Ensures valid entity selection
- **Layout Validation**: Verifies drawing space arrangement

## Logging

Configurable logging levels:

```python
import logging
logging.basicConfig(level=logging.INFO)

# Available levels: DEBUG, INFO, WARNING, ERROR
```

## Dependencies

### Required Packages
- `ezdxf`: DXF file manipulation
- `pandas`: Excel file processing
- `openpyxl`: Excel file reading
- `tkinter`: GUI interface (included with Python)

### Internal Dependencies
- `auth`: User authentication and session management
- `drawing_spaces`: Drawing space layout and management

## License

Part of the Drawing-Production System for professional structural engineering applications.

## Support

For technical support and documentation, refer to the main Drawing-Production project documentation.
