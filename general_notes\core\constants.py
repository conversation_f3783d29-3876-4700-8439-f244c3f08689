"""
Application Constants
====================

Constants and configuration values for the General Notes Drawing Generator.

This module defines application-wide constants including:
- Application metadata
- File format specifications
- Default configuration values
- Layout and drawing parameters

Author: Drawing Production System
"""

from typing import Dict, Any, List, Tuple

# Application metadata
APPLICATION_NAME = "General Notes Drawing Generator"
APPLICATION_VERSION = "1.0.0"
APPLICATION_AUTHOR = "Drawing Production System"

# File format specifications
DEFAULT_DXF_VERSION = "R2018"
SUPPORTED_DXF_VERSIONS = ["R12", "R2000", "R2004", "R2007", "R2010", "R2013", "R2018"]

SUPPORTED_FILE_FORMATS = {
    'excel': ['.xlsx', '.xls'],
    'dxf': ['.dxf'],
    'output': ['.dxf']
}

# Excel file specifications
EXCEL_REQUIRED_COLUMNS = ['Title', 'Point1', 'Point2', 'Point3', 'Point4']
EXCEL_OPTIONAL_COLUMNS = ['Description', 'Category', 'Tags']
DEFAULT_EXCEL_SHEET_NAME = 'Detail'

# Coordinate and geometry constants
COORDINATE_PRECISION = 6
SPATIAL_TOLERANCE = 1e-6
MAX_COORDINATE_VALUE = 1e8  # 100 million units
MIN_BOUNDARY_SIZE = 1.0     # Minimum boundary dimension
MAX_BOUNDARY_SIZE = 100000.0  # Maximum boundary dimension

# Entity processing constants
MAX_ENTITIES_PER_NOTE = 10000  # Maximum entities per note detail
ENTITY_BATCH_SIZE = 1000       # Batch size for entity processing

# Drawing space and layout constants
DEFAULT_DRAWING_SPACE_WIDTH = 67600.0   # Drawing space width in units
DEFAULT_DRAWING_SPACE_HEIGHT = 56400.0  # Drawing space height in units
DEFAULT_MODULE_WIDTH = 16900.0          # Module width in units
DEFAULT_MODULE_HEIGHT = 14100.0         # Module height in units
DEFAULT_MODULES_PER_ROW = 2             # Modules per row in drawing space
DEFAULT_MODULES_PER_COLUMN = 2          # Modules per column in drawing space
DEFAULT_SPACING_BETWEEN_MODULES = 0.0   # Spacing between modules
DEFAULT_SPACING_BETWEEN_SPACES = 5000.0 # Spacing between drawing spaces

# Layout optimization settings
DEFAULT_LAYOUT_SETTINGS = {
    'optimization_enabled': True,
    'prefer_aspect_ratio_match': True,
    'minimize_empty_space': True,
    'sort_by_size': False,
    'sort_by_title': True,
    'group_by_category': False
}

# UI configuration constants
DEFAULT_UI_SETTINGS = {
    'window_width': 800,
    'window_height': 600,
    'checklist_height': 400,
    'search_enabled': True,
    'category_filter_enabled': True,
    'progress_bar_enabled': True,
    'auto_save_selections': True,
    'remember_window_position': True,
    'theme': 'default'
}

# Validation settings
VALIDATION_SETTINGS = {
    'strict_coordinate_parsing': False,
    'validate_boundary_rectangles': True,
    'check_entity_limits': True,
    'warn_on_large_boundaries': True,
    'warn_on_empty_notes': True
}

# Logging configuration
LOG_SETTINGS = {
    'default_level': 'INFO',
    'file_logging_enabled': True,
    'console_logging_enabled': True,
    'log_file_name': 'general_notes.log',
    'max_log_file_size': 10 * 1024 * 1024,  # 10 MB
    'backup_count': 5
}

# Performance settings
PERFORMANCE_SETTINGS = {
    'enable_caching': True,
    'cache_size_limit': 100,  # Number of cached items
    'parallel_processing': False,  # Enable for large datasets
    'max_worker_threads': 4,
    'memory_limit_mb': 512
}

# Error handling settings
ERROR_HANDLING = {
    'continue_on_parse_errors': True,
    'max_parse_errors': 10,
    'show_detailed_errors': True,
    'auto_retry_failed_operations': False,
    'retry_attempts': 3
}

# File path defaults (can be overridden by configuration)
DEFAULT_FILE_PATHS = {
    'database_path': "C:\\Users\\<USER>\\OneDrive - Asia Infrastructure Solutions Limited\\RPA Documentation\\Drafting Agent\\General Notes\\General Notes Database.dxf",
    'summary_path': "C:\\Users\\<USER>\\OneDrive - Asia Infrastructure Solutions Limited\\RPA Documentation\\Drafting Agent\\General Notes\\General Notes Summary.xlsx",
    'output_directory': "output",
    'backup_directory': "backup",
    'temp_directory': "temp"
}

# Entity type mappings for DXF processing
SUPPORTED_ENTITY_TYPES = {
    'LINE': 'line',
    'LWPOLYLINE': 'polyline',
    'POLYLINE': 'polyline',
    'CIRCLE': 'circle',
    'ARC': 'arc',
    'TEXT': 'text',
    'MTEXT': 'text',
    'INSERT': 'block',
    'DIMENSION': 'dimension',
    'LEADER': 'leader',
    'HATCH': 'hatch',
    'SOLID': 'solid',
    'POINT': 'point'
}

# Layer management settings
LAYER_SETTINGS = {
    'preserve_original_layers': True,
    'create_note_specific_layers': False,
    'default_layer_name': '0',
    'layer_prefix': 'GENERAL_NOTES_',
    'color_preservation': True,
    'linetype_preservation': True,
    'lineweight_preservation': True
}

# Output generation settings
OUTPUT_SETTINGS = {
    'preserve_entity_properties': True,
    'preserve_block_definitions': True,
    'preserve_text_styles': True,
    'preserve_dimension_styles': True,
    'generate_summary_report': True,
    'include_metadata': True,
    'compress_output': False
}

# Selection and filtering constants
SELECTION_SETTINGS = {
    'max_history_size': 50,
    'auto_save_snapshots': True,
    'enable_undo_redo': True,
    'default_selection_mode': 'multiple',
    'search_case_sensitive': False,
    'search_exact_match': False
}

# Progress reporting settings
PROGRESS_SETTINGS = {
    'show_progress_dialog': True,
    'update_interval_ms': 100,
    'show_detailed_progress': True,
    'show_time_estimates': True,
    'show_cancel_button': True
}

# Memory and resource management
RESOURCE_SETTINGS = {
    'cleanup_temp_files': True,
    'max_temp_file_age_hours': 24,
    'garbage_collect_frequency': 100,  # Operations between GC calls
    'monitor_memory_usage': True
}

# Development and debugging settings
DEBUG_SETTINGS = {
    'enable_debug_mode': False,
    'verbose_logging': False,
    'save_intermediate_files': False,
    'profile_performance': False,
    'validate_all_operations': False
}

# Version compatibility settings
COMPATIBILITY_SETTINGS = {
    'min_python_version': (3, 8),
    'min_ezdxf_version': "1.0.0",
    'min_pandas_version': "1.5.0",
    'check_dependencies': True
}

# Export all constants for easy access
__all__ = [
    'APPLICATION_NAME',
    'APPLICATION_VERSION', 
    'APPLICATION_AUTHOR',
    'DEFAULT_DXF_VERSION',
    'SUPPORTED_DXF_VERSIONS',
    'SUPPORTED_FILE_FORMATS',
    'EXCEL_REQUIRED_COLUMNS',
    'EXCEL_OPTIONAL_COLUMNS',
    'DEFAULT_EXCEL_SHEET_NAME',
    'COORDINATE_PRECISION',
    'SPATIAL_TOLERANCE',
    'MAX_COORDINATE_VALUE',
    'MIN_BOUNDARY_SIZE',
    'MAX_BOUNDARY_SIZE',
    'MAX_ENTITIES_PER_NOTE',
    'ENTITY_BATCH_SIZE',
    'DEFAULT_DRAWING_SPACE_WIDTH',
    'DEFAULT_DRAWING_SPACE_HEIGHT',
    'DEFAULT_MODULE_WIDTH',
    'DEFAULT_MODULE_HEIGHT',
    'DEFAULT_MODULES_PER_ROW',
    'DEFAULT_MODULES_PER_COLUMN',
    'DEFAULT_SPACING_BETWEEN_MODULES',
    'DEFAULT_SPACING_BETWEEN_SPACES',
    'DEFAULT_LAYOUT_SETTINGS',
    'DEFAULT_UI_SETTINGS',
    'VALIDATION_SETTINGS',
    'LOG_SETTINGS',
    'PERFORMANCE_SETTINGS',
    'ERROR_HANDLING',
    'DEFAULT_FILE_PATHS',
    'SUPPORTED_ENTITY_TYPES',
    'LAYER_SETTINGS',
    'OUTPUT_SETTINGS',
    'SELECTION_SETTINGS',
    'PROGRESS_SETTINGS',
    'RESOURCE_SETTINGS',
    'DEBUG_SETTINGS',
    'COMPATIBILITY_SETTINGS'
]
