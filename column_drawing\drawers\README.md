# Drawers Package

Specialized drawing components for different aspects of column drawings in the Drawing-Production application.

## 📋 Overview

The drawers package contains specialized drawing classes that handle different aspects of technical drawing generation. Each drawer is responsible for a specific type of drawing element, following the single responsibility principle for maintainable and extensible code.

## 🏗️ Package Components

### TableDrawer (`table_drawer.py`)

**Purpose**: Handles drawing of ASD table structures and content placement with professional layer management following AIA standards.

**Key Features**:

- Intelligent line management to prevent duplicate/overlapping lines in horizontal table layouts
- Proper sequencing of tables by column mark and floor level
- Horizontal grouping of tables with the same column mark
- Zone detail drawing integration with proper cell bounds
- Professional layer management following AIA standards

**Main Methods**:

```python
draw_table_structure(origin_x: float, origin_y: float, table_index: int = 0, is_first_in_group: bool = True) -> Tuple[float, float, float, float]
add_table_content(column_config: ColumnConfig, table_x: float, table_y: float, actual_rebar_data: RebarLayerData = None, zone_config_set: ZoneConfigSet = None)
```

**Integration Points**:

- Uses SectionDrawer for zone detail drawings within table cells
- Coordinates with ElevationDrawer for elevation diagrams
- Integrates with LayerManager for consistent layer usage

### SectionDrawer (`section_drawer.py`)

**Purpose**: Handles drawing of reinforcement bars, links, and stirrups for column sections with professional layer management following AIA standards.

**Key Features**:

- Drawing rebar patterns and individual bars on appropriate layers
- Drawing links and stirrups (BS8666 compliant) with proper layer assignment
- Column outline drawing following AIA standards
- Zone detail drawings with automatic scaling and professional annotations
- Arrow drawing for link intersection indicators

**Main Methods**:

```python
draw_zone_detail(zone_id: str, zone_config: ZoneConfig, column_config: ColumnConfig, cell_bounds: Tuple[float, float, float, float], scale_factor: float = None) -> bool
draw_section_dimensions(x: float, y: float, width: float, height: float, actual_width: float, actual_height: float) -> bool
```

**Modular Architecture**:

- CoreDrawingMixin: Basic drawing operations (outlines, rebar layers, links)
- ZoneDetailMixin: Zone-specific operations (zone details, reinforcement, scaling)
- ArrowDrawingMixin: Arrow drawing operations (link intersection arrows, annotations)

### DimensionDrawer (`dimension_drawer.py`)

**Purpose**: Handles dimension annotations for ASD table column drawings with AIA layer management.

**Key Features**:

- Professional dimension annotations for zone detail drawings within ZONE\_\*\_DETAIL cells
- Column section drawings with proper scaling
- AutoCAD-compatible dimension styles on appropriate AIA layers
- Professional engineering dimension lines with stroke end marks (user preference)
- Text styling and formatting optimized for technical drawings

**Main Methods**:

```python
draw_section_dimensions(x: float, y: float, width: float, height: float, B: float, D: float) -> bool
setup_dimension_style() -> str
setup_text_styles() -> None
```

**Standards Compliance**:

- Large, legible dimension text for engineering drawings
- Stroke end marks instead of arrowheads (per user preference)
- Proper layer assignment following AIA standards
- Backward compatible with existing drawing systems

### ElevationDrawer (`elevation_drawer.py`)

**Purpose**: Handles drawing of rebar elevation diagrams in the rightmost column of the drawing table.

**Key Features**:

- Elevation diagrams showing floor levels, beam soffits, and triangular markers
- Modular component architecture with unified layer management system
- Continuous polyline rebar drawing (replacing fragmented segments)
- Consolidated duplicate functions and streamlined coordinate calculations
- BS8666 compliance maintained

**Main Methods**:

```python
draw_elevation_diagrams(table_x: float, table_y: float, column_config: ColumnConfig, zone_config_set: Optional[ZoneConfigSet] = None) -> None
```

**Modular Mixins**:

- RebarDrawingMixin: Rebar line drawing and positioning
- FloorLevelDrawingMixin: Floor level indicators and labels
- ZoneCalculationMixin: Zone-specific calculations and data extraction
- DimensionDrawingMixin: Lap length dimensioning
- TextRenderingMixin: Text labels and annotations

## 🎯 Specialized Subdirectories

### Rebar Components (`rebar/`)

**Purpose**: Rebar-specific drawing components for BS8666-compliant reinforcement elements.

**Key Components**:

- `BS8666ShapeFactory`: Factory for creating standard reinforcement shapes
- `LinkDrawer`: Specialized drawer for links and stirrups

### Section Components (`section/`)

**Purpose**: Section drawing mixins and utilities for modular section drawing functionality.

**Key Components**:

- Core drawing mixins for basic operations
- Zone detail mixins for zone-specific functionality
- Arrow drawing mixins for intersection indicators
- Utility classes for coordinate processing and geometry

### Table Components (`table/`)

**Purpose**: Table drawing utilities and managers for modular table functionality.

**Key Components**:

- Table text management utilities
- Position calculation utilities
- Layout validation and filtering operations
- Cell content management for all table cells
- Table structure drawing and line management

### Elevation Components (`elevation/`)

**Purpose**: Elevation drawing components for vertical representations.

**Key Components**:

- Coordinate calculation utilities
- Rebar positioning calculators
- Floor level drawing utilities
- Dimension drawing components

## 🔧 Dependencies

### Internal Dependencies

- `models.column_config.ColumnConfig`: Column specifications
- `models.drawing_config.DrawingConfig`: Drawing configuration
- `models.zone_config.ZoneConfig, ZoneConfigSet`: Zone configurations
- `models.rebar_layer_data.RebarLayerData`: Calculated rebar data
- `calculators.rebar_calculator.RebarCalculator`: Rebar calculations
- `calculators.geometry_calculator.GeometryCalculator`: Geometric calculations
- `io.dxf_writer.DXFWriter`: DXF output operations

### External Dependencies

- `ezdxf`: DXF entity creation and manipulation
- `logging`: Error reporting and debugging

## 🚀 Usage Examples

### Basic Table Drawing

```python
from column_drawing.drawers.table_drawer import TableDrawer
from column_drawing.models.column_config import ColumnConfig
from column_drawing.models.drawing_config import DrawingConfig
from column_drawing.io.dxf_writer import DXFWriter

# Initialize components
config = DrawingConfig()
dxf_writer = DXFWriter(config=config)
table_drawer = TableDrawer(dxf_writer.get_modelspace(), config, dxf_writer)

# Draw table structure
table_bounds = table_drawer.draw_table_structure(
    origin_x=0, origin_y=0, table_index=0, is_first_in_group=True
)

# Add table content
column_config = ColumnConfig(
    floor="1F", name="C1", B=600, D=700, cover=50,
    dia1=40, num_x1=3, num_y1=5
)

table_drawer.add_table_content(
    column_config=column_config,
    table_x=0, table_y=0
)
```

### Zone Detail Drawing

```python
from column_drawing.drawers.section_drawer import SectionDrawer
from column_drawing.models.zone_config import ZoneConfig

# Initialize section drawer
section_drawer = SectionDrawer(
    doc=dxf_writer.get_document(),
    modelspace=dxf_writer.get_modelspace(),
    config=config,
    rebar_calculator=rebar_calc,
    dxf_writer=dxf_writer
)

# Create zone configuration
zone_config = ZoneConfig(
    zone_id="A",
    outer_link_diameter=12,
    inner_link_diameter=10,
    link_spacing=200,
    link_legs_x=2,
    link_legs_y=4
)

# Draw zone detail
cell_bounds = (100, 100, 200, 250)  # x, y, width, height
success = section_drawer.draw_zone_detail(
    zone_id="A",
    zone_config=zone_config,
    column_config=column_config,
    cell_bounds=cell_bounds,
    scale_factor=0.3
)
```

### Professional Custom Dimensions

The dimension system now uses custom implementations with basic LINE and TEXT entities
instead of native DXF dimension entities. This ensures proper scaling behavior when
DXF files are scaled, as all elements scale proportionally.

```python
from column_drawing.drawers.dimension_drawer import DimensionDrawer

# Initialize dimension drawer
dimension_drawer = DimensionDrawer(
    doc=dxf_writer.get_document(),
    modelspace=dxf_writer.get_modelspace(),
    config=config,
    dxf_writer=dxf_writer
)

# Draw section dimensions (now uses custom implementation)
success = dimension_drawer.draw_section_dimensions(
    x=0, y=0, width=180, height=210,  # Scaled dimensions
    B=600, D=700  # Actual dimensions in mm
)

# Draw linear dimensions (custom implementation)
success = dimension_drawer.draw_linear_dimension(
    p1=(0, 0), p2=(500, 0),  # Measurement points
    base=(250, 100),         # Dimension line position
    text_override="500mm",   # Custom text
    angle=0                  # Horizontal dimension
)

# Draw angular dimensions (custom implementation)
success = dimension_drawer.draw_angular_dimension(
    center=(0, 0),           # Center point
    p1=(100, 0),             # First angle point
    p2=(70.7, 70.7),         # Second angle point (45°)
    radius=100               # Arc radius
)

# Draw radial dimensions (custom implementation)
success = dimension_drawer.draw_radial_dimension(
    center=(0, 0),           # Center point
    radius_point=(75, 0)     # Point on radius
)
```

### Elevation Diagrams

```python
from column_drawing.drawers.elevation_drawer import ElevationDrawer

# Initialize elevation drawer
elevation_drawer = ElevationDrawer(
    modelspace=dxf_writer.get_modelspace(),
    config=config,
    dxf_writer=dxf_writer,
    doc=dxf_writer.get_document()
)

# Draw elevation diagrams
elevation_drawer.draw_elevation_diagrams(
    table_x=0, table_y=0,
    column_config=column_config,
    zone_config_set=zone_config_set
)
```

## 📊 Standards Compliance

### AIA Layer Management

All drawing components follow AIA standards for layer organization:

- **S-CONC-RBAR**: Reinforcement bars and rebar-related elements
- **S-CONC-STIR**: Stirrups, links, and connection elements
- **S-CONC-DIMS**: Dimensions, annotations, and measurement elements
- **S-TABL-BORD**: Table borders, grid lines, and structural elements

### BS8666 Compliance

Reinforcement drawing follows BS8666 standards:

- **Shape Code 52**: Closed rectangular links with proper representation
- **Shape Code 25A**: Intermediate connections and tie elements
- **Standard Symbols**: Proper reinforcement symbols and notation
- **Professional Representation**: Accurate link drawing with overlaps and bends

### AutoCAD Compatibility

All drawing output is optimized for AutoCAD:

- **R2018 Format**: Compatible with AutoCAD 2018 and later versions
- **Professional Dimensions**: Stroke end marks and proper text formatting
- **Text Styles**: Engineering-grade fonts and sizing
- **Layer Properties**: Standardized colors, line weights, and line types

## 🎯 Integration Points

### Component Coordination

1. **TableDrawer ↔ SectionDrawer**: Table drawer calls section drawer for zone detail drawings
2. **SectionDrawer ↔ DimensionDrawer**: Section drawer uses dimension drawer for annotations
3. **TableDrawer ↔ ElevationDrawer**: Table drawer coordinates elevation diagram placement
4. **All Drawers ↔ LayerManager**: Consistent layer usage across all drawing components

### Data Flow

1. **Input**: Column configurations and zone data from CSV parsing
2. **Calculations**: Rebar positions and geometric transformations from calculators
3. **Drawing**: Specialized drawing operations by individual drawer components
4. **Output**: Coordinated DXF output through DXFWriter

### Error Handling

- **Graceful Degradation**: Individual drawing failures don't stop overall process
- **Detailed Logging**: Component-specific error messages for debugging
- **Validation**: Input validation before drawing operations
- **Recovery**: Fallback options for critical drawing operations

## 🔍 Architecture Benefits

### Modular Design

- **Single Responsibility**: Each drawer handles one specific aspect of drawing
- **Loose Coupling**: Minimal dependencies between drawing components
- **High Cohesion**: Related functionality grouped within individual drawers
- **Extensibility**: New drawing types can be added without affecting existing code

### Maintainability

- **Clear Interfaces**: Well-defined methods and parameters for each drawer
- **Consistent Patterns**: Similar structure and naming across all drawers
- **Comprehensive Documentation**: Detailed docstrings and type hints
- **Error Handling**: Consistent error handling patterns across components

### Performance

- **Efficient Drawing**: Optimized drawing operations for large datasets
- **Memory Management**: Proper cleanup and resource management
- **Lazy Loading**: Drawing operations performed only when needed
- **Batch Processing**: Efficient handling of multiple drawing elements

## 🔧 Development Guidelines

### Adding New Drawers

1. **Follow Naming Convention**: Use descriptive names ending with "Drawer"
2. **Implement Standard Interface**: Include initialization, main drawing methods, and cleanup
3. **Use LayerManager**: Ensure consistent layer usage through LayerManager
4. **Add Error Handling**: Include appropriate validation and error recovery
5. **Document Thoroughly**: Add comprehensive docstrings and usage examples
6. **Include Tests**: Add unit tests for new drawing functionality

### Extending Existing Drawers

1. **Maintain Backward Compatibility**: Don't break existing interfaces
2. **Follow Existing Patterns**: Use similar structure and naming conventions
3. **Add Configuration Options**: Use DrawingConfig for new parameters
4. **Update Documentation**: Keep README and docstrings current
5. **Test Thoroughly**: Ensure new functionality doesn't break existing features
