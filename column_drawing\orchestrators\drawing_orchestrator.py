"""
Drawing Orchestrator Module
============================

Handles the orchestration of drawing generation processes, including
column processing, drawing coordination, and section drawing operations.
"""

import logging
from typing import List, Tuple, Dict, Any, TYPE_CHECKING

from ..models.column_config import ColumnConfig
from ..models.zone_config import ZoneConfigSet
from ..models.drawing_config import DrawingConfig
from ..models.rebar_layer_data import RebarLayerData
from ..drawers.table_drawer import TableDrawer
from ..drawers.section_drawer import SectionDrawer
from ..calculators.rebar_calculator import RebarCalculator
from ..calculators.geometry_calculator import Geometry<PERSON>alculator
from ..processors.data_processor import DataProcessor
from ..processors.column_sorter import ColumnSorter
from ..coordinators.link_mark_coordinator import LinkMarkCoordinator
from .section_orchestrator import SectionOrchestrator
from .drawing_space_organizer import DrawingSpaceOrganizer

if TYPE_CHECKING:
    from ..io.dxf_writer import DXFWriter

logger = logging.getLogger(__name__)


class DrawingOrchestrator:
    """
    Orchestrates the drawing generation process.

    This class handles the coordination of drawing operations,
    including column processing, section drawing, and table generation.
    """

    def __init__(
        self,
        config: DrawingConfig,
        table_drawer: TableDrawer,
        rebar_drawer: SectionDrawer,
        rebar_calculator: RebarCalculator,
        geometry_calculator: GeometryCalculator,
        data_processor: DataProcessor,
        column_sorter: ColumnSorter,
        dxf_writer: 'DXFWriter' = None  # Optional DXF writer for drawing space integration
    ):
        """
        Initialize the drawing orchestrator.

        Args:
            config: Drawing configuration
            table_drawer: Table drawing component
            rebar_drawer: Section drawing component
            rebar_calculator: Rebar calculation component
            geometry_calculator: Geometry calculation component
            data_processor: Data processing component
            dxf_writer: Optional DXF writer for drawing space integration
        """
        self.config = config
        self.table_drawer = table_drawer
        self.rebar_drawer = rebar_drawer
        self.rebar_calculator = rebar_calculator
        self.geometry_calculator = geometry_calculator
        self.data_processor = data_processor
        self.column_sorter = column_sorter
        self.dxf_writer = dxf_writer

        # Initialize coordinators and orchestrators
        self.link_mark_coordinator = LinkMarkCoordinator(column_sorter)
        self.section_orchestrator = SectionOrchestrator(
            config, rebar_drawer, rebar_calculator)

        # Initialize drawing space organizer
        self.drawing_space_organizer = DrawingSpaceOrganizer(config)

        # Initialize drawing space generator (will be set when DXF writer is available)
        self._drawing_space_generator = None

        # Storage for rebar data
        self.column_rebar_data = {}

    def set_dxf_writer(self, dxf_writer):
        """
        Set the DXF writer and initialize the drawing space generator.

        Args:
            dxf_writer: DXF writer instance
        """
        self.dxf_writer = dxf_writer
        if dxf_writer:
            # Import here to avoid circular imports
            from drawing_spaces.drawing_space_generator import DXFDrawingSpaceGenerator

            modelspace = dxf_writer.get_modelspace()
            self._drawing_space_generator = DXFDrawingSpaceGenerator(
                modelspace,
                layer_name="AIS070__"  # Drawing space layer
            )
            logger.info("Drawing space generator initialized")

    def process_columns_with_zones_and_spaces(self, columns_data: List[Tuple[ColumnConfig, ZoneConfigSet]]) -> int:
        """
        Process all column configurations with zone details and organize into drawing spaces.
        This is the enhanced version that uses drawing space organization.

        Args:
            columns_data: List of (column_config, zone_config_set) tuples

        Returns:
            int: Number of successfully processed columns
        """
        # Import link mark manager here to avoid circular imports
        from ..managers.link_mark_manager import reset_global_link_mark_manager

        # Reset the global link mark manager for this drawing session
        reset_global_link_mark_manager()
        logger.info("Reset global link mark manager for new drawing session")

        successful_count = 0

        # Sort columns by floor level for correct link mark assignment order
        floor_sorted_columns = self.column_sorter.sort_columns_by_floor_level(
            columns_data)

        # Group columns by their mark for horizontal arrangement
        column_groups_by_mark = self.column_sorter.group_columns_by_mark(
            floor_sorted_columns)
        total_column_groups = len(column_groups_by_mark)
        total_columns = len(floor_sorted_columns)

        # Show progress with drawing space information
        print(
            f"Processing {total_columns} columns in {total_column_groups} column mark groups with drawing space organization...")
        print(f"Drawing space layout: A1 size at 1:50 scale, max 4 tables per row, 2 rows per space")
        print(f"Link mark assignment order: Floor level -> Zones A->B->C->D -> 52 links before 25a links")

        # Log column grouping details
        for column_mark, column_group in column_groups_by_mark.items():
            floor_names = [config.floor for config, _ in column_group]
            floor_levels = [self.column_sorter.get_floor_level_for_sorting(
                config) for config, _ in column_group]
            print(
                f"  Column Mark Group: {column_mark} - {len(column_group)} tables")
            print(f"    Floor Names: {', '.join(floor_names)}")
            print(f"    Floor Levels: {floor_levels} (arranged left to right)")

        # Pre-validate all columns for potential Layer 2 rebar constraint issues
        print("Checking for potential rebar constraint issues...")
        for column_config, zone_config_set in floor_sorted_columns:
            self.data_processor.validate_and_warn_layer2_constraints(
                column_config)

        # Pre-assign all link marks in the correct order before drawing
        self.link_mark_coordinator.pre_assign_link_marks_for_all_groups(
            column_groups_by_mark)

        # Organize column groups into drawing spaces
        drawing_spaces = self.drawing_space_organizer.organize_column_groups_into_spaces(
            column_groups_by_mark)

        print(f"Organized tables into {len(drawing_spaces)} drawing spaces")

        # Process each drawing space
        for space_config in drawing_spaces:
            try:
                space_number = space_config['space_number']
                print(
                    f"\n-> Processing Drawing Space {space_number} ({space_config['table_count']} tables)")

                # Create drawing space boundary if generator is available
                if self._drawing_space_generator:
                    self._add_drawing_space_boundary(space_config)

                # Process tables within this drawing space
                space_successful_count = self._process_drawing_space_tables(
                    space_config)
                successful_count += space_successful_count

                print(
                    f"OK Completed Drawing Space {space_number}: {space_successful_count} tables processed")

            except Exception as e:
                print(
                    f"FAILED Failed to process Drawing Space {space_config['space_number']}: {e}")
                logger.error(
                    f"Failed to process Drawing Space {space_config['space_number']}: {e}")
                continue

        # Log link mark assignment summary
        self.link_mark_coordinator.log_link_mark_summary()

        return successful_count

    def _add_drawing_space_boundary(self, space_config: Dict[str, Any]) -> None:
        """
        Add drawing space boundary rectangle and labels.

        Args:
            space_config: Drawing space configuration dictionary
        """
        if not self._drawing_space_generator:
            return

        try:
            # Get drawing space parameters
            origin_x = space_config['origin_x']
            origin_y = space_config['origin_y']
            width = space_config['drawing_space_width']
            height = space_config['drawing_space_height']
            space_number = space_config['space_number']

            # Generate title and scale text
            title = self.drawing_space_organizer.get_drawing_space_title(
                space_number)
            scale_text = self.drawing_space_organizer.get_drawing_space_scale_text()

            # Add the drawing space boundary
            self._drawing_space_generator.add_drawing_space(
                origin_x, origin_y, width, height,
                title=title,
                scale_text=scale_text,
                title_height=200.0,  # Large title for engineering drawings
                scale_text_height=120.0  # Medium scale text
            )

            logger.info(
                f"Added drawing space boundary for space {space_number}")

        except Exception as e:
            logger.error(f"Error adding drawing space boundary: {e}")
            # Continue without boundary - not critical

    def _process_drawing_space_tables(self, space_config: Dict[str, Any]) -> int:
        """
        Process tables within a single drawing space.

        Args:
            space_config: Drawing space configuration dictionary

        Returns:
            int: Number of successfully processed tables
        """
        successful_count = 0
        grouped_tables = space_config['grouped_tables']

        # Process each column mark group within the drawing space
        for column_mark, table_positions in grouped_tables.items():
            try:
                print(
                    f"  -> Processing Column Mark Group: {column_mark} ({len(table_positions)} tables)")

                # Extract table data and positions
                table_data_list = []
                position_list = []

                for pos_info in table_positions:
                    table_data_list.append(pos_info['table_data'])
                    position_list.append((pos_info['x'], pos_info['y']))

                # Process this column group with specified positions
                group_successful_count = self._generate_positioned_column_group(
                    column_mark, table_data_list, position_list
                )

                successful_count += group_successful_count
                print(
                    f"    OK Completed {column_mark}: {group_successful_count} tables")

            except Exception as e:
                print(
                    f"    FAILED Failed to process column mark group {column_mark}: {e}")
                logger.error(
                    f"Failed to process column mark group {column_mark}: {e}")
                continue

        return successful_count

    def _generate_positioned_column_group(
        self,
        column_mark: str,
        table_data_list: List[Tuple[ColumnConfig, ZoneConfigSet]],
        position_list: List[Tuple[float, float]]
    ) -> int:
        """
        Generate column tables at specified positions within a drawing space.

        Args:
            column_mark: Column mark shared by all tables
            table_data_list: List of (column_config, zone_config_set) tuples
            position_list: List of (x, y) positions for each table

        Returns:
            int: Number of successfully generated tables
        """
        successful_count = 0

        try:
            logger.info(
                f"Generating positioned column group for mark {column_mark} with {len(table_data_list)} tables")

            # Process each table at its specified position
            for table_index, ((column_config, zone_config_set), (table_x, table_y)) in enumerate(zip(table_data_list, position_list)):
                try:
                    logger.debug(
                        f"  Table {table_index+1}/{len(table_data_list)}: {column_config.name} at ({table_x}, {table_y})")

                    # Draw table structure at the specified position
                    table_bounds = self.table_drawer.draw_table_structure(
                        table_x, table_y, table_index=table_index, is_first_in_group=(
                            table_index == 0)
                    )

                    # Add table content with zone details
                    self.table_drawer.add_table_content(
                        column_config, table_x, table_y,
                        actual_rebar_data=None,  # No embedded section data for zone detail tables
                        zone_config_set=zone_config_set
                    )

                    successful_count += 1
                    logger.debug(
                        f"  Completed table content for {column_config.name}")

                except Exception as e:
                    logger.error(
                        f"Error processing table for {column_config.name}: {e}")
                    # Continue with next table even if one fails
                    continue

            logger.info(
                f"Completed positioned column group {column_mark}: {successful_count}/{len(table_data_list)} tables")
            return successful_count

        except Exception as e:
            logger.error(
                f"Error generating positioned column group {column_mark}: {e}")
            return successful_count

    def process_columns(self, columns_data: List[ColumnConfig]) -> int:
        """
        Process all column configurations and generate drawings.

        Args:
            columns_data: List of column configurations

        Returns:
            int: Number of successfully processed columns
        """
        successful_count = 0
        current_y = self.config.DRAWING_START_Y_POSITION
        total_columns = len(columns_data)

        # Show progress in batches of 5
        print(f"Processing {total_columns} columns...")

        # Pre-validate all columns for potential Layer 2 issues
        print("Checking for potential rebar constraint issues...")
        for column_config in columns_data:
            self.data_processor.validate_and_warn_layer2_constraints(
                column_config)

        for i, column_config in enumerate(columns_data):
            try:
                # Only log every 5th column or first/last to reduce noise
                if i == 0 or (i + 1) % 5 == 0 or i == total_columns - 1:
                    print(
                        f"  -> Column {i+1}/{total_columns}: {column_config.name}")

                # Generate drawing for this column
                next_y = self.generate_single_column_drawing(
                    column_config,
                    origin_x=0,
                    origin_y=current_y
                )

                current_y = next_y
                successful_count += 1

            except Exception as e:
                print(
                    f"  FAILED Failed to process column {column_config.name}: {e}")
                logger.error(
                    f"Failed to process column {column_config.name}: {e}")
                continue

        return successful_count

    def process_columns_with_zones(self, columns_data: List[Tuple[ColumnConfig, ZoneConfigSet]]) -> int:
        """
        Process all column configurations with zone details and generate drawings.
        Implements correct ordering for link mark assignment: sort by floor level,
        then process zones A->B->C->D, then 52 links before 25a links.
        Tables with the same column mark are arranged horizontally.

        Args:
            columns_data: List of (column_config, zone_config_set) tuples

        Returns:
            int: Number of successfully processed columns
        """
        # Import link mark manager here to avoid circular imports
        from ..managers.link_mark_manager import reset_global_link_mark_manager

        # Reset the global link mark manager for this drawing session
        reset_global_link_mark_manager()
        logger.info("Reset global link mark manager for new drawing session")

        successful_count = 0
        current_y = self.config.DRAWING_START_Y_POSITION

        # Sort columns by floor level for correct link mark assignment order
        floor_sorted_columns = self.column_sorter.sort_columns_by_floor_level(
            columns_data)

        # Group columns by their mark for horizontal arrangement
        column_groups_by_mark = self.column_sorter.group_columns_by_mark(
            floor_sorted_columns)
        total_column_groups = len(column_groups_by_mark)
        total_columns = len(floor_sorted_columns)

        # Show progress with descriptive terminology
        print(
            f"Processing {total_columns} columns in {total_column_groups} column mark groups with zone details...")
        print(f"Link mark assignment order: Floor level -> Zones A->B->C->D -> 52 links before 25a links")
        print(f"Table arrangement: Horizontal layout grouped by column mark, sorted by floor level")

        # Log column grouping details with floor level information
        for column_mark, column_group in column_groups_by_mark.items():
            floor_names = [config.floor for config, _ in column_group]
            floor_levels = [self.column_sorter.get_floor_level_for_sorting(
                config) for config, _ in column_group]
            print(
                f"  Column Mark Group: {column_mark} - {len(column_group)} tables")
            print(f"    Floor Names: {', '.join(floor_names)}")
            print(f"    Floor Levels: {floor_levels} (arranged left to right)")

        # Pre-validate all columns for potential Layer 2 rebar constraint issues
        print("Checking for potential rebar constraint issues...")
        for column_config, zone_config_set in floor_sorted_columns:
            self.data_processor.validate_and_warn_layer2_constraints(
                column_config)

        # Pre-assign all link marks in the correct order before drawing
        self.link_mark_coordinator.pre_assign_link_marks_for_all_groups(
            column_groups_by_mark)

        # Process each column mark group horizontally
        group_count = 0
        for column_mark, column_group_data in column_groups_by_mark.items():
            group_count += 1
            try:
                print(
                    f"  -> Processing Column Mark Group {group_count}/{total_column_groups}: {column_mark} ({len(column_group_data)} tables)")

                # Debug the group layout (optional, can be disabled)
                if logger.level <= logging.DEBUG:
                    print(
                        f"    Drawing column group at drawing origin ({0}, {current_y})")
                    floor_sequence = [
                        f"{config.floor}({self.column_sorter.get_floor_level_for_sorting(config):.1f})" for config, _ in column_group_data]
                    print(
                        f"    Floor sequence (Left->Right): {' -> '.join(floor_sequence)}")

                # Generate horizontal group of drawings for this column mark
                next_y = self.generate_horizontal_column_group(
                    column_mark, column_group_data,
                    origin_x=0,
                    origin_y=current_y
                )

                current_y = next_y
                successful_count += len(column_group_data)

                print(
                    f"    OK Completed column mark group {column_mark}, next Y position: {next_y:.1f}")

            except Exception as e:
                print(
                    f"  FAILED Failed to process column mark group {column_mark}: {e}")
                logger.error(
                    f"Failed to process column mark group {column_mark}: {e}")
                continue

        # Log link mark assignment summary
        self.link_mark_coordinator.log_link_mark_summary()

        return successful_count

    def generate_single_column_drawing(
        self,
        column_config: ColumnConfig,
        origin_x: float,
        origin_y: float
    ) -> float:
        """
        Generate a complete drawing for a single column.

        Args:
            column_config: Column configuration
            origin_x: X coordinate for table origin
            origin_y: Y Coordinate for table origin

        Returns:
            float: Y coordinate for next table position
        """
        try:
            # Draw table structure first (single table, so it's the first in its group)
            table_bounds = self.table_drawer.draw_table_structure(
                origin_x, origin_y, table_index=0, is_first_in_group=True)

            # Draw embedded column section to get actual rebar data
            section_bounds, actual_rebar_data = self.draw_column_section(
                column_config, origin_x, origin_y)

            # Add table content with actual rebar data
            self.table_drawer.add_table_content(
                column_config, origin_x, origin_y, actual_rebar_data)

            # Calculate next position
            next_y = origin_y - table_bounds[3] - 500  # Table height + spacing

            logger.debug(f"Generated drawing for {column_config.name}")
            return next_y

        except Exception as e:
            logger.error(
                f"Error generating drawing for {column_config.name}: {e}")
            raise

    def generate_single_column_drawing_with_zones(
        self,
        column_config: ColumnConfig,
        zone_config_set: ZoneConfigSet,
        origin_x: float,
        origin_y: float
    ) -> float:
        """
        Generate a complete drawing for a single column with zone details.

        Args:
            column_config: Column configuration
            zone_config_set: Zone configurations for detailed drawings
            origin_x: X coordinate for table origin
            origin_y: Y coordinate for table origin

        Returns:
            float: Y coordinate for next table position
        """
        try:
            # Draw table structure first (single table, so it's the first in its group)
            table_bounds = self.table_drawer.draw_table_structure(
                origin_x, origin_y, table_index=0, is_first_in_group=True)

            # Note: We no longer draw the embedded section in the large cell
            # Instead, zone details are drawn directly in their respective cells

            # Add table content with zone details (no embedded section)
            self.table_drawer.add_table_content(
                column_config, origin_x, origin_y,
                actual_rebar_data=None,  # No embedded section data
                zone_config_set=zone_config_set
            )

            # Calculate next position
            next_y = origin_y - table_bounds[3] - 500  # Table height + spacing

            logger.debug(
                f"Generated drawing with zone details for {column_config.name}")
            return next_y

        except Exception as e:
            logger.error(
                f"Error generating drawing with zones for {column_config.name}: {e}")
            raise

    def draw_column_section(
        self,
        column_config: ColumnConfig,
        table_origin_x: float,
        table_origin_y: float
    ) -> Tuple[Tuple[float, float, float, float], RebarLayerData]:
        """
        Draw the embedded column section within the table at real scale (1:1).

        Args:
            column_config: Column configuration
            table_origin_x: Table origin X coordinate
            table_origin_y: Table origin Y coordinate

        Returns:
            tuple: (section_bounds, rebar_data) where:
                - section_bounds: (x, y, width, height)
                - rebar_data: RebarLayerData with actual drawn positions
        """
        try:
            # Get cell positions for section drawing
            cell_positions = self.config.get_cell_positions()

            # Calculate section cell bounds
            section_x = table_origin_x + cell_positions['col3_x'] + 100
            section_y = table_origin_y + cell_positions['row3_y'] + 100
            section_width = cell_positions['section_cell_width'] - 200
            section_height = cell_positions['section_cell_height'] - 200

            # Use real scale (1:1) - no scaling applied
            scale = 1.0
            actual_width = column_config.B  # Use actual column width from CSV
            actual_height = column_config.D  # Use actual column height from CSV

            # Center the section in the cell
            centered_x, centered_y = self.geometry_calculator.center_in_rectangle(
                actual_width, actual_height,
                section_x, section_y, section_width, section_height
            )

            # Draw column outline at real scale
            self.rebar_drawer.draw_column_outline(
                centered_x, centered_y, actual_width, actual_height
            )

            # Draw reinforcement layers at real scale and get actual rebar data
            rebar_data = self.section_orchestrator.draw_reinforcement_layers(
                column_config, centered_x, centered_y,
                actual_width, actual_height, scale
            )

            # Store the organized rebar data for potential future use
            self.column_rebar_data[column_config.name] = rebar_data

            # Draw dimensions using actual values
            self.rebar_drawer.draw_section_dimensions(
                centered_x, centered_y, actual_width, actual_height,
                column_config.B, column_config.D
            )

            section_bounds = (centered_x, centered_y,
                              actual_width, actual_height)
            return section_bounds, rebar_data

        except Exception as e:
            logger.error(f"Error drawing column section: {e}")
            raise

    def get_column_rebar_data(self, column_name: str = None):
        """
        Get organized rebar data for a specific column or all columns.

        Args:
            column_name: Name of the column to retrieve data for (optional)

        Returns:
            RebarLayerData for specific column, or dict of all column data
        """
        if column_name:
            return self.column_rebar_data.get(column_name)
        else:
            return self.column_rebar_data

    def generate_horizontal_column_group(
        self,
        column_mark: str,
        group_data: List[Tuple[ColumnConfig, ZoneConfigSet]],
        origin_x: float,
        origin_y: float
    ) -> float:
        """
        Generate a horizontal group of drawings for columns with the same mark.
        Uses intelligent table structure drawing to prevent duplicate lines.

        Args:
            column_mark: The column mark shared by all columns in the group
            group_data: List of (column_config, zone_config_set) tuples for the group
            origin_x: X coordinate for group origin
            origin_y: Y coordinate for group origin

        Returns:
            float: Y coordinate for next group position
        """
        try:
            # Safety check for empty group
            if not group_data:
                logger.warning(
                    f"Empty column group data for column mark {column_mark}")
                return origin_y

            # Use intelligent spacing based on group size for optimal layout
            horizontal_table_spacing = self.table_drawer.get_recommended_horizontal_spacing(
                len(group_data))
            max_table_height = 0

            logger.info(
                f"Generating horizontal column group for mark {column_mark} with {len(group_data)} tables (spacing: {horizontal_table_spacing})")

            # Draw table structures first with intelligent line management
            # Note: group_data is already sorted by floor level from _group_columns_by_mark
            table_bounds_list = self.table_drawer.draw_table_group_structure(
                group_data, origin_x, origin_y, horizontal_table_spacing
            )

            # Add content to each table in the horizontal group
            for table_index, ((column_config, zone_config_set), table_bounds) in enumerate(zip(group_data, table_bounds_list)):
                try:
                    logger.debug(
                        f"  Table {table_index+1}/{len(group_data)}: {column_config.name} (floor: {column_config.floor}) at x={table_bounds[0]}")

                    # Add table content with zone details
                    self.table_drawer.add_table_content(
                        column_config, table_bounds[0], table_bounds[1],
                        actual_rebar_data=None,  # No embedded section data for zone detail tables
                        zone_config_set=zone_config_set
                    )

                    # Track maximum table height for vertical spacing calculation
                    max_table_height = max(max_table_height, table_bounds[3])

                    logger.debug(
                        f"  Completed table content for {column_config.name}")

                except Exception as e:
                    logger.error(
                        f"Error adding content to table for {column_config.name}: {e}")
                    # Continue with next table even if one fails
                    continue

            # Calculate next Y position based on maximum table height in the group
            vertical_group_spacing = 1000  # Standard spacing between column mark groups
            next_y_position = origin_y - max_table_height - vertical_group_spacing

            logger.info(
                f"Completed horizontal column group {column_mark}: {len(group_data)} tables, max height: {max_table_height:.1f}")
            return next_y_position

        except Exception as e:
            logger.error(
                f"Error generating horizontal column group {column_mark}: {e}")
            raise
