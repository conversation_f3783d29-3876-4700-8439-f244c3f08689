"""
Drawing Space Organizer for Column Tables

This module provides functionality to organize column detail tables into structured
drawing spaces within DXF documents. Implements automatic grouping with proper
spacing and overflow handling for multiple drawing spaces.
"""

import logging
from typing import List, Tuple, Dict, Any
from ..models.column_config import ColumnConfig
from ..models.zone_config import ZoneConfigSet
from ..models.drawing_config import DrawingConfig

logger = logging.getLogger(__name__)


class DrawingSpaceOrganizer:
    """
    Organizes column detail tables into structured drawing spaces.

    This class handles the spatial organization of column tables within drawing spaces,
    implementing the layout specifications:
    - Supports A0 and A1 paper sizes with configurable scale factors
    - Dynamic layout based on table dimensions and drawing space size
    - Automatic overflow to additional drawing spaces
    """

    def __init__(self, config: DrawingConfig):
        """
        Initialize the drawing space organizer.

        Args:
            config: Drawing configuration object
        """
        self.config = config        # Import drawing space utilities
        from drawing_spaces.drawing_space_generator import get_drawing_space_dimensions

        # Drawing space specifications - FIXED values for A1 1:50 scale
        self.paper_size = 'A1'  # Fixed to A1
        self.scale_factor = 50  # Fixed to 1:50 scale
        self.landscape = True   # Use landscape orientation

        # Calculate drawing space dimensions based on paper size and scale
        self.drawing_space_width, self.drawing_space_height = get_drawing_space_dimensions(
            self.paper_size, self.scale_factor, self.landscape
        )

        # Ensure dimensions match the exact requirements for A1 1:50 scale
        # A1 at 1:50 scale should be 34,800mm × 28,200mm
        if abs(self.drawing_space_width - 34800) > 100 or abs(self.drawing_space_height - 28200) > 100:
            logger.warning(
                f"Drawing space dimensions don't match A1 1:50 requirements. Forcing correct dimensions.")
            self.drawing_space_width = 34800.0  # Fixed width for A1 1:50
            self.drawing_space_height = 28200.0  # Fixed height for A1 1:50

        # Table layout specifications - per requirements
        self.max_tables_per_row = 4  # Maximum 4 tables per row
        self.max_rows_per_space = 2  # Maximum 2 rows per drawing space
        # Maximum 8 tables per drawing space (4x2)
        self.max_tables_per_space = 8

        # Table dimensions (from TableConfig)
        from ..models.table_config import TableConfig
        table_config = TableConfig()
        self.table_width = table_config.get_table_width()  # mm (actual table width)
        self.table_height = table_config.get_table_height()  # mm (actual table height)

        # Spacing specifications - using fixed 500mm clear spacing per requirements
        # mm (margin around drawing space edges)
        self.margin = 2000.0
        # mm (fixed 500mm clear space between tables horizontally)
        self.table_horizontal_spacing = 500.0
        # mm (fixed 500mm clear space between rows vertically)
        self.table_vertical_spacing = 500.0

        # Calculate and adjust drawing space if needed based on table layout
        self._adjust_drawing_space_to_content()

        logger.info(f"Drawing space organizer initialized:")
        logger.info(
            f"  Paper size: {self.paper_size} {'Landscape' if self.landscape else 'Portrait'}")
        logger.info(f"  Scale factor: 1:{self.scale_factor}")
        logger.info(
            f"  Table dimensions: {self.table_width:.0f} x {self.table_height:.0f} mm")
        logger.info(
            f"  Drawing space: {self.drawing_space_width:.0f} x {self.drawing_space_height:.0f} mm")
        logger.info(
            f"  Layout: max {self.max_tables_per_row} tables/row, {self.max_rows_per_space} rows/space")
        logger.info(
            f"  Spacing: Horizontal={self.table_horizontal_spacing:.0f}mm, Vertical={self.table_vertical_spacing:.0f}mm, Margin={self.margin:.0f}mm")

    def organize_column_groups_into_spaces(
        self,
        column_groups_by_mark: Dict[str,
                                    List[Tuple[ColumnConfig, ZoneConfigSet]]]
    ) -> List[Dict[str, Any]]:
        """
        Organize column groups into drawing spaces with proper layout.

        Args:
            column_groups_by_mark: Dictionary of column mark -> list of column data

        Returns:
            List of drawing space dictionaries, each containing:
            - space_number: Drawing space number (1, 2, 3, ...)
            - origin_x, origin_y: Origin coordinates for the drawing space
            - column_groups: List of column groups in this space
            - table_positions: List of (group_index, table_index, x, y) positions
        """
        drawing_spaces = []
        current_space_number = 1
        current_space_tables = []
        current_space_groups = []

        # Flatten column groups into individual tables for counting
        all_tables = []
        for column_mark, group_data in column_groups_by_mark.items():
            for table_data in group_data:
                all_tables.append((column_mark, table_data))

        total_tables = len(all_tables)
        logger.info(
            f"Organizing {total_tables} tables into drawing spaces (max {self.max_tables_per_space} per space)")

        # Group tables into drawing spaces
        table_index = 0
        while table_index < total_tables:
            # Determine how many tables can fit in current space
            remaining_tables = total_tables - table_index
            tables_in_this_space = min(
                remaining_tables, self.max_tables_per_space)

            # Extract tables for this space
            space_tables = all_tables[table_index:table_index +
                                      tables_in_this_space]

            # Create drawing space configuration
            space_config = self._create_drawing_space_config(
                current_space_number, space_tables
            )

            drawing_spaces.append(space_config)

            logger.info(
                f"Drawing space {current_space_number}: {tables_in_this_space} tables")

            table_index += tables_in_this_space
            current_space_number += 1

        logger.info(
            f"Created {len(drawing_spaces)} drawing spaces for {total_tables} tables")

        # Call the logging method to help with debugging
        self.log_table_layout_details(drawing_spaces)

        return drawing_spaces

    def _create_drawing_space_config(
        self,
        space_number: int,
        space_tables: List[Tuple[str, Tuple[ColumnConfig, ZoneConfigSet]]]
    ) -> Dict[str, Any]:
        """
        Create configuration for a single drawing space.

        Args:
            space_number: Drawing space number (1-based)
            space_tables: List of (column_mark, table_data) tuples for this space

        Returns:
            Dictionary with drawing space configuration
        """
        # Calculate drawing space origin (spaces arranged vertically)
        space_y_spacing = self.drawing_space_height + 2000.0  # 2m gap between spaces
        origin_x = 0.0
        origin_y = -(space_number - 1) * space_y_spacing

        # Calculate table positions within the drawing space
        table_positions = []

        # Arrange tables in grid layout (max 4 per row, max 2 rows)
        for i, (column_mark, table_data) in enumerate(space_tables):
            row = i // self.max_tables_per_row
            col = i % self.max_tables_per_row

            # Implement fixed positioning based on requirements
            # 1. First table: 500mm right, 500mm down from top-left corner
            # 2. Fixed 500mm clear spacing between tables horizontally
            # 3. Fixed 500mm clear spacing between rows vertically
            # 4. Grid arrangement filling horizontally first (already implemented by row/col)

            # Calculate the fixed horizontal spacing between tables
            # This is 500mm clear space between tables, so add table width
            fixed_horizontal_spacing = self.table_width + 500.0

            # Calculate the fixed vertical spacing between rows
            # This is 500mm clear space between rows, so add table height
            fixed_vertical_spacing = self.table_height + 500.0
            # Origin is at bottom-left, so top-left is (origin_x, origin_y + drawing_space_height)
            top_left_x = origin_x
            top_left_y = origin_y + self.drawing_space_height

            # Position the first table's TOP-LEFT corner 500mm right and 500mm down from drawing space top-left
            first_table_x = top_left_x + 500.0
            # This is the TOP of the table, 500mm down from drawing space top
            first_table_top_y = top_left_y - 500.0

            # Add detailed debug logging for the first table in each space
            if i == 0:
                logger.info(f"POSITION DEBUG: Space {space_number}, Table 1")
                logger.info(
                    f"  Drawing space origin (bottom-left): ({origin_x:.0f}, {origin_y:.0f})")
                logger.info(
                    f"  Drawing space dimensions: {self.drawing_space_width:.0f} × {self.drawing_space_height:.0f}")
                logger.info(
                    f"  Drawing top-left corner: ({top_left_x:.0f}, {top_left_y:.0f})")
                logger.info(
                    f"  Table dimensions: {self.table_width:.0f} × {self.table_height:.0f}")
                logger.info(
                    f"  First table top-left: ({first_table_x:.0f}, {first_table_top_y:.0f}) [500mm down from drawing top]")
                logger.info(
                    f"  First table bottom-left: ({first_table_x:.0f}, {first_table_top_y - self.table_height:.0f})")
                distance_from_top = top_left_y - first_table_top_y
                logger.info(
                    f"  Distance from drawing top to table top: {distance_from_top:.0f}mm [should be 500mm]")

                # Calculate the position in the drawing scale (1:50)
                logger.info(
                    f"  In drawing scale (1:{self.scale_factor}): Top of first table is {distance_from_top/self.scale_factor:.0f}mm from top edge")

            # Add debug output at the start
            print(f"\n=== DRAWING SPACE {space_number} DEBUG ===")
            print(f"Drawing space origin: ({origin_x:.0f}, {origin_y:.0f})")

            # Calculate the top-left corner of the drawing space
            top_left_x = origin_x
            top_left_y = origin_y + self.drawing_space_height

            print(f"Drawing space top-left Y: {top_left_y:.0f}")
            print(
                f"Drawing space dimensions: {self.drawing_space_width:.0f} x {self.drawing_space_height:.0f}")

            # Position the first table's TOP-LEFT corner 500mm right and 500mm down from drawing space top-left
            first_table_x = top_left_x + 500.0
            # This is the TOP of the table, 500mm down from drawing space top
            first_table_top_y = top_left_y - 500.0

            print(f"First table top Y target: {first_table_top_y:.0f}")
            print(
                f"Table dimensions: {self.table_width:.0f} x {self.table_height:.0f}")
            print(f"Tables to position: {len(space_tables)}")

            # Calculate table positions within the drawing space
            table_positions = []

            # Arrange tables in grid layout (max 4 per row, max 2 rows)
            for i, (column_mark, table_data) in enumerate(space_tables):
                row = i // self.max_tables_per_row
                col = i % self.max_tables_per_row

                # Implement fixed positioning based on requirements
                # 1. First table: 500mm right, 500mm down from top-left corner
                # 2. Fixed 500mm clear spacing between tables horizontally
                # 3. Fixed 500mm clear spacing between rows vertically
                # 4. Grid arrangement filling horizontally first (already implemented by row/col)

                # Calculate the fixed horizontal spacing between tables
                # This is 500mm clear space between tables, so add table width
                fixed_horizontal_spacing = self.table_width + 500.0

                # Calculate the fixed vertical spacing between rows
                # This is 500mm clear space between rows, so add table height
                fixed_vertical_spacing = self.table_height + 500.0
                # Origin is at bottom-left, so top-left is (origin_x, origin_y + drawing_space_height)
                top_left_x = origin_x
                top_left_y = origin_y + self.drawing_space_height

                # Position the first table's TOP-LEFT corner 500mm right and 500mm down from drawing space top-left
                first_table_x = top_left_x + 500.0
                # This is the TOP of the table, 500mm down from drawing space top
                first_table_top_y = top_left_y - 500.0

                # Add detailed debug logging for the first table in each space
                if i == 0:
                    logger.info(
                        f"POSITION DEBUG: Space {space_number}, Table 1")
                    logger.info(
                        f"  Drawing space origin (bottom-left): ({origin_x:.0f}, {origin_y:.0f})")
                    logger.info(
                        f"  Drawing space dimensions: {self.drawing_space_width:.0f} × {self.drawing_space_height:.0f}")
                    logger.info(
                        f"  Drawing top-left corner: ({top_left_x:.0f}, {top_left_y:.0f})")
                    logger.info(
                        f"  Table dimensions: {self.table_width:.0f} × {self.table_height:.0f}")
                    logger.info(
                        f"  First table top-left: ({first_table_x:.0f}, {first_table_top_y:.0f}) [500mm down from drawing top]")
                    logger.info(
                        f"  First table bottom-left: ({first_table_x:.0f}, {first_table_top_y - self.table_height:.0f})")
                    distance_from_top = top_left_y - first_table_top_y
                    logger.info(
                        f"  Distance from drawing top to table top: {distance_from_top:.0f}mm [should be 500mm]")

                    # Calculate the position in the drawing scale (1:50)
                    logger.info(
                        f"  In drawing scale (1:{self.scale_factor}): Top of first table is {distance_from_top/self.scale_factor:.0f}mm from top edge")

                # Add debug output at the start
                print(f"\n=== DRAWING SPACE {space_number} DEBUG ===")
                print(
                    f"Drawing space origin: ({origin_x:.0f}, {origin_y:.0f})")

                # Calculate the top-left corner of the drawing space
                top_left_x = origin_x
                top_left_y = origin_y + self.drawing_space_height

                print(f"Drawing space top-left Y: {top_left_y:.0f}")
                print(
                    f"Drawing space dimensions: {self.drawing_space_width:.0f} x {self.drawing_space_height:.0f}")

                # Position the first table's TOP-LEFT corner 500mm right and 500mm down from drawing space top-left
                first_table_x = top_left_x + 500.0
                # This is the TOP of the table, 500mm down from drawing space top
                first_table_top_y = top_left_y - 500.0

                print(f"First table top Y target: {first_table_top_y:.0f}")
                print(
                    f"Table dimensions: {self.table_width:.0f} x {self.table_height:.0f}")
                print(f"Tables to position: {len(space_tables)}")

                # Calculate this table's position (we store the BOTTOM-LEFT corner in our data structure)
                table_x = first_table_x + col * fixed_horizontal_spacing

                # For the Y position, we need the BOTTOM-LEFT corner of the table
                # The TOP of each row should be positioned as follows:
                # - 1st row: 500 units from the top horizontal line of the drawing space
                # - 2nd row: (2 × 500) + 1 table height from the top horizontal line
                # - Subsequent rows: Follow the same pattern

                if row == 0:
                    # First row: TOP should be exactly 500mm from drawing space top
                    # Since first_table_top_y is already 500mm down from top, use it directly
                    # Bottom-left Y = TOP Y - table height
                    table_y = first_table_top_y - self.table_height
                else:
                    # Subsequent rows: TOP should be at (row+1) × 500 + row × table_height from drawing space top
                    # This ensures 500mm clear space between rows
                    row_top_y = top_left_y - \
                        ((row + 1) * 500.0 + row * self.table_height)
                    table_y = row_top_y - self.table_height

                # Add debug logging for row positioning verification
                if col == 0:  # First table in each row
                    current_table_top_y = table_y + self.table_height
                    distance_from_drawing_top = top_left_y - current_table_top_y
                    expected_distance = (row + 1) * 500.0 + \
                        row * self.table_height
                    print(f"  Row {row + 1} positioning verification:")
                    print(f"    Table top Y: {current_table_top_y:.0f}")
                    print(
                        f"    Distance from drawing top to table top: {distance_from_drawing_top:.0f}mm")
                    print(
                        f"    Expected distance: {expected_distance:.0f}mm")
                    match_status = "MATCH" if abs(
                        distance_from_drawing_top - expected_distance) < 1.0 else "MISMATCH"
                    print(f"    Match: {match_status}")

                    # Also log these important coordinates for debugging
                    logger.info(f"  Row {row + 1} positioning verification:")
                    logger.info(f"    Table top Y: {current_table_top_y:.0f}")
                    logger.info(
                        f"    Distance from drawing top to table top: {distance_from_drawing_top:.0f}mm")
                    logger.info(
                        f"    Expected distance: {expected_distance:.0f}mm")
                    logger.info(f"    Match: {match_status}")

                # Log the table position with detailed spacing information for debugging
                logger.debug(
                    f"Table {i+1} (row {row+1}, col {col+1}) positioned at ({table_x:.0f}, {table_y:.0f})")

                # Log the spacing calculations
                if col > 0:  # Not the first column in the row
                    prev_x = first_table_x + (col-1) * fixed_horizontal_spacing
                    horizontal_spacing = table_x - (prev_x + self.table_width)
                    logger.debug(
                        f"  Horizontal clear spacing from previous table: {horizontal_spacing:.0f}mm")

                if row > 0:  # Not in the first row
                    # Calculate Y position of the table in the row above using the new logic
                    if row - 1 == 0:
                        # Previous row was the first row
                        above_table_bottom_y = first_table_top_y - self.table_height
                    else:
                        # Previous row using the new formula
                        above_row_top_y = top_left_y - \
                            (row * 500.0 + (row - 1) * self.table_height)
                        above_table_bottom_y = above_row_top_y - self.table_height

                    # Current table top Y using the new formula
                    current_table_top_y = table_y + self.table_height

                    # Calculate clear space between bottom of the above table and top of this table
                    vertical_spacing = above_table_bottom_y - current_table_top_y
                    logger.debug(
                        f"  Vertical clear spacing from table above: {vertical_spacing:.0f}mm")

                table_positions.append({
                    'table_index': i,
                    'column_mark': column_mark,
                    'table_data': table_data,
                    'x': table_x,
                    'y': table_y
                })

        # Group tables back by column mark for processing
        grouped_tables = {}
        for pos_info in table_positions:
            column_mark = pos_info['column_mark']
            if column_mark not in grouped_tables:
                grouped_tables[column_mark] = []
            grouped_tables[column_mark].append(pos_info)

        space_config = {
            'space_number': space_number,
            'origin_x': origin_x,
            'origin_y': origin_y,
            'drawing_space_width': self.drawing_space_width,
            'drawing_space_height': self.drawing_space_height,
            'table_count': len(space_tables),
            'table_positions': table_positions,
            'grouped_tables': grouped_tables
        }

        return space_config

    def get_drawing_space_title(self, space_number: int) -> str:
        """
        Get title for a drawing space.

        Args:
            space_number: Drawing space number (1-based)

        Returns:
            Title string for the drawing space
        """
        return f"COLUMN DETAIL TABLES - SHEET {space_number:02d}"

    def get_drawing_space_scale_text(self) -> str:
        """
        Get scale text for drawing spaces.

        Returns:
            Scale text string
        """
        return f"SCALE 1:{self.scale_factor}"

    def log_table_layout_details(self, drawing_spaces: List[Dict[str, Any]]) -> None:
        """
        Log detailed information about table layout in drawing spaces.

        This helps debug table positioning issues and verify that the layout
        meets the requirements.

        Args:
            drawing_spaces: List of drawing space configurations
        """
        for space in drawing_spaces:
            space_num = space['space_number']
            table_count = space['table_count']
            origin_x = space['origin_x']
            origin_y = space['origin_y']
            width = space['drawing_space_width']
            height = space['drawing_space_height']

            logger.info(f"Drawing Space {space_num} - Layout Details:")
            logger.info(f"  Origin: ({origin_x:.0f}, {origin_y:.0f})")
            logger.info(f"  Dimensions: {width:.0f} × {height:.0f} mm")
            logger.info(
                f"  Top-Left Corner: ({origin_x:.0f}, {origin_y + height:.0f})")
            logger.info(f"  Contains {table_count} tables")

            # Log position of each table
            for i, pos in enumerate(space['table_positions']):
                table_x = pos['x']
                table_y = pos['y']
                table_right = table_x + self.table_width
                table_top = table_y + self.table_height

                logger.info(f"  Table {i+1}: Mark {pos['column_mark']}")
                logger.info(f"    Bottom-Left: ({table_x:.0f}, {table_y:.0f})")
                logger.info(
                    f"    Top-Right: ({table_right:.0f}, {table_top:.0f})")
                logger.info(
                    f"    Width × Height: {self.table_width:.0f} × {self.table_height:.0f} mm")

                # For table after the first one in a row, log the horizontal spacing
                if i > 0 and i % self.max_tables_per_row != 0:
                    prev_table = space['table_positions'][i-1]
                    prev_right = prev_table['x'] + self.table_width
                    h_spacing = table_x - prev_right
                    logger.info(
                        f"    Horizontal clear spacing from previous table: {h_spacing:.0f}mm")

                # For tables in rows after the first row, log the vertical spacing
                row = i // self.max_tables_per_row
                if row > 0:
                    table_above_idx = i - self.max_tables_per_row
                    if table_above_idx >= 0:
                        table_above = space['table_positions'][table_above_idx]
                        # Bottom edge of the table above
                        table_above_bottom = table_above['y']
                        # Top edge of this table
                        this_table_top = table_y + self.table_height
                        # Clear space between tables = distance from top edge of this table to bottom edge of table above
                        v_spacing = table_above_bottom - this_table_top
                        logger.info(
                            f"    Vertical clear spacing from table above: {v_spacing:.0f}mm")

    def calculate_total_drawing_height(self, num_spaces: int) -> float:
        """
        Calculate total height needed for all drawing spaces.

        Args:
            num_spaces: Number of drawing spaces

        Returns:
            Total height in mm
        """
        if num_spaces == 0:
            return 0.0

        space_y_spacing = self.drawing_space_height + 2000.0  # 2m gap between spaces
        return num_spaces * space_y_spacing

    def set_drawing_space_config(
        self,
        paper_size: str = 'A1',
        scale_factor: int = 50,
        landscape: bool = True
    ) -> None:
        """
        Set the drawing space configuration parameters.

        Args:
            paper_size: Paper size code ('A0' or 'A1')
            scale_factor: Scale factor (e.g., 50 for 1:50, 100 for 1:100)
            landscape: True for landscape orientation, False for portrait

        Raises:
            ValueError: If paper_size is not supported ('A0' or 'A1' only)
        """
        if paper_size not in ['A0', 'A1']:
            raise ValueError(
                f"Unsupported paper size: {paper_size}. Only 'A0' and 'A1' are supported.")

        self.paper_size = paper_size
        self.scale_factor = scale_factor
        self.landscape = landscape

        # Recalculate drawing space dimensions
        from drawing_spaces.drawing_space_generator import get_drawing_space_dimensions
        self.drawing_space_width, self.drawing_space_height = get_drawing_space_dimensions(
            self.paper_size, self.scale_factor, self.landscape
        )

        logger.info(f"Drawing space configuration updated:")
        logger.info(
            f"  Paper size: {self.paper_size} {'Landscape' if self.landscape else 'Portrait'}")
        logger.info(f"  Scale factor: 1:{self.scale_factor}")
        logger.info(
            f"  Drawing area: {self.drawing_space_width:.0f} x {self.drawing_space_height:.0f} mm")

    def calculate_optimal_scale_factor(
        self,
        min_drawing_width: float,
        min_drawing_height: float,
        paper_size: str = 'A1',
        landscape: bool = True
    ) -> int:
        """
        Calculate the optimal scale factor for a given minimum drawing area.

        This function finds the smallest scale factor that allows the drawing
        to fit within the specified paper size.

        Args:
            min_drawing_width: Minimum width needed for the drawing in real-world mm
            min_drawing_height: Minimum height needed for the drawing in real-world mm
            paper_size: Paper size code ('A0' or 'A1')
            landscape: True for landscape orientation, False for portrait

        Returns:
            The optimal scale factor (e.g., 50 for 1:50)
        """
        from drawing_spaces.drawing_space_generator import get_paper_size

        paper_width, paper_height = get_paper_size(paper_size, landscape)

        # Apply margin factor to get usable space
        margin_factor = 0.9
        usable_width = paper_width * margin_factor
        usable_height = paper_height * margin_factor

        # Calculate minimum scale factor needed
        width_scale = min_drawing_width / usable_width
        height_scale = min_drawing_height / usable_height
        min_scale = max(width_scale, height_scale)

        # Round up to nearest "standard" scale
        standard_scales = [1, 2, 5, 10, 20, 25, 50, 100, 200, 500, 1000]

        for scale in standard_scales:
            if scale >= min_scale:
                return scale

        # If no standard scale found, use the calculated minimum
        return int(min_scale) + 1

    def _adjust_drawing_space_to_content(self) -> None:
        """
        Adjust drawing space dimensions based on table layout requirements.

        Note: This method has been modified to ensure fixed A1 1:50 scale is maintained.
        It will not adjust the scale factor but will warn if content may not fit.
        """
        # Calculate minimum space needed for table layout
        min_width_for_max_tables = (self.max_tables_per_row * self.table_width) + (
            (self.max_tables_per_row - 1) * self.table_horizontal_spacing) + (2 * self.margin)
        min_height_for_max_rows = (self.max_rows_per_space * self.table_height) + (
            (self.max_rows_per_space - 1) * self.table_vertical_spacing) + (2 * self.margin)

        # Check if current drawing space is large enough
        if min_width_for_max_tables > self.drawing_space_width or min_height_for_max_rows > self.drawing_space_height:
            # Instead of adjusting scale, warn about potential overflow
            logger.warning(
                f"Required content size ({min_width_for_max_tables:.0f}×{min_height_for_max_rows:.0f}) "
                f"may not fit in A1 1:50 drawing space ({self.drawing_space_width:.0f}×{self.drawing_space_height:.0f}). "
                f"Content might overflow or appear crowded."
            )

            # Calculate required scale for reference only
            optimal_scale = self.calculate_optimal_scale_factor(
                min_width_for_max_tables,
                min_height_for_max_rows,
                self.paper_size,
                self.landscape
            )

            logger.info(
                f"For reference: Scale factor 1:{optimal_scale} would fit content comfortably "
                f"but fixed 1:50 scale is being maintained per requirements."
            )
