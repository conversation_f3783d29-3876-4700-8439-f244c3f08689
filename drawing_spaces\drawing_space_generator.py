"""
Simplified Drawing Space Generator

This module provides a lightweight utility for adding drawing space rectangles
to existing DXF documents. Focuses on the core responsibility of creating
rectangular boundaries/frames within DXF files.
"""

import logging
from typing import Optional, Tuple, List
from ezdxf.layouts import Modelspace
from ezdxf.entities import LWPolyline

logger = logging.getLogger(__name__)


class DXFDrawingSpaceGenerator:
    """
    Simplified drawing space generator for adding rectangular boundaries to DXF documents.

    This class provides a lightweight interface for adding drawing space rectangles
    to existing DXF modelspaces. Focuses on the core functionality without complex
    configuration or file management.
    """

    def __init__(self, modelspace: Modelspace, layer_name: str = "DRAWING_SPACE"):
        """
        Initialize the drawing space generator.

        Args:
            modelspace: DXF modelspace to draw into
            layer_name: Layer name for drawing space rectangles
        """
        self.msp = modelspace
        self.layer_name = layer_name

    def add_drawing_space(
        self,
        x: float,
        y: float,
        width: float,
        height: float,
        title: Optional[str] = None,
        scale_text: Optional[str] = None,
        title_height: float = 3.5,
        scale_text_height: float = 2.5
    ) -> None:
        """
        Add a single drawing space rectangle to the DXF modelspace.

        Args:
            x: X coordinate of bottom-left corner
            y: Y coordinate of bottom-left corner  
            width: Width of the drawing space
            height: Height of the drawing space
            title: Optional title text (positioned at top-center)
            scale_text: Optional scale text (positioned at bottom-right)
            title_height: Height of title text in mm
            scale_text_height: Height of scale text in mm
        """
        # Create the rectangular boundary
        self._create_rectangle(x, y, width, height)

        # Add title text if provided
        if title:
            self._add_title_text(x, y, width, height, title, title_height)

        # Add scale text if provided
        if scale_text:
            self._add_scale_text(x, y, width, height,
                                 scale_text, scale_text_height, title_height)

    def add_multiple_spaces(
        self,
        spaces: List[Tuple[float, float, float, float]],
        title_prefix: str = "DRAWING SPACE",
        scale_text: Optional[str] = None
    ) -> None:
        """
        Add multiple drawing spaces to the DXF modelspace.

        Args:
            spaces: List of (x, y, width, height) tuples for each space
            title_prefix: Prefix for auto-generated titles
            scale_text: Optional scale text for all spaces
        """
        for i, (x, y, width, height) in enumerate(spaces, 1):
            title = f"{title_prefix} {i:02d}" if title_prefix else None
            self.add_drawing_space(
                x, y, width, height,
                title=title,
                scale_text=scale_text
            )

    def add_horizontal_layout(
        self,
        start_x: float,
        start_y: float,
        width: float,
        height: float,
        count: int,
        spacing: float,
        title_prefix: str = "DRAWING SPACE",
        scale_text: Optional[str] = None
    ) -> None:
        """
        Add drawing spaces in a horizontal layout.

        Args:
            start_x: X coordinate of first space
            start_y: Y coordinate of first space
            width: Width of each space
            height: Height of each space
            count: Number of spaces
            spacing: Spacing between spaces
            title_prefix: Prefix for auto-generated titles
            scale_text: Optional scale text for all spaces
        """
        spaces = []
        for i in range(count):
            x = start_x + i * (width + spacing)
            spaces.append((x, start_y, width, height))

        self.add_multiple_spaces(spaces, title_prefix, scale_text)

    def add_vertical_layout(
        self,
        start_x: float,
        start_y: float,
        width: float,
        height: float,
        count: int,
        spacing: float,
        title_prefix: str = "DRAWING SPACE",
        scale_text: Optional[str] = None
    ) -> None:
        """
        Add drawing spaces in a vertical layout.

        Args:
            start_x: X coordinate of first space
            start_y: Y coordinate of first space (top)
            width: Width of each space
            height: Height of each space
            count: Number of spaces
            spacing: Spacing between spaces
            title_prefix: Prefix for auto-generated titles
            scale_text: Optional scale text for all spaces
        """
        spaces = []
        for i in range(count):
            y = start_y - i * (height + spacing)
            spaces.append((start_x, y, width, height))

        self.add_multiple_spaces(spaces, title_prefix, scale_text)

    def _create_rectangle(self, x: float, y: float, width: float, height: float) -> None:
        """Create a rectangular boundary for the drawing space."""
        points = [
            (x, y),
            (x + width, y),
            (x + width, y + height),
            (x, y + height),
            (x, y)  # Close the rectangle
        ]

        self.msp.add_lwpolyline(
            points,
            dxfattribs={'layer': self.layer_name, 'closed': True}
        )

    def _add_title_text(
        self, x: float, y: float, width: float, height: float,
        title: str, text_height: float
    ) -> None:
        """Add title text above the drawing space rectangle with proper positioning and clearance."""
        # Calculate text dimensions for proper clearance
        text_width, total_text_height = self._calculate_text_dimensions(title, text_height)
        
        # Position title above the drawing space
        # X: Left-align text at the left edge of the drawing space (x=0 relative to drawing space)
        title_x = x
        
        # Y: Position above drawing space with proper clearance
        # Account for text height + ascender space + clearance margin
        text_clearance_margin = text_height * 0.5  # 50% of text height as margin
        title_y = y + height + text_clearance_margin
        
        self.msp.add_text(
            title,
            dxfattribs={
                'layer': self.layer_name,
                'height': text_height,
                'halign': 0,  # Left aligned from insertion point
                'valign': 0,  # Bottom aligned from insertion point
                'insert': (title_x, title_y)
            }
        )

    def _add_scale_text(
        self, x: float, y: float, width: float, height: float,
        scale_text: str, text_height: float, title_height: float = 3.5
    ) -> None:
        """Add scale text above the drawing space, positioned above the title with proper clearance."""
        # Calculate text dimensions for proper clearance
        scale_text_width, scale_total_height = self._calculate_text_dimensions(scale_text, text_height)
        title_text_width, title_total_height = self._calculate_text_dimensions("Sample Title", title_height)
        
        # Position scale text above the drawing space, above the title
        # X: Left-align text at the left edge of the drawing space (x=0 relative to drawing space)
        scale_x = x
        
        # Y: Position above drawing space and above title text
        # Start from title position and add spacing
        title_clearance = title_height * 0.5
        title_y = y + height + title_clearance
        
        # Position scale text above title with appropriate spacing
        text_spacing = max(title_height * 0.4, text_height * 0.4)  # Dynamic spacing
        scale_y = title_y + title_total_height + text_spacing
        
        self.msp.add_text(
            scale_text,
            dxfattribs={
                'layer': self.layer_name,
                'height': text_height,
                'halign': 0,  # Left aligned from insertion point
                'valign': 0,  # Bottom aligned from insertion point
                'insert': (scale_x, scale_y)
            }
        )

    def _calculate_text_dimensions(self, text: str, height: float) -> tuple[float, float]:
        """
        Calculate approximate text bounding box dimensions.
        
        Args:
            text: The text string
            height: Text height in drawing units
            
        Returns:
            Tuple of (width, height) including typical font spacing
        """
        # Approximate width calculation based on typical romans.shx font characteristics
        # Character width is approximately 60% of height for most characters
        char_width_ratio = 0.6
        estimated_width = len(text) * height * char_width_ratio
        
        # Account for typical ascender/descender space (about 20% above baseline)
        total_height = height * 1.2
        
        return estimated_width, total_height


# Utility functions for common paper sizes and scales
def calculate_drawing_space_size(
    paper_size: str,
    scale_factor: int,
    landscape: bool = True,
    margin_factor: float = 0.9
) -> Tuple[float, float]:
    """
    Calculate drawing space dimensions based on paper size and scale.

    Args:
        paper_size: Paper size code ('A0', 'A1')
        scale_factor: Scale factor (e.g., 50 for 1:50, 100 for 1:100)
        landscape: True for landscape orientation, False for portrait
        margin_factor: Factor to leave margin (0.9 = 10% margin)

    Returns:
        Tuple of (width, height) in real-world mm

    Examples:
        - A1 at 1:50 scale yields ~34,800mm x 28,200mm drawing area
        - A1 at 1:100 scale yields ~69,600mm x 56,400mm drawing area
    """
    # Get paper dimensions
    paper_width, paper_height = get_paper_size(paper_size, landscape)

    # Calculate usable area with margins
    usable_width = paper_width * margin_factor
    usable_height = paper_height * margin_factor

    # Calculate real-world dimensions based on scale
    real_width = usable_width * scale_factor
    real_height = usable_height * scale_factor

    return real_width, real_height


# Common paper sizes in mm (ISO A series)
PAPER_SIZES = {
    'A0': (841, 1189),  # Width = 841mm, Height = 1189mm
    'A1': (594, 841)    # Width = 594mm, Height = 841mm
}


def get_paper_size(size: str, landscape: bool = True) -> Tuple[float, float]:
    """
    Get paper dimensions for common sizes.

    Args:
        size: Paper size ('A0', 'A1', 'A2', 'A3', 'A4')
        landscape: True for landscape, False for portrait

    Returns:
        Tuple of (width, height) in mm
    """
    if size not in PAPER_SIZES:
        raise ValueError(f"Unknown paper size: {size}")

    width, height = PAPER_SIZES[size]

    if landscape:
        return max(width, height), min(width, height)
    else:
        return min(width, height), max(width, height)


def get_drawing_space_dimensions(
    paper_size: str,
    scale_factor: int,
    landscape: bool = True,
    margin_factor: float = 0.9
) -> Tuple[float, float]:
    """
    Get the appropriate drawing space dimensions for a given paper size and scale factor.

    This is a convenience function that combines get_paper_size and calculate_drawing_space_size.

    Args:
        paper_size: Paper size code ('A0', 'A1')
        scale_factor: Scale factor (e.g., 50 for 1:50, 100 for 1:100)
        landscape: True for landscape orientation, False for portrait
        margin_factor: Factor to leave margin (0.9 = 10% margin)

    Returns:
        Tuple of (width, height) in real-world mm

    Examples:
        - get_drawing_space_dimensions('A1', 50) → ~34,800 x 28,200 mm
        - get_drawing_space_dimensions('A1', 100) → ~69,600 x 56,400 mm
        - get_drawing_space_dimensions('A0', 50) → ~49,300 x 40,000 mm
        - get_drawing_space_dimensions('A0', 100) → ~98,600 x 80,000 mm
    """
    return calculate_drawing_space_size(paper_size, scale_factor, landscape, margin_factor)
