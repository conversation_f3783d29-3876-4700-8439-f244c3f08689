"""
Main Window
==========

Main GUI window for the General Notes Drawing Generator.

This module provides the primary user interface using tkinter,
including file selection, note checklist, and generation controls.

Author: Drawing Production System
"""

import logging
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from typing import Optional

from ..orchestrators.workflow_orchestrator import WorkflowOrchestrator

logger = logging.getLogger(__name__)


class MainWindow:
    """
    Main application window for the General Notes Drawing Generator.
    
    Provides a user-friendly interface for:
    - File selection (database and summary)
    - Note selection with checklist
    - Output path selection
    - Generation progress monitoring
    """
    
    def __init__(self, workflow_orchestrator: WorkflowOrchestrator):
        """
        Initialize the main window.
        
        Args:
            workflow_orchestrator: Workflow coordinator
        """
        self.workflow_orchestrator = workflow_orchestrator
        self.root: Optional[tk.Tk] = None
        self.result = False
        
        # File paths
        self.database_path = tk.StringVar()
        self.summary_path = tk.StringVar()
        self.output_path = tk.StringVar()
        
        # UI state
        self.notes_loaded = False
        
    def run(self) -> bool:
        """
        Run the main window and return result.
        
        Returns:
            True if generation was successful
        """
        try:
            self._create_window()
            self._create_widgets()
            self._setup_layout()
            self._setup_bindings()
            
            # Set default paths from configuration
            config = self.workflow_orchestrator.core.config
            self.database_path.set(config.default_database_path)
            self.summary_path.set(config.default_summary_path)
            
            # Run the main loop
            self.root.mainloop()
            
            return self.result
            
        except Exception as e:
            logger.error(f"Main window error: {str(e)}")
            messagebox.showerror("Error", f"Application error: {str(e)}")
            return False
        finally:
            if self.root:
                self.root.destroy()
    
    def _create_window(self) -> None:
        """Create the main window."""
        self.root = tk.Tk()
        self.root.title("General Notes Drawing Generator")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"800x600+{x}+{y}")
    
    def _create_widgets(self) -> None:
        """Create all widgets."""
        # Main frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # File selection frame
        self.file_frame = ttk.LabelFrame(self.main_frame, text="Input Files", padding="10")
        
        # Database file selection
        ttk.Label(self.file_frame, text="DXF Database:").grid(row=0, column=0, sticky="w", pady=2)
        self.database_entry = ttk.Entry(self.file_frame, textvariable=self.database_path, width=60)
        self.database_button = ttk.Button(self.file_frame, text="Browse...", command=self._browse_database)
        
        # Summary file selection
        ttk.Label(self.file_frame, text="Excel Summary:").grid(row=1, column=0, sticky="w", pady=2)
        self.summary_entry = ttk.Entry(self.file_frame, textvariable=self.summary_path, width=60)
        self.summary_button = ttk.Button(self.file_frame, text="Browse...", command=self._browse_summary)
        
        # Load files button
        self.load_button = ttk.Button(self.file_frame, text="Load Files", command=self._load_files)
        
        # Notes selection frame
        self.notes_frame = ttk.LabelFrame(self.main_frame, text="Note Selection", padding="10")
        
        # Notes listbox with scrollbar
        self.notes_listbox = tk.Listbox(self.notes_frame, selectmode=tk.MULTIPLE, height=15)
        self.notes_scrollbar = ttk.Scrollbar(self.notes_frame, orient="vertical", command=self.notes_listbox.yview)
        self.notes_listbox.configure(yscrollcommand=self.notes_scrollbar.set)
        
        # Selection buttons
        self.select_all_button = ttk.Button(self.notes_frame, text="Select All", command=self._select_all)
        self.select_none_button = ttk.Button(self.notes_frame, text="Select None", command=self._select_none)
        
        # Output frame
        self.output_frame = ttk.LabelFrame(self.main_frame, text="Output", padding="10")
        
        # Output file selection
        ttk.Label(self.output_frame, text="Output File:").grid(row=0, column=0, sticky="w", pady=2)
        self.output_entry = ttk.Entry(self.output_frame, textvariable=self.output_path, width=60)
        self.output_button = ttk.Button(self.output_frame, text="Browse...", command=self._browse_output)
        
        # Generate button
        self.generate_button = ttk.Button(self.output_frame, text="Generate Drawings", command=self._generate_drawings)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        self.status_bar = ttk.Label(self.main_frame, textvariable=self.status_var, relief="sunken")
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.main_frame, variable=self.progress_var, maximum=100)
    
    def _setup_layout(self) -> None:
        """Setup widget layout."""
        # Main frame
        self.main_frame.pack(fill="both", expand=True)
        
        # File frame layout
        self.file_frame.pack(fill="x", pady=(0, 10))
        
        self.database_entry.grid(row=0, column=1, sticky="ew", padx=(5, 5))
        self.database_button.grid(row=0, column=2, padx=(0, 0))
        
        self.summary_entry.grid(row=1, column=1, sticky="ew", padx=(5, 5))
        self.summary_button.grid(row=1, column=2, padx=(0, 0))
        
        self.load_button.grid(row=2, column=1, pady=(10, 0))
        
        self.file_frame.columnconfigure(1, weight=1)
        
        # Notes frame layout
        self.notes_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        self.notes_listbox.pack(side="left", fill="both", expand=True)
        self.notes_scrollbar.pack(side="right", fill="y")
        
        button_frame = ttk.Frame(self.notes_frame)
        button_frame.pack(side="bottom", fill="x", pady=(10, 0))
        
        self.select_all_button.pack(side="left", padx=(0, 5))
        self.select_none_button.pack(side="left")
        
        # Output frame layout
        self.output_frame.pack(fill="x", pady=(0, 10))
        
        self.output_entry.grid(row=0, column=1, sticky="ew", padx=(5, 5))
        self.output_button.grid(row=0, column=2, padx=(0, 0))
        
        self.generate_button.grid(row=1, column=1, pady=(10, 0))
        
        self.output_frame.columnconfigure(1, weight=1)
        
        # Status and progress
        self.status_bar.pack(fill="x", pady=(0, 5))
        self.progress_bar.pack(fill="x")
    
    def _setup_bindings(self) -> None:
        """Setup event bindings."""
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _browse_database(self) -> None:
        """Browse for DXF database file."""
        filename = filedialog.askopenfilename(
            title="Select DXF Database File",
            filetypes=[("DXF files", "*.dxf"), ("All files", "*.*")]
        )
        if filename:
            self.database_path.set(filename)
    
    def _browse_summary(self) -> None:
        """Browse for Excel summary file."""
        filename = filedialog.askopenfilename(
            title="Select Excel Summary File",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.summary_path.set(filename)
    
    def _browse_output(self) -> None:
        """Browse for output file location."""
        filename = filedialog.asksaveasfilename(
            title="Save Output DXF File",
            defaultextension=".dxf",
            filetypes=[("DXF files", "*.dxf"), ("All files", "*.*")]
        )
        if filename:
            self.output_path.set(filename)
    
    def _load_files(self) -> None:
        """Load input files and populate notes list."""
        try:
            self.status_var.set("Loading files...")
            self.root.update()
            
            database_path = self.database_path.get()
            summary_path = self.summary_path.get()
            
            if not database_path or not summary_path:
                messagebox.showerror("Error", "Please select both database and summary files")
                return
            
            # Load files using workflow orchestrator
            result = self.workflow_orchestrator.load_files_step(database_path, summary_path)
            
            if result['status'] == 'success':
                # Populate notes list
                available_notes = self.workflow_orchestrator.core.get_available_notes()
                self._populate_notes_list(available_notes)
                
                self.notes_loaded = True
                self.status_var.set(f"Loaded {len(available_notes)} notes")
                messagebox.showinfo("Success", f"Successfully loaded {len(available_notes)} note details")
            else:
                messagebox.showerror("Error", f"Failed to load files: {result.get('error', 'Unknown error')}")
                self.status_var.set("Error loading files")
                
        except Exception as e:
            logger.error(f"File loading error: {str(e)}")
            messagebox.showerror("Error", f"Error loading files: {str(e)}")
            self.status_var.set("Error")
    
    def _populate_notes_list(self, notes: list) -> None:
        """Populate the notes listbox."""
        self.notes_listbox.delete(0, tk.END)
        for note in notes:
            self.notes_listbox.insert(tk.END, note)
    
    def _select_all(self) -> None:
        """Select all notes in the list."""
        self.notes_listbox.select_set(0, tk.END)
    
    def _select_none(self) -> None:
        """Deselect all notes in the list."""
        self.notes_listbox.selection_clear(0, tk.END)
    
    def _generate_drawings(self) -> None:
        """Generate drawings from selected notes."""
        try:
            if not self.notes_loaded:
                messagebox.showerror("Error", "Please load files first")
                return
            
            # Get selected notes
            selected_indices = self.notes_listbox.curselection()
            if not selected_indices:
                messagebox.showerror("Error", "Please select at least one note")
                return
            
            selected_notes = [self.notes_listbox.get(i) for i in selected_indices]
            output_path = self.output_path.get()
            
            if not output_path:
                messagebox.showerror("Error", "Please specify output file path")
                return
            
            self.status_var.set("Generating drawings...")
            self.progress_var.set(0)
            self.root.update()
            
            # Process selection
            selection_result = self.workflow_orchestrator.selection_step(selected_notes)
            if selection_result['status'] != 'success':
                raise Exception(selection_result.get('error', 'Selection failed'))
            
            self.progress_var.set(50)
            self.root.update()
            
            # Generate output
            generation_result = self.workflow_orchestrator.generation_step(output_path)
            if generation_result['status'] == 'success':
                self.progress_var.set(100)
                self.status_var.set("Generation completed")
                messagebox.showinfo("Success", f"Drawings generated successfully!\nOutput: {output_path}")
                self.result = True
            else:
                raise Exception(generation_result.get('error', 'Generation failed'))
                
        except Exception as e:
            logger.error(f"Generation error: {str(e)}")
            messagebox.showerror("Error", f"Error generating drawings: {str(e)}")
            self.status_var.set("Error")
            self.progress_var.set(0)
    
    def _on_closing(self) -> None:
        """Handle window closing."""
        self.root.quit()
