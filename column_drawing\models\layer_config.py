"""
Layer Configuration for Structural Engineering Drawings
======================================================

Implements AIA (American Institute of Architects) layer naming standards
for structural building engineering projects, specifically for reinforced
concrete column details.

Layer Naming Convention: [Discipline]-[Major Group]-[Minor Group]-[Status]
Example: AIS291__-EXIST for existing structural concrete reinforcement
"""

from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
from ezdxf import colors
from .drawing_config import BaseDrawingConfig


class LayerStatus(Enum):
    """Layer status suffixes following AIA standards."""
    EXISTING = "EXIST"      # Existing elements to remain
    NEW = "NEW"             # New construction elements
    DEMOLITION = "DEMO"     # Elements to be demolished
    TEMPORARY = "TEMP"      # Temporary construction elements
    FUTURE = "FUTR"         # Future construction elements
    NONE = ""               # No status (default)


class LineType(Enum):
    """Standard AutoCAD line types."""
    CONTINUOUS = "CONTINUOUS"
    DASHED = "DASHED"
    DASHDOT = "DASHDOT"
    DOTTED = "DOTTED"
    HIDDEN = "HIDDEN"
    CENTER = "CENTER"


@dataclass
class LayerProperties:
    """
    Properties for a DXF layer following AIA standards.

    Attributes:
        name: Layer name following AIA convention
        color: AutoCAD color index or RGB value
        lineweight: Line weight in mm (0.09, 0.13, 0.18, 0.25, 0.35, 0.50, 0.70, 1.00)
        linetype: Line type pattern
        description: Human-readable description
        plottable: Whether layer should plot
        frozen: Whether layer is frozen
        locked: Whether layer is locked
        on: Whether layer is visible
    """
    name: str
    color: int
    lineweight: float
    linetype: str = LineType.CONTINUOUS.value
    description: str = ""
    plottable: bool = True
    frozen: bool = False
    locked: bool = False
    on: bool = True


class StructuralLayerConfig(BaseDrawingConfig):
    """
    Configuration class for structural engineering drawing layers.

    Implements AIA layer naming standards for reinforced concrete structures
    with appropriate colors, line weights, and properties following
    professional CAD standards.

    Inherits common colors and line weights from BaseDrawingConfig.
    """

    # AIA Standard Line Weights (in mm) - specific to layer management
    LINEWEIGHT_HEAVY_MM = 0.70      # Major structural elements, cut lines
    LINEWEIGHT_MEDIUM_MM = 0.35     # Secondary elements, outlines
    LINEWEIGHT_STANDARD_MM = 0.18   # General drawing elements
    LINEWEIGHT_LIGHT_MM = 0.13      # Reinforcement, details
    LINEWEIGHT_FINE_MM = 0.09       # Dimensions, text, annotations

    def __init__(self):
        """Initialize the structural layer configuration."""
        self._layers = self._create_standard_layers()

    def _create_standard_layers(self) -> Dict[str, LayerProperties]:
        """
        Create the standard layer definitions for structural concrete drawings.

        Returns:
            Dict[str, LayerProperties]: Dictionary of layer configurations
        """
        layers = {}

        # ==================== STRUCTURAL CONCRETE LAYERS ====================

        # Concrete outline/formwork
        layers["AIS210__"] = LayerProperties(
            name="AIS210__",
            color=self.COLOR_WHITE,
            lineweight=self.LINEWEIGHT_STANDARD_MM,
            linetype=LineType.CONTINUOUS.value,
            description="Concrete outline and formwork elements"
        )

        # Reinforcement bars and stirrups (combined into AIS291__)
        layers["AIS291__"] = LayerProperties(
            name="AIS291__",
            color=self.COLOR_WHITE,
            lineweight=self.LINEWEIGHT_LIGHT_MM,
            linetype=LineType.CONTINUOUS.value,
            description="Reinforcement bars, stirrups, ties, and links"
        )

        # ==================== DIMENSION LAYERS ====================

        # Dimensions and measurements
        layers["AIS030__"] = LayerProperties(
            name="AIS030__",
            color=self.COLOR_WHITE,
            lineweight=self.LINEWEIGHT_FINE_MM,
            linetype=LineType.CONTINUOUS.value,
            description="Dimensions and measurements"
        )

        # ==================== TABLE AND SCHEDULE LAYERS ====================

        # Table structure and borders
        layers["AIS052__"] = LayerProperties(
            name="AIS052__",
            color=self.COLOR_WHITE,
            lineweight=self.LINEWEIGHT_MEDIUM_MM,
            linetype=LineType.CONTINUOUS.value,
            description="Table borders and structure"
        )

        # Table text, data, and headers (combined into AIS042__)
        layers["AIS042__"] = LayerProperties(
            name="AIS042__",
            color=self.COLOR_WHITE,
            lineweight=self.LINEWEIGHT_FINE_MM,
            linetype=LineType.CONTINUOUS.value,
            description="Table text, data, and headers"
        )

        # ==================== LEVEL AND ELEVATION LAYERS ====================

        # Level indicators, floor markers, and elevation elements
        layers["AIS032__"] = LayerProperties(
            name="AIS032__",
            color=self.COLOR_WHITE,
            lineweight=self.LINEWEIGHT_FINE_MM,
            linetype=LineType.CONTINUOUS.value,
            description="Level indicators, floor markers, triangular symbols, and elevation elements"
        )

        # ==================== DRAWING SPACE LAYERS ====================

        # Drawing space boundaries and organization
        layers["AIS070__"] = LayerProperties(
            name="AIS070__",
            color=self.COLOR_WHITE,
            lineweight=self.LINEWEIGHT_LIGHT_MM,
            linetype=LineType.CONTINUOUS.value,
            description="Drawing space boundaries, frames, and organization elements"
        )

        return layers

    def get_layer(self, layer_name: str) -> Optional[LayerProperties]:
        """
        Get layer properties by name.

        Args:
            layer_name: Name of the layer

        Returns:
            LayerProperties: Layer configuration or None if not found
        """
        return self._layers.get(layer_name)

    def get_all_layers(self) -> Dict[str, LayerProperties]:
        """
        Get all layer configurations.

        Returns:
            Dict[str, LayerProperties]: All layer configurations
        """
        return self._layers.copy()

    def get_layers_by_discipline(self, discipline: str = "S") -> Dict[str, LayerProperties]:
        """
        Get all layers for a specific discipline.

        Args:
            discipline: Discipline code (default: "S" for Structural)

        Returns:
            Dict[str, LayerProperties]: Layers for the discipline
        """
        prefix = f"{discipline}-"
        return {name: props for name, props in self._layers.items()
                if name.startswith(prefix)}

    def get_layers_by_major_group(self, major_group: str) -> Dict[str, LayerProperties]:
        """
        Get all layers for a specific major group.

        Args:
            major_group: Major group code (e.g., "CONC", "ANNO", "GRID")

        Returns:
            Dict[str, LayerProperties]: Layers for the major group
        """
        pattern = f"-{major_group}-"
        return {name: props for name, props in self._layers.items()
                if pattern in name}

    def add_custom_layer(self, layer_props: LayerProperties) -> None:
        """
        Add a custom layer to the configuration.

        Args:
            layer_props: Layer properties to add
        """
        self._layers[layer_props.name] = layer_props

    def create_status_variant(self, base_layer: str, status: LayerStatus) -> LayerProperties:
        """
        Create a status variant of an existing layer.

        Args:
            base_layer: Base layer name
            status: Status suffix to add

        Returns:
            LayerProperties: New layer with status suffix

        Raises:
            ValueError: If base layer doesn't exist
        """
        if base_layer not in self._layers:
            raise ValueError(f"Base layer '{base_layer}' not found")

        base_props = self._layers[base_layer]

        # Create new layer name with status
        if status == LayerStatus.NONE:
            new_name = base_layer
        else:
            new_name = f"{base_layer}-{status.value}"

        # Modify color for status variants
        status_color = base_props.color
        if status == LayerStatus.EXISTING:
            status_color = self.COLOR_GREEN
        elif status == LayerStatus.DEMOLITION:
            status_color = self.COLOR_YELLOW
        elif status == LayerStatus.TEMPORARY:
            status_color = self.COLOR_CYAN
        elif status == LayerStatus.FUTURE:
            status_color = self.COLOR_LIGHT_GRAY

        return LayerProperties(
            name=new_name,
            color=status_color,
            lineweight=base_props.lineweight,
            linetype=base_props.linetype,
            description=f"{base_props.description} ({status.value.lower()})",
            plottable=base_props.plottable,
            frozen=base_props.frozen,
            locked=base_props.locked,
            on=base_props.on
        )

    def get_layer_mapping(self) -> Dict[str, str]:
        """
        Get a mapping of drawing element types to layer names.

        Returns:
            Dict[str, str]: Mapping of element types to layer names
        """
        return {
            # Structural elements
            "column_outline": "AIS210__",
            "concrete_outline": "AIS210__",
            "formwork": "AIS210__",

            # Reinforcement (combined with stirrups and ties)
            "rebar_layer1": "AIS291__",
            "rebar_layer2": "AIS291__",
            "reinforcement": "AIS291__",
            "links": "AIS291__",
            "stirrups": "AIS291__",
            "ties": "AIS291__",

            # Dimensions
            "dimensions": "AIS030__",
            "dimension_text": "AIS030__",
            "dimension_lines": "AIS030__",

            # Table elements
            "table_border": "AIS052__",
            "table_lines": "AIS052__",
            "table_text": "AIS042__",
            "table_data": "AIS042__",
            "table_headers": "AIS042__",
            "column_mark": "AIS042__",

            # Level-related elements (all assigned to AIS032__)
            "level_triangle": "AIS032__",
            "level_marker": "AIS032__",
            "floor_marker": "AIS032__",
            "triangular_marker": "AIS032__",
            "level_text": "AIS032__",
            "level_number": "AIS032__",
            "level_value": "AIS032__",
            "floor_text": "AIS032__",
            "floor_description": "AIS032__",
            "level_description": "AIS032__",
            "level_line": "AIS032__",
            "floor_line": "AIS032__",
            "level_indicator": "AIS032__",
            "elevation_level": "AIS032__",
            "elevation_text": "AIS032__",
            "elevation_marker": "AIS032__",
            "elevation_triangle": "AIS032__",
            "floor_level_info": "AIS032__"
        }

    def validate_layer_name(self, layer_name: str) -> bool:
        """
        Validate a layer name against AIA standards.

        Args:
            layer_name: Layer name to validate

        Returns:
            bool: True if valid, False otherwise
        """
        parts = layer_name.split("-")

        # Must have at least 3 parts: Discipline-Major-Minor
        if len(parts) < 3:
            return False

        # Discipline should be 1-2 characters
        if len(parts[0]) < 1 or len(parts[0]) > 2:
            return False

        # Major and minor groups should be 4 characters
        if len(parts[1]) != 4 or len(parts[2]) != 4:
            return False

        # Optional status should be valid
        if len(parts) > 3:
            status_part = parts[3]
            valid_statuses = [
                status.value for status in LayerStatus if status.value]
            if status_part not in valid_statuses:
                return False

        return True

    def get_layer_statistics(self) -> Dict[str, int]:
        """
        Get statistics about the layer configuration.

        Returns:
            Dict[str, int]: Statistics about layers
        """
        stats = {
            "total_layers": len(self._layers),
            "disciplines": len(set(name.split("-")[0] for name in self._layers.keys())),
            "major_groups": len(set(name.split("-")[1] for name in self._layers.keys() if len(name.split("-")) > 1)),
            "color_variants": len(set(props.color for props in self._layers.values())),
            "lineweight_variants": len(set(props.lineweight for props in self._layers.values())),
        }

        return stats
