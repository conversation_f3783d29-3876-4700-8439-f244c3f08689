"""
Link and Stirrup Drawing Module
==============================

Specialized module for drawing BS8666 compliant reinforcement links and stirrups.
Handles Shape Code 52 (closed rectangular links) and Shape Code 25a (specialized links)
with proper AIA-standard layer management and positioning algorithms.
"""

import logging
from typing import TYPE_CHECKING, List, Tuple, Optional

if TYPE_CHECKING:
    from ezdxf.layouts import Modelspace
    from ...models.drawing_config import DrawingConfig
    from ...io.dxf_writer import DXFWriter
    from ...calculators.rebar_calculator import RebarCalculator
    from ...drawers.rebar.link_drawer import LinkDrawer

logger = logging.getLogger(__name__)


class LinkStirrupDrawer:
    """
    Specialized drawer for BS8666 compliant reinforcement links and stirrups.

    This class handles drawing links/stirrups with:
    - BS8666 Shape Code 52 (closed rectangular links)
    - BS8666 Shape Code 25a (specialized intermediate links)
    - Proper AIA layer management (AIS291__)
    - Corner rebar position calculations
    - Intermediate leg positioning algorithms

    Follows single responsibility principle by focusing solely on link/stirrup drawing.
    """

    def __init__(
        self,
        msp: 'Modelspace',
        dxf_writer: 'DXFWriter',
        config: 'DrawingConfig',
        rebar_calc: 'RebarCalculator',
        link_drawer: 'LinkDrawer'
    ):
        """
        Initialize the link stirrup drawer.

        Args:
            msp: DXF modelspace for drawing operations
            dxf_writer: DXF writer with layer management capabilities
            config: Drawing configuration for styling parameters
            rebar_calc: Rebar calculator for position calculations
            link_drawer: Specialized link drawer for BS8666 shapes
        """
        self.msp = msp
        self.dxf_writer = dxf_writer
        self.config = config
        self.rebar_calc = rebar_calc
        self.link_drawer = link_drawer

        logger.debug("Initialized LinkStirrupDrawer")

    def draw_links(
        self,
        layer1_x: float,
        layer1_y: float,
        layer1_width: float,
        layer1_height: float,
        num_legs_x: int,
        num_legs_y: int,
        color: int,
        layer1_radius: Optional[float] = None,
        link_radius: Optional[float] = None,
        actual_rebar_positions: Optional[List[Tuple[float, float]]] = None,
        rebar_diameter: Optional[float] = None,
        link_diameter: Optional[float] = None,
        use_25a_links: bool = True
    ) -> None:
        """
        Draw links/stirrups using BS8666 Shape Code 52 and optional Shape Code 25a.

        Creates a complete link system consisting of:
        - BS8666 Shape Code 52: Closed rectangular link around perimeter
        - BS8666 Shape Code 25a: Specialized intermediate links with arcs and extensions

        Shape Code 25a creates specialized links between rebar points with:
        - Arc 1: 135° counter-clockwise at start point with extension C
        - Arc 2: 90° clockwise at end point with extension A
        - Connecting straight line aligned with local gravity axis

        Args:
            layer1_x: Layer 1 rebar rectangle bottom-left corner X coordinate (mm)
            layer1_y: Layer 1 rebar rectangle bottom-left corner Y coordinate (mm)
            layer1_width: Layer 1 rebar rectangle width dimension (mm)
            layer1_height: Layer 1 rebar rectangle height dimension (mm)
            num_legs_x: Number of legs in X direction
            num_legs_y: Number of legs in Y direction
            color: Drawing color (AutoCAD color index)
            layer1_radius: Radius of layer 1 corner rebar for offset calculation (uses config default if None)
            link_radius: Radius of the link for corner filleting (uses config default if None)
            actual_rebar_positions: Actual positions of drawn rebars (for 25a alignment)
            rebar_diameter: Diameter of main rebar (from CSV data, for 25a links)
            link_diameter: Diameter of link (from CSV data, for 25a links)
            use_25a_links: Whether to use 25a links for intermediate legs (default: True)

        Raises:
            Exception: If drawing operation fails
        """
        try:
            logger.debug(
                f"Drawing links: num_legs_x={num_legs_x}, num_legs_y={num_legs_y}, "
                f"use_25a_links={use_25a_links}"
            )

            # Use configuration defaults if not provided
            if layer1_radius is None:
                layer1_radius = self.config.REBAR_DEFAULT_LAYER1_RADIUS
            if link_radius is None:
                link_radius = self.config.REBAR_DEFAULT_LINK_RADIUS

            # Calculate corner rebar positions from actual rebar data when available
            corner_rebars = self._calculate_corner_rebars(
                layer1_x, layer1_y, layer1_width, layer1_height, actual_rebar_positions
            )

            # Determine layer 1 positions for 25a alignment
            layer1_positions = self._get_layer1_positions(
                layer1_x, layer1_y, layer1_width, layer1_height,
                num_legs_x, num_legs_y, actual_rebar_positions
            )

            # Draw BS8666 Shape Code 52 - Closed rectangular link
            self._draw_outer_links(
                corner_rebars, rebar_diameter, link_diameter, layer1_radius, link_radius, color)

            # Draw intermediate legs if needed
            intermediate_legs_x, intermediate_legs_y = self._calculate_intermediate_legs(
                num_legs_x, num_legs_y)

            if intermediate_legs_x > 0 or intermediate_legs_y > 0:
                self._draw_intermediate_links(
                    corner_rebars, rebar_diameter, link_diameter, num_legs_x, num_legs_y,
                    layer1_positions, use_25a_links, color
                )
            else:
                logger.debug("No intermediate legs to draw")

        except Exception as e:
            logger.error(f"Error drawing links: {e}")
            raise

    def draw_multiple_link_systems(
        self,
        link_specs: List[dict]
    ) -> None:
        """
        Draw multiple link systems efficiently with consistent layer management.

        Batch drawing operation for multiple link systems using individual
        specifications for each system.

        Args:
            link_specs: List of dictionaries containing link specifications.
                       Each dict should contain the same parameters as draw_links method.

        Raises:
            Exception: If any drawing operation fails
        """
        try:
            for i, spec in enumerate(link_specs):
                self.draw_links(
                    layer1_x=spec['layer1_x'],
                    layer1_y=spec['layer1_y'],
                    layer1_width=spec['layer1_width'],
                    layer1_height=spec['layer1_height'],
                    num_legs_x=spec['num_legs_x'],
                    num_legs_y=spec['num_legs_y'],
                    color=spec['color'],
                    layer1_radius=spec.get('layer1_radius'),
                    link_radius=spec.get('link_radius'),
                    actual_rebar_positions=spec.get('actual_rebar_positions'),
                    rebar_diameter=spec.get('rebar_diameter'),
                    link_diameter=spec.get('link_diameter'),
                    use_25a_links=spec.get('use_25a_links', True)
                )

            logger.debug(f"Drew {len(link_specs)} link systems")

        except Exception as e:
            logger.error(f"Error drawing multiple link systems: {e}")
            raise

    def _calculate_corner_rebars(
        self,
        layer1_x: float,
        layer1_y: float,
        layer1_width: float,
        layer1_height: float,
        actual_rebar_positions: Optional[List[Tuple[float, float]]]
    ) -> List[Tuple[float, float]]:
        """
        Calculate corner rebar positions from actual rebar data or fallback to bounds.

        Args:
            layer1_x: Layer 1 X coordinate
            layer1_y: Layer 1 Y coordinate
            layer1_width: Layer 1 width
            layer1_height: Layer 1 height
            actual_rebar_positions: Actual rebar positions if available

        Returns:
            List[Tuple[float, float]]: Corner rebar positions [BL, BR, TR, TL]
        """
        if actual_rebar_positions and len(actual_rebar_positions) >= 4:
            # Use actual rebar positions to calculate corner rebars
            x_coords = [pos[0] for pos in actual_rebar_positions]
            y_coords = [pos[1] for pos in actual_rebar_positions]

            min_x = min(x_coords)
            max_x = max(x_coords)
            min_y = min(y_coords)
            max_y = max(y_coords)

            # Extract actual corner rebar positions
            corner_rebars = [
                (min_x, min_y),  # Bottom-left
                (max_x, min_y),  # Bottom-right
                (max_x, max_y),  # Top-right
                (min_x, max_y)   # Top-left
            ]
            logger.debug(
                f"Using actual rebar positions for corner rebars: {corner_rebars}")
        else:
            # Fallback to bounds-based calculation if no rebar positions available
            corner_rebars = [
                # Bottom-left
                (layer1_x, layer1_y),
                (layer1_x + layer1_width, layer1_y),                # Bottom-right
                (layer1_x + layer1_width, layer1_y + layer1_height),  # Top-right
                (layer1_x, layer1_y + layer1_height)                # Top-left
            ]
            logger.debug(
                f"Using bounds-based corner rebars as fallback: {corner_rebars}")

        return corner_rebars

    def _get_layer1_positions(
        self,
        layer1_x: float,
        layer1_y: float,
        layer1_width: float,
        layer1_height: float,
        num_legs_x: int,
        num_legs_y: int,
        actual_rebar_positions: Optional[List[Tuple[float, float]]]
    ) -> List[Tuple[float, float]]:
        """
        Get layer 1 positions for 25a alignment.

        Args:
            layer1_x: Layer 1 X coordinate
            layer1_y: Layer 1 Y coordinate
            layer1_width: Layer 1 width
            layer1_height: Layer 1 height
            num_legs_x: Number of legs in X direction
            num_legs_y: Number of legs in Y direction
            actual_rebar_positions: Actual rebar positions if available

        Returns:
            List[Tuple[float, float]]: Layer 1 positions for alignment
        """
        if actual_rebar_positions is not None:
            layer1_positions = actual_rebar_positions
            logger.debug(
                f"Using actual rebar positions for 25a alignment: {len(layer1_positions)} positions")
        else:
            # Fallback: Calculate positions based on link layout (legacy behavior)
            layer1_positions = self.rebar_calc.calculate_perimeter_positions(
                layer1_x, layer1_y, layer1_width, layer1_height, num_legs_x, num_legs_y
            )
            logger.debug(
                f"Using calculated positions for 25a alignment: {len(layer1_positions)} positions")

        return layer1_positions

    def _draw_outer_links(
        self,
        corner_rebars: List[Tuple[float, float]],
        rebar_diameter: Optional[float],
        link_diameter: Optional[float],
        layer1_radius: float,
        link_radius: float,
        color: int
    ) -> None:
        """
        Draw BS8666 Shape Code 52 outer links.

        Args:
            corner_rebars: Corner rebar positions
            rebar_diameter: Rebar diameter from CSV
            link_diameter: Link diameter from CSV
            layer1_radius: Layer 1 radius fallback
            link_radius: Link radius fallback
            color: Drawing color
        """
        self.link_drawer.draw_outer_links(
            corner_rebars=corner_rebars,
            rebar_diameter=rebar_diameter if rebar_diameter is not None else layer1_radius * 2,
            link_diameter=link_diameter if link_diameter is not None else link_radius * 2,
            color=color
        )

    def _calculate_intermediate_legs(self, num_legs_x: int, num_legs_y: int) -> Tuple[int, int]:
        """
        Calculate actual intermediate leg counts.

        Shape Code 52 provides 2 lines in each direction, so intermediate legs
        are the additional legs beyond the main rectangular frame.

        Args:
            num_legs_x: Total number of legs in X direction
            num_legs_y: Total number of legs in Y direction

        Returns:
            Tuple[int, int]: (intermediate_legs_x, intermediate_legs_y)
        """
        intermediate_legs_x = max(
            0, num_legs_x - 2)  # Subtract 2 vertical sides of main rectangle
        # Subtract 2 horizontal sides of main rectangle
        intermediate_legs_y = max(0, num_legs_y - 2)

        logger.debug(
            f"Calculated intermediate legs: intermediate_legs_x={intermediate_legs_x}, intermediate_legs_y={intermediate_legs_y}")
        return intermediate_legs_x, intermediate_legs_y

    def _draw_intermediate_links(
        self,
        corner_rebars: List[Tuple[float, float]],
        rebar_diameter: Optional[float],
        link_diameter: Optional[float],
        num_legs_x: int,
        num_legs_y: int,
        layer1_positions: List[Tuple[float, float]],
        use_25a_links: bool,
        color: int
    ) -> None:
        """
        Draw intermediate links using 25a approach if available.

        Args:
            corner_rebars: Corner rebar positions
            rebar_diameter: Rebar diameter from CSV
            link_diameter: Link diameter from CSV
            num_legs_x: Number of legs in X direction
            num_legs_y: Number of legs in Y direction
            layer1_positions: Layer 1 positions for alignment
            use_25a_links: Whether to use 25a links
            color: Drawing color
        """
        if use_25a_links and rebar_diameter is not None and link_diameter is not None:
            # Use new 25a link approach with CSV data via LinkDrawer
            logger.debug(
                f"Drawing 25a links for intermediate legs: rebar_diameter={rebar_diameter}, link_diameter={link_diameter}")
            self.link_drawer.draw_intermediate_links(
                corner_rebars=corner_rebars,
                rebar_diameter=rebar_diameter,
                link_diameter=link_diameter,
                num_legs_x=num_legs_x,
                num_legs_y=num_legs_y,
                color=color,
                layer1_positions=layer1_positions
            )
        else:
            # Skip drawing intermediate legs if CSV data not available
            logger.warning(
                f"Skipping intermediate legs - CSV data required for 25a links: rebar_diameter={rebar_diameter}, link_diameter={link_diameter}")
            logger.debug(
                f"To enable 25a links, ensure use_25a_links=True and provide rebar_diameter and link_diameter from CSV data")
