"""
Integration Tests
================

End-to-end integration tests for the General Notes Drawing Generator.

Author: Drawing Production System
"""

import unittest
import tempfile
import os
import pandas as pd
from unittest.mock import patch, Mock

from ..main import GeneralNotesGenerator, main
from ..models.configuration import GeneralNotesConfig
from ..core.application_core import ApplicationCore
from ..orchestrators.workflow_orchestrator import WorkflowOrchestrator


class TestMainApplicationIntegration(unittest.TestCase):
    """Test main application integration."""
    
    def setUp(self):
        """Set up test environment."""
        self.config = GeneralNotesConfig.create_development()
        self.generator = GeneralNotesGenerator(self.config)
    
    def test_generator_initialization(self):
        """Test GeneralNotesGenerator initialization."""
        self.assertIsNotNone(self.generator.core)
        self.assertIsNotNone(self.generator.config)
        self.assertIsNotNone(self.generator.workflow_orchestrator)
        self.assertIsNotNone(self.generator.session_manager)
    
    @patch('general_notes.main.check_user_license')
    @patch('general_notes.main.show_info_message')
    def test_cli_workflow_mock(self, mock_show_info, mock_check_license):
        """Test CLI workflow with mocked components."""
        # Mock license check
        mock_check_license.return_value = True
        
        # Create temporary files
        with tempfile.TemporaryDirectory() as tmp_dir:
            database_path = os.path.join(tmp_dir, "database.dxf")
            summary_path = os.path.join(tmp_dir, "summary.xlsx")
            output_path = os.path.join(tmp_dir, "output.dxf")
            
            # Create mock DXF file (just empty file for testing)
            with open(database_path, 'w') as f:
                f.write("mock dxf content")
            
            # Create mock Excel file
            test_data = {
                'Title': ['Test Note 1', 'Test Note 2'],
                'Point1': ['(0, 0, 0)', '(100, 0, 0)'],
                'Point2': ['(50, 0, 0)', '(150, 0, 0)'],
                'Point3': ['(50, 25, 0)', '(150, 25, 0)'],
                'Point4': ['(0, 25, 0)', '(100, 25, 0)']
            }
            df = pd.DataFrame(test_data)
            df.to_excel(summary_path, sheet_name='Detail', index=False)
            
            # Mock the workflow orchestrator methods
            with patch.object(self.generator.workflow_orchestrator, 'execute_workflow') as mock_execute:
                mock_execute.return_value = True
                
                # Test CLI generation
                result = self.generator.generate_drawings_cli(
                    database_path=database_path,
                    summary_path=summary_path,
                    output_path=output_path,
                    selected_notes=["Test Note 1"]
                )
                
                self.assertTrue(result)
                mock_execute.assert_called_once()
    
    def test_get_available_notes_empty(self):
        """Test getting available notes when none are loaded."""
        notes = self.generator.get_available_notes("nonexistent.dxf", "nonexistent.xlsx")
        self.assertEqual(len(notes), 0)


class TestWorkflowIntegration(unittest.TestCase):
    """Test complete workflow integration."""
    
    def setUp(self):
        """Set up test workflow."""
        self.config = GeneralNotesConfig.create_development()
        self.core = ApplicationCore(self.config)
        self.orchestrator = WorkflowOrchestrator(self.core)
    
    def test_workflow_state_transitions(self):
        """Test workflow state transitions."""
        # Initial state
        status = self.orchestrator.get_workflow_status()
        self.assertFalse(status['prerequisites']['all_prerequisites_met'])
        
        # Interactive workflow should provide next steps
        interactive_result = self.orchestrator.execute_interactive_workflow()
        self.assertEqual(interactive_result['status'], 'ready')
        self.assertIn('load_excel_file', interactive_result['next_steps'])
        self.assertIn('load_dxf_file', interactive_result['next_steps'])
    
    def test_workflow_error_handling(self):
        """Test workflow error handling."""
        # Try to execute workflow without files
        with self.assertRaises(Exception):
            self.orchestrator.execute_workflow(
                database_path="nonexistent.dxf",
                summary_path="nonexistent.xlsx",
                output_path="output.dxf"
            )


class TestAuthIntegration(unittest.TestCase):
    """Test integration with auth package."""
    
    @patch('general_notes.main.create_session_manager')
    @patch('general_notes.main.check_user_license')
    def test_auth_integration(self, mock_check_license, mock_session_manager):
        """Test integration with auth package."""
        # Mock auth components
        mock_session_manager.return_value = Mock()
        mock_check_license.return_value = True
        
        # Create generator
        config = GeneralNotesConfig.create_development()
        generator = GeneralNotesGenerator(config)
        
        # Verify auth components are initialized
        self.assertIsNotNone(generator.session_manager)
        mock_session_manager.assert_called_once()
    
    @patch('general_notes.main.check_user_license')
    @patch('general_notes.main.show_error_message')
    def test_license_validation_failure(self, mock_show_error, mock_check_license):
        """Test license validation failure handling."""
        # Mock license check failure
        mock_check_license.return_value = False
        
        # Test main function
        with patch('sys.exit') as mock_exit:
            main('gui')
            # Should not exit due to mocked components, but license check should be called
            mock_check_license.assert_called()


class TestConfigurationIntegration(unittest.TestCase):
    """Test configuration integration across components."""
    
    def test_configuration_propagation(self):
        """Test that configuration is properly propagated."""
        config = GeneralNotesConfig.create_development()
        config.debug_mode = True
        config.log_level = "DEBUG"
        
        # Create core with config
        core = ApplicationCore(config)
        
        # Verify config is accessible
        self.assertEqual(core.config.debug_mode, True)
        self.assertEqual(core.config.log_level, "DEBUG")
        
        # Verify config is in dependency container
        resolved_config = core.dependencies.resolve('config')
        self.assertIs(resolved_config, config)
    
    def test_default_configuration_values(self):
        """Test default configuration values."""
        config = GeneralNotesConfig.create_default()
        
        self.assertEqual(config.application_name, "General Notes Drawing Generator")
        self.assertEqual(config.version, "1.0.0")
        self.assertFalse(config.debug_mode)
        self.assertEqual(config.log_level, "INFO")
        
        # Test drawing space dimensions
        self.assertEqual(config.drawing_space_dimensions, (67600.0, 56400.0))
        self.assertEqual(config.module_dimensions, (16900.0, 14100.0))


class TestErrorHandlingIntegration(unittest.TestCase):
    """Test error handling across the application."""
    
    def setUp(self):
        """Set up test environment."""
        self.config = GeneralNotesConfig.create_development()
        self.generator = GeneralNotesGenerator(self.config)
    
    def test_file_not_found_handling(self):
        """Test handling of file not found errors."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            output_path = os.path.join(tmp_dir, "output.dxf")
            
            # Test with non-existent files
            result = self.generator.generate_drawings_cli(
                database_path="nonexistent.dxf",
                summary_path="nonexistent.xlsx",
                output_path=output_path
            )
            
            # Should return False due to file not found
            self.assertFalse(result)
    
    def test_invalid_data_handling(self):
        """Test handling of invalid data."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            # Create invalid Excel file
            summary_path = os.path.join(tmp_dir, "invalid.xlsx")
            invalid_data = pd.DataFrame({'InvalidColumn': ['data']})
            invalid_data.to_excel(summary_path, index=False)
            
            database_path = os.path.join(tmp_dir, "database.dxf")
            output_path = os.path.join(tmp_dir, "output.dxf")
            
            # Create empty DXF file
            with open(database_path, 'w') as f:
                f.write("")
            
            # Should handle invalid data gracefully
            result = self.generator.generate_drawings_cli(
                database_path=database_path,
                summary_path=summary_path,
                output_path=output_path
            )
            
            self.assertFalse(result)


if __name__ == '__main__':
    unittest.main()
