"""
Elevation Drawing Component (Refactored)
=======================================

Handles drawing of rebar elevation diagrams in the rightmost column of the drawing table.
This module creates elevation diagrams showing floor levels, beam soffits, and triangular markers.

Refactored for improved efficiency and maintainability:
- Modular component architecture
- Unified layer management system
- Continuous polyline rebar drawing (replacing fragmented segments)
- Consolidated duplicate functions
- Streamlined coordinate calculations
- BS8666 compliance maintained
"""

import logging
from typing import Optional, Dict, Any
from ..models.drawing_config import DrawingConfig
from ..models.zone_config import ZoneConfigSet
from ..models.column_config import ColumnConfig
from ..io.dxf_writer import DXFWriter
from .dimension_drawer import DimensionDrawer

# Import the rebar calculator for lap length calculation
from ..calculators.rebar_calculator import RebarCalculator

# Import DataFrame manager for centralized link mark management
from ..managers.dataframe_manager import get_global_dataframe_manager

# Import modular components
from .elevation import (
    RebarCoordinateCalculator,
    FloorLevelTextFormatter,
    LayerManager,
    RebarDrawingMixin,
    FloorLevelDrawingMixin,
    ZoneCalculationMixin,
    DimensionDrawingMixin,
    TextRenderingMixin
)

logger = logging.getLogger(__name__)


class ElevationDrawer(
    RebarDrawingMixin,
    FloorLevelDrawingMixin,
    ZoneCalculationMixin,
    DimensionDrawingMixin,
    TextRenderingMixin
):
    """
    Refactored elevation drawer with improved efficiency and maintainability.

    Key improvements:
    - Modular component architecture
    - Unified layer management
    - Continuous polyline rebar drawing
    - Consolidated duplicate functions
    - Streamlined coordinate calculations
    """

    def __init__(self, modelspace: Any, config: DrawingConfig,
                 dxf_writer: Optional[DXFWriter] = None, doc: Optional[Any] = None) -> None:
        """Initialize the elevation drawer with unified helper components."""
        self.msp = modelspace
        self.config = config
        self.dxf_writer = dxf_writer
        self.doc = doc

        # Initialize unified helper components
        self.coordinate_calculator = RebarCoordinateCalculator(config)
        self.text_formatter = FloorLevelTextFormatter()
        self.layer_manager = LayerManager(dxf_writer)

        # Initialize dimension drawer if document is available
        self.dimension_drawer = None
        if doc is not None:
            self.dimension_drawer = DimensionDrawer(
                doc, modelspace, config, dxf_writer)

        # Initialize rebar calculator for lap length calculations
        self.rebar_calculator = RebarCalculator(config)

        # Data storage for zone names and link marks (for later recall and consistency)
        self.zone_data_cache: Dict[str, Dict[str, Any]] = {}
        self.calculated_zone_info: Dict[str, Dict[str, Any]] = {}
        # {column_name: {zone_id: mark}}
        self.column_zone_marks: Dict[str, Dict[str, str]] = {}

        # Get DataFrame manager for centralized link mark management
        self.dataframe_manager = get_global_dataframe_manager()

        logger.debug(
            "ElevationDrawer initialized with modular components and DataFrame manager")

    def draw_elevation_diagrams(self, table_x: float, table_y: float,
                                column_config: ColumnConfig, zone_config_set: Optional[ZoneConfigSet] = None) -> None:
        """Draw elevation diagrams in both elevation cells."""
        try:
            # Store zone_config_set for use in zone data extraction
            self.current_zone_config_set = zone_config_set

            # COORDINATE SYSTEM FIX:
            # DrawingSpaceOrganizer passes table_y as bottom-left corner
            # But elevation drawing coordinates expect top-left corner
            # Convert: top-left Y = bottom-left Y + table height
            table_height = self.config.TABLE_HEIGHT
            table_top_left_y = table_y + table_height

            # Get cell positions for elevation column positioning
            cell_positions = self.config.get_cell_positions()

            # Calculate elevation column X coordinate
            elevation_column_x = table_x + \
                cell_positions['elevation_column_x']  # 4500
            upper_floor_cell_bottom_y = table_top_left_y - \
                self.config.HEADER_ROW_HEIGHT  # -1200 (relative to table top)

            # Draw both elevation cells using unified method
            self._draw_elevation_cell(
                elevation_column_x, table_top_left_y, upper_floor_cell_bottom_y,
                column_config, cell_type="upper_floor"
            )

            self._draw_elevation_cell(
                elevation_column_x, upper_floor_cell_bottom_y, table_top_left_y,
                column_config, cell_type="current_floor"
            )

            logger.debug(f"Drew elevation diagrams for {column_config.name}")

        except Exception as e:
            logger.error(
                f"Error drawing elevation diagrams for {column_config.name}: {e}")
            raise

    def _draw_elevation_cell(self, elevation_column_x: float, cell_top_y: float, table_top_left_y: float,
                             column_config: ColumnConfig, cell_type: str) -> None:
        """
        Unified method for drawing elevation cells (upper_floor or current_floor).

        Args:
            elevation_column_x: X coordinate of elevation column
            cell_top_y: Top Y coordinate of the cell
            table_top_left_y: Table top-left Y coordinate (corrected coordinate system)
            column_config: Column configuration
            cell_type: "upper_floor" or "current_floor"
        """
        try:
            cell_width = self.config.ELEVATION_COLUMN_WIDTH

            if cell_type == "upper_floor":
                # Draw ELEVATION_UPPER_FLOOR cell (End Floor Information)
                # Use actual table coordinates - cell_top_y is the top of the upper cell
                # The upper cell extends from cell_top_y to cell_top_y - upper_elev_cell_height
                upper_elev_cell_height = self.config.UPPER_ELEVATION_CELL_HEIGHT  # 1200mm

                # Floor level info positioned at bottom of upper cell (not using negative coordinates)
                floor_level_y = cell_top_y - upper_elev_cell_height  # Bottom of upper cell
                self._draw_floor_level_info(
                    elevation_column_x, cell_width, floor_level_y,
                    column_config.end_floor_level, column_config.end_floor_name
                )

                # Vertical rebar for upper floor cell
                self._draw_upper_floor_vertical_rebar(
                    elevation_column_x, cell_width, upper_elev_cell_height, column_config, cell_top_y
                )

            elif cell_type == "current_floor":
                # Draw ELEVATION_CURRENT_FLOOR cell (Start Floor Information)
                self._draw_current_floor_content(
                    elevation_column_x, cell_width, cell_top_y, table_top_left_y, column_config
                )

            logger.debug(f"Drew {cell_type} elevation cell")

        except Exception as e:
            logger.error(f"Error drawing {cell_type} elevation cell: {e}")
            raise

    def _draw_current_floor_content(self, elevation_column_x: float, cell_width: float,
                                    cell_top_y: float, table_top_left_y: float, column_config: ColumnConfig) -> None:
        """Draw complete current floor elevation cell content."""
        try:
            # Calculate table boundaries using corrected top-left coordinate
            table_bottom_y = table_top_left_y - self.config.TABLE_HEIGHT

            current_floor_cell_height = self.config.TABLE_HEIGHT - self.config.HEADER_ROW_HEIGHT

            end_floor_y = table_bottom_y + self.config.TABLE_HEIGHT - \
                self.config.HEADER_ROW_HEIGHT
            zone_d_cell_bottom_y = end_floor_y - current_floor_cell_height/4
            zone_c_cell_bottom_y = end_floor_y - current_floor_cell_height/4*2
            zone_b_cell_bottom_y = end_floor_y - current_floor_cell_height/4*3
            start_floor_y = end_floor_y - current_floor_cell_height

            # Get floor data
            start_floor = column_config.start_floor_name
            start_floor_level = column_config.start_floor_level
            lowest_beam_soffit = column_config.lowest_beam_soffit

            # Draw floor level triangle and text at bottom
            triangle_x = elevation_column_x + \
                (cell_width * self.config.ELEVATION_TRIANGLE_X_POSITION_RATIO)
            triangle_y = table_bottom_y
            self._draw_triangular_floor_marker(triangle_x, triangle_y)

            # Add floor level text
            level_text = self.text_formatter.format_level_value(
                start_floor_level)
            level_y = triangle_y + self.config.ELEVATION_LEVEL_TEXT_Y_OFFSET
            self._add_engineering_text(
                level_text, triangle_x, level_y, self.config.ELEVATION_LEVEL_TEXT_HEIGHT, 'center', 'red', 'level_text')

            # Add descriptive text for start floor
            desc_line1, desc_line2 = self.text_formatter.get_floor_description_lines(
                start_floor)
            self._add_engineering_text(desc_line1, triangle_x, level_y + self.config.ELEVATION_DESC_LINE1_Y_OFFSET,
                                       self.config.ELEVATION_DESC_TEXT_HEIGHT, 'center', 'black', 'level_text')
            self._add_engineering_text(desc_line2, triangle_x, level_y + self.config.ELEVATION_DESC_LINE2_Y_OFFSET,
                                       self.config.ELEVATION_DESC_TEXT_HEIGHT, 'center', 'black', 'level_text')

            # Position lowest beam soffit elements at zone D cell bottom
            soffit_triangle_x = elevation_column_x + \
                (cell_width * self.config.ELEVATION_TRIANGLE_X_POSITION_RATIO)
            half_line_length = self.config.ELEVATION_HORIZONTAL_LINE_LENGTH / 2
            line_start_x = soffit_triangle_x - half_line_length
            line_end_x = soffit_triangle_x + half_line_length

            # Draw horizontal line at zone D cell bottom
            self._draw_horizontal_line(
                line_start_x, line_end_x, zone_d_cell_bottom_y)

            # Draw soffit triangle and text at zone D cell bottom
            self._draw_triangular_floor_marker(
                soffit_triangle_x, zone_d_cell_bottom_y)

            soffit_text = self.text_formatter.format_level_value(
                lowest_beam_soffit)
            soffit_y = zone_d_cell_bottom_y + self.config.ELEVATION_LEVEL_TEXT_Y_OFFSET
            self._add_engineering_text(soffit_text, soffit_triangle_x, soffit_y,
                                       self.config.ELEVATION_LEVEL_TEXT_HEIGHT, 'center', 'red', 'level_text')
            self._add_engineering_text("SOFFIT", soffit_triangle_x, soffit_y + self.config.ELEVATION_DESC_LINE2_Y_OFFSET,
                                       self.config.ELEVATION_DESC_TEXT_HEIGHT, 'center', 'black', 'level_text')
            self._add_engineering_text("BEAM", soffit_triangle_x, soffit_y + self.config.ELEVATION_DESC_LINE1_Y_OFFSET,
                                       self.config.ELEVATION_DESC_TEXT_HEIGHT, 'center', 'black', 'level_text')
            self._add_engineering_text("LOWEST", soffit_triangle_x, soffit_y + self.config.ELEVATION_DESC_LINE3_Y_OFFSET,
                                       self.config.ELEVATION_DESC_TEXT_HEIGHT, 'center', 'black', 'level_text')

            # Draw continuous rebar system with dimensioning
            self._draw_continuous_rebar_system(
                elevation_column_x, cell_width, table_bottom_y, cell_top_y, column_config
            )

            logger.debug(
                f"Drew current floor content: {start_floor} at {start_floor_level} mPD, soffit at zone D cell bottom Y={zone_d_cell_bottom_y}")

        except Exception as e:
            logger.error(f"Error drawing current floor content: {e}")
            raise
