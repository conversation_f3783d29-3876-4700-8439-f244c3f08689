"""
Session Manager Module
====================

This module provides session management functionality for the authentication system.
Handles session timeout, token management, and session state tracking.

Features:
    - Session timeout management
    - Secure session token generation
    - Session state tracking
    - Timer-based session monitoring

Author: Drawing Production System
Date: 2025
"""

import logging
import secrets
import time
from typing import Optional
from tkinter import messagebox

from .constants import SESSION_TOKEN_BYTES, LOGIN_TIMEOUT_MINUTES


class SessionManager:
    """
    Manages user sessions with timeout and token validation.
    
    Attributes:
        session_token (str): Current session security token
        start_time (float): Session start timestamp
        timeout_minutes (int): Session timeout in minutes
        is_active (bool): Session activity status
    """
    
    def __init__(self, timeout_minutes: int = LOGIN_TIMEOUT_MINUTES):
        """
        Initialize the session manager.
        
        Args:
            timeout_minutes: Session timeout in minutes
        """
        self.session_token = ""
        self.start_time = 0
        self.timeout_minutes = timeout_minutes
        self.is_active = False
        
    def start_session(self) -> str:
        """
        Start a new session with a secure token.
        
        Returns:
            The generated session token
        """
        self.session_token = secrets.token_hex(SESSION_TOKEN_BYTES)
        self.start_time = time.time()
        self.is_active = True
        
        logging.info("New session started")
        return self.session_token
        
    def end_session(self) -> None:
        """
        End the current session and clear token.
        """
        self.session_token = ""
        self.start_time = 0
        self.is_active = False
        
        logging.info("Session ended")
        
    def is_session_valid(self) -> bool:
        """
        Check if the current session is still valid.
        
        Returns:
            Boolean indicating if session is valid
        """
        if not self.is_active or not self.session_token:
            return False
            
        elapsed_time = time.time() - self.start_time
        return elapsed_time < (self.timeout_minutes * 60)
        
    def get_remaining_time(self) -> int:
        """
        Get remaining session time in seconds.
        
        Returns:
            Remaining time in seconds, or 0 if session is invalid
        """
        if not self.is_session_valid():
            return 0
            
        elapsed_time = time.time() - self.start_time
        remaining_time = (self.timeout_minutes * 60) - elapsed_time
        return max(0, int(remaining_time))
        
    def format_remaining_time(self) -> str:
        """
        Format remaining time as a readable string.
        
        Returns:
            Formatted time string (e.g., "14:32")
        """
        remaining_seconds = self.get_remaining_time()
        if remaining_seconds <= 0:
            return "00:00"
            
        minutes = remaining_seconds // 60
        seconds = remaining_seconds % 60
        return f"{minutes:02d}:{seconds:02d}"
        
    def check_timeout(self) -> bool:
        """
        Check if session has timed out and handle accordingly.
        
        Returns:
            Boolean indicating if session timed out
        """
        if not self.is_session_valid():
            if self.is_active:  # Was active but now timed out
                self.handle_timeout()
                return True
        return False
        
    def handle_timeout(self) -> None:
        """
        Handle session timeout by ending session and notifying user.
        """
        logging.warning("Session timeout occurred")
        self.end_session()
        
        try:
            messagebox.showwarning(
                "Session Timeout",
                f"Your session has expired after {self.timeout_minutes} minutes. "
                "Please log in again to continue."
            )
        except Exception as e:
            logging.error(f"Error showing timeout message: {e}")


def create_session_manager(timeout_minutes: Optional[int] = None) -> SessionManager:
    """
    Create a new session manager instance.
    
    Args:
        timeout_minutes: Optional custom timeout (defaults to LOGIN_TIMEOUT_MINUTES)
        
    Returns:
        SessionManager instance
    """
    if timeout_minutes is None:
        timeout_minutes = LOGIN_TIMEOUT_MINUTES
        
    return SessionManager(timeout_minutes)