"""
Email Logging Module for Column Drawing Application
==================================================

This module provides email logging functionality for the Column Drawing application,
including completion notifications and user activity tracking. It contains functions
that were moved from the auth module to be more closely integrated with the drawing
generation workflow.

Features:
- Drawing completion notifications
- Background email sending to prevent UI blocking
- Multiple SMTP method fallbacks for network resilience
- Comprehensive error handling and logging

Dependencies:
- smtplib: SMTP email sending
- ssl: Secure email connections
- threading: Background email operations
- logging: Error and status logging
- datetime: Timestamp generation

Authors: <AUTHORS>
Version: 3.0
Last Modified: 2025
"""

# Standard library imports
import logging
import smtplib
import ssl
import threading
from datetime import datetime
from email.message import EmailMessage
from typing import Optional, Callable

# Import version information
try:
    from version_config import APP_VERSION_V_PREFIX as APP_VERSION
except ImportError:
    APP_VERSION = "3.0"

# Email configuration constants
EMAIL_SENDER = '<EMAIL>'
# Consider using environment variables for production
EMAIL_PASSWORD = 'nunwcsgerkfetpii'


# ------ Email Sending Infrastructure ------

def _send_email_in_background(func: Callable, *args, **kwargs) -> None:
    """
    Execute an email sending function in the background to prevent UI blocking.

    Args:
        func: The email function to execute
        *args: Arguments to pass to the function
        **kwargs: Keyword arguments to pass to the function
    """
    def email_wrapper():
        try:
            success = func(*args, **kwargs)
            if success:
                logging.info(f"Background email sent successfully")
            else:
                logging.warning(
                    f"Background email sending failed - likely due to network restrictions")
                logging.info(
                    "Email service unavailable: This may be due to firewall/SMTP port blocking")
        except Exception as e:
            logging.error(f"Error in background email sending: {e}")
            logging.info(
                "Email service error: Network connectivity or configuration issue")

    # Create and start background thread
    email_thread = threading.Thread(
        target=email_wrapper,
        name="EmailSender",
        daemon=True
    )
    email_thread.start()


def _send_via_ssl_465(email_message: EmailMessage) -> None:
    """Send email using SSL on port 465 (original method)."""
    context = ssl.create_default_context()
    with smtplib.SMTP_SSL('smtp.gmail.com', 465, context=context, timeout=20) as smtp:
        smtp.login(EMAIL_SENDER, EMAIL_PASSWORD)
        smtp.sendmail(EMAIL_SENDER, EMAIL_SENDER, email_message.as_string())


def _send_via_starttls_587(email_message: EmailMessage) -> None:
    """Send email using STARTTLS on port 587 (alternative method)."""
    with smtplib.SMTP('smtp.gmail.com', 587, timeout=20) as smtp:
        smtp.starttls()
        smtp.login(EMAIL_SENDER, EMAIL_PASSWORD)
        smtp.sendmail(EMAIL_SENDER, EMAIL_SENDER, email_message.as_string())


# ------ Drawing Completion Logging Functions ------

def send_drawing_completion_log(user_name: str, drawings_count: int, csv_filename: str = "", user_email: Optional[str] = None) -> bool:
    """
    Log drawing generation completion via email for monitoring.

    This function sends a completion notification to EMAIL_SENDER when a user 
    successfully finishes generating column drawings, including the count of 
    details drawn.

    Args:
        user_name: Username who completed the drawing generation
        drawings_count: Number of column detail drawings successfully generated
        csv_filename: Name of the CSV file processed (optional)
        user_email: Optional email address (<NAME_EMAIL>)

    Returns:
        Boolean indicating success (True if queued successfully, even if actual sending might fail)
    """
    try:
        # Create a detailed completion message
        completion_case = f"Drawing Generation Completed - {drawings_count} details generated"
        if csv_filename:
            completion_case += f" (Source: {csv_filename})"

        # Send email in background using existing infrastructure
        _send_email_in_background(
            send_drawing_completion_log_sync,
            user_name,
            drawings_count,
            csv_filename,
            user_email
        )
        logging.debug(
            f"Drawing completion email queued: user={user_name}, count={drawings_count}")
        return True

    except Exception as e:
        logging.error(f"Failed to queue drawing completion email: {e}")
        logging.warning(
            "Drawing completion email logging is currently unavailable")
        return False


def send_drawing_completion_log_sync(user_name: str, drawings_count: int, csv_filename: str = "", user_email: Optional[str] = None) -> bool:
    """
    Synchronous drawing completion email sending function.

    This function sends detailed completion notifications to EMAIL_SENDER 
    with information about successful drawing generation.

    Args:
        user_name: Username who completed the drawing generation
        drawings_count: Number of column detail drawings successfully generated
        csv_filename: Name of the CSV file processed (optional)
        user_email: Optional email address (<NAME_EMAIL>)

    Returns:
        Boolean indicating success
    """
    email_receiver = EMAIL_SENDER  # Send completion logs to monitoring address
    subject = f'Drawing Production - Generation Completed ({drawings_count} drawings)'

    # Get current timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Use provided email or construct from username
    if user_email is None:
        user_email = f"{user_name}@asiainfrasolutions.com"

    # Create detailed completion report
    body = f"""
    DRAWING GENERATION COMPLETED

    Timestamp: {timestamp}
    Username: {user_name}
    Email: {user_email}
    Version: {APP_VERSION}
    
    Generation Results:
    - Number of Details Generated: {drawings_count}
    - Source CSV File: {csv_filename if csv_filename else 'Not specified'}
    - Status: Successfully Completed
    
    This notification confirms that the user has successfully completed 
    the column drawing generation process.
    """

    # Create email message
    em = EmailMessage()
    em['From'] = EMAIL_SENDER
    em['To'] = email_receiver
    em['Subject'] = subject
    em.set_content(body)

    # Try multiple SMTP methods in order of preference
    methods = [
        ("SMTP SSL (Port 465)", _send_via_ssl_465),
        ("SMTP STARTTLS (Port 587)", _send_via_starttls_587),
    ]

    for method_name, send_method in methods:
        try:
            logging.debug(f"Attempting completion email via {method_name}...")
            send_method(em)
            logging.info(
                f"Drawing completion email sent successfully for user: {user_name}, drawings: {drawings_count} via {method_name}")
            return True

        except (smtplib.SMTPAuthenticationError, smtplib.SMTPException) as smtp_error:
            logging.warning(
                f"{method_name} failed with SMTP error: {smtp_error}")
            continue

        except OSError as network_error:
            # Network connectivity issues (timeouts, connection refused, etc.)
            if "10060" in str(network_error) or "timeout" in str(network_error).lower():
                logging.warning(
                    f"{method_name} failed due to network timeout/blocking")
            else:
                logging.warning(
                    f"{method_name} failed with network error: {network_error}")
            continue

        except Exception as e:
            logging.warning(f"{method_name} failed with unexpected error: {e}")
            continue

    # All methods failed
    logging.error(
        f"All email sending methods failed for drawing completion: user={user_name}, drawings={drawings_count}")
    logging.info(
        "Drawing completion email failed - this may be due to network restrictions (firewall/SMTP blocking)")
    return False
