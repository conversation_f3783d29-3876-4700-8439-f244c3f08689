"""
General Notes Package Integration Verification
==============================================

Comprehensive verification script to test all aspects of the general_notes package
including integration with auth and drawing_spaces packages.

Author: Drawing Production System
"""

import sys
import logging
import tempfile
import os
from pathlib import Path
import pandas as pd

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_package_structure():
    """Test that all package components are properly structured."""
    print("Testing Package Structure...")
    
    try:
        # Test main package imports
        import general_notes
        from general_notes import GeneralNotesGenerator, main
        from general_notes.models import GeneralNotesConfig, NoteDetail, Boundary
        from general_notes.core import ApplicationCore
        from general_notes.orchestrators import WorkflowOrchestrator
        
        print("✓ All main components imported successfully")
        return True
        
    except Exception as e:
        print(f"✗ Package structure test failed: {str(e)}")
        return False

def test_configuration_system():
    """Test the configuration system."""
    print("Testing Configuration System...")
    
    try:
        from general_notes.models.configuration import GeneralNotesConfig
        
        # Test default configuration
        default_config = GeneralNotesConfig.create_default()
        assert default_config.application_name == "General Notes Drawing Generator"
        assert default_config.version == "1.0.0"
        assert not default_config.debug_mode
        
        # Test development configuration
        dev_config = GeneralNotesConfig.create_development()
        assert dev_config.debug_mode
        assert dev_config.log_level == "DEBUG"
        
        # Test configuration validation
        warnings = default_config.validate_configuration()
        # Should have warnings about missing files (expected)
        
        print("✓ Configuration system working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {str(e)}")
        return False

def test_data_models():
    """Test the data models."""
    print("Testing Data Models...")
    
    try:
        from general_notes.models.boundary import BoundaryPoint, Boundary
        from general_notes.models.note_detail import NoteDetail, NoteDetailCollection
        from general_notes.models.selection import SelectionState
        
        # Test boundary creation
        point1 = BoundaryPoint(0, 0, 0)
        point2 = BoundaryPoint(100, 0, 0)
        point3 = BoundaryPoint(100, 50, 0)
        point4 = BoundaryPoint(0, 50, 0)
        
        boundary = Boundary(point1, point2, point3, point4, title="Test Boundary")
        assert boundary.width == 100.0
        assert boundary.height == 50.0
        assert boundary.area == 5000.0
        
        # Test note detail creation
        note = NoteDetail(title="Test Note", boundary=boundary)
        assert note.title == "Test Note"
        assert note.area == 5000.0
        
        # Test collection
        collection = NoteDetailCollection([note])
        assert len(collection) == 1
        
        # Test selection state
        selection = SelectionState()
        selection.select_note("Test Note")
        assert selection.is_selected("Test Note")
        
        print("✓ Data models working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Data models test failed: {str(e)}")
        return False

def test_coordinate_parsing():
    """Test coordinate parsing functionality."""
    print("Testing Coordinate Parsing...")
    
    try:
        from general_notes.parsers.coordinate_parser import CoordinateParser
        
        parser = CoordinateParser()
        
        # Test various coordinate formats
        test_cases = [
            ("(100, 200, 0)", (100.0, 200.0, 0.0)),
            ("(100.5, -200.5, 0.0)", (100.5, -200.5, 0.0)),
            ("100, 200, 0", (100.0, 200.0, 0.0)),
        ]
        
        for coord_str, expected in test_cases:
            parsed = parser.parse_coordinate(coord_str)
            assert parsed.to_tuple() == expected
        
        print("✓ Coordinate parsing working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Coordinate parsing test failed: {str(e)}")
        return False

def test_excel_parsing():
    """Test Excel file parsing."""
    print("Testing Excel Parsing...")
    
    try:
        from general_notes.parsers.excel_parser import ExcelParser
        
        # Create test Excel data
        test_data = {
            'Title': ['Test Note 1', 'Test Note 2'],
            'Point1': ['(0, 0, 0)', '(100, 0, 0)'],
            'Point2': ['(50, 0, 0)', '(150, 0, 0)'],
            'Point3': ['(50, 25, 0)', '(150, 25, 0)'],
            'Point4': ['(0, 25, 0)', '(100, 25, 0)'],
            'Description': ['First test note', 'Second test note']
        }
        
        df = pd.DataFrame(test_data)
        
        # Create temporary Excel file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            df.to_excel(tmp_path, sheet_name='Detail', index=False)
            
            # Parse the file
            parser = ExcelParser()
            collection = parser.parse_excel_file(tmp_path)
            
            assert len(collection) == 2
            assert collection[0].title == 'Test Note 1'
            assert collection[0].description == 'First test note'
            
            print("✓ Excel parsing working correctly")
            return True
            
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
        
    except Exception as e:
        print(f"✗ Excel parsing test failed: {str(e)}")
        return False

def test_application_core():
    """Test the application core functionality."""
    print("Testing Application Core...")
    
    try:
        from general_notes.core.application_core import ApplicationCore
        from general_notes.models.configuration import GeneralNotesConfig
        
        config = GeneralNotesConfig.create_development()
        core = ApplicationCore(config)
        
        # Test initialization
        assert core.config is not None
        assert core.business_logic is not None
        assert core.dependencies is not None
        
        # Test workflow status
        status = core.get_workflow_status()
        assert 'prerequisites' in status
        assert 'note_collection' in status
        assert 'configuration' in status
        
        # Test system info
        info = core.get_system_info()
        assert info['application']['name'] == "General Notes Drawing Generator"
        
        print("✓ Application core working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Application core test failed: {str(e)}")
        return False

def test_workflow_orchestration():
    """Test workflow orchestration."""
    print("Testing Workflow Orchestration...")
    
    try:
        from general_notes.core.application_core import ApplicationCore
        from general_notes.orchestrators.workflow_orchestrator import WorkflowOrchestrator
        from general_notes.models.configuration import GeneralNotesConfig
        
        config = GeneralNotesConfig.create_development()
        core = ApplicationCore(config)
        orchestrator = WorkflowOrchestrator(core)
        
        # Test interactive workflow
        result = orchestrator.execute_interactive_workflow()
        assert result['status'] == 'ready'
        assert 'next_steps' in result
        
        # Test workflow status
        status = orchestrator.get_workflow_status()
        assert 'orchestrator' in status
        
        print("✓ Workflow orchestration working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Workflow orchestration test failed: {str(e)}")
        return False

def test_auth_integration():
    """Test integration with auth package."""
    print("Testing Auth Integration...")
    
    try:
        # Test that auth imports work
        from general_notes.main import GeneralNotesGenerator
        from general_notes.models.configuration import GeneralNotesConfig
        
        # This should work without errors (auth components are mocked in tests)
        config = GeneralNotesConfig.create_development()
        generator = GeneralNotesGenerator(config)
        
        # Verify session manager is initialized
        assert generator.session_manager is not None
        
        print("✓ Auth integration working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Auth integration test failed: {str(e)}")
        return False

def test_main_application():
    """Test the main application functionality."""
    print("Testing Main Application...")
    
    try:
        from general_notes import GeneralNotesGenerator
        from general_notes.models.configuration import GeneralNotesConfig
        
        config = GeneralNotesConfig.create_development()
        generator = GeneralNotesGenerator(config)
        
        # Test that all components are initialized
        assert generator.core is not None
        assert generator.workflow_orchestrator is not None
        
        # Test getting available notes (should return empty list initially)
        notes = generator.get_available_notes("nonexistent.dxf", "nonexistent.xlsx")
        assert isinstance(notes, list)
        
        print("✓ Main application working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Main application test failed: {str(e)}")
        return False

def main():
    """Run all integration verification tests."""
    print("General Notes Package Integration Verification")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        ("Package Structure", test_package_structure),
        ("Configuration System", test_configuration_system),
        ("Data Models", test_data_models),
        ("Coordinate Parsing", test_coordinate_parsing),
        ("Excel Parsing", test_excel_parsing),
        ("Application Core", test_application_core),
        ("Workflow Orchestration", test_workflow_orchestration),
        ("Auth Integration", test_auth_integration),
        ("Main Application", test_main_application)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"Integration Verification Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed! Package is ready for production use.")
        print("\nNext Steps:")
        print("1. Run the full test suite: python -m general_notes.tests.run_tests")
        print("2. Test with real data files")
        print("3. Deploy to production environment")
        return True
    else:
        print("❌ Some integration tests failed. Review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
