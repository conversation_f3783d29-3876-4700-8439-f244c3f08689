# Authentication Package

This package provides comprehensive authentication functionality for the Drawing Production System. It has been refactored from the previous monolithic structure to a modular, well-organized package.

## Package Structure

```
auth/
├── __init__.py              # Package initialization and exports
├── constants.py             # Authentication constants and configuration
├── email_service.py         # Email functionality for passwords and logging
├── password_service.py      # Password generation and validation
├── license_service.py       # User license validation
├── session_manager.py       # Session timeout and token management
├── ui_components.py         # UI elements and forms for authentication
├── login_frame.py           # Backward compatibility layer (DEPRECATED)
└── README.md               # This documentation file
```

## Module Overview

### constants.py
Contains all authentication-related constants:
- Security settings (max login attempts, timeouts, etc.)
- Email configuration
- UI constants (fonts, button sizes, etc.)
- Developer mode configuration

### email_service.py
Handles all email-related functionality:
- Password delivery via email
- Usage logging for monitoring
- Drawing completion notifications
- Multiple SMTP fallback methods
- Asynchronous email processing

### password_service.py
Manages password generation and validation:
- Secure random password generation
- Salt-based password hashing
- Password validation with timing attack resistance
- Secure password data cleanup

### license_service.py
Provides license validation functionality:
- Remote license validation against Google Drive
- Network timeout handling
- Graceful error handling for network issues

### session_manager.py
Manages user sessions:
- Session timeout management
- Secure session token generation
- Session state tracking
- Timer-based session monitoring

### ui_components.py
Contains UI-related functions:
- Login screen creation and layout
- Form validation and user interaction
- Message dialogs (error, info, warning)
- Keyboard event handling

### login_frame.py (DEPRECATED)
Backward compatibility layer that re-exports components from the new structure. This file will be removed in future versions.

## Usage Examples

### Basic Authentication Setup

```python
from auth import (
    send_password_email,
    check_user_license,
    generate_password_with_salt,
    validate_password,
    SessionManager
)

# Check user license
if check_user_license("john.doe"):
    print("User is licensed")

# Generate and send password
plain_password, salt, hash_value = generate_password_with_salt()
send_password_email("john.doe", plain_password)

# Validate password
if validate_password(user_input, salt, hash_value):
    print("Password is valid")

# Create session manager
session_manager = SessionManager()
session_token = session_manager.start_session()
```

### UI Components

```python
from auth import (
    create_login_screen,
    create_menu_bar,
    show_error_message,
    show_info_message
)

# Create login screen
create_login_screen(
    root_window,
    username_var,
    password_var,
    send_password_callback,
    login_callback,
    developer_callback
)

# Show messages
show_error_message("Error", "Invalid credentials")
show_info_message("Success", "Login successful")
```

### Session Management

```python
from auth import SessionManager

# Create session manager with custom timeout
session_manager = SessionManager(timeout_minutes=30)

# Start session
token = session_manager.start_session()

# Check session validity
if session_manager.is_session_valid():
    remaining = session_manager.get_remaining_time()
    print(f"Session expires in {remaining} seconds")

# End session
session_manager.end_session()
```

## Migration Guide

### From login_frame.py to auth package

**Old way:**
```python
from auth.login_frame import (
    send_password_email,
    check_user_license,
    generate_password_key
)
```

**New way:**
```python
from auth import (
    send_password_email,
    check_user_license,
    generate_password_key
)
```

### Application Class Integration

**Old way:**
```python
# Functions scattered across main.py and login_frame.py
def send_password(self):
    # Implementation mixed with UI code
    pass
```

**New way:**
```python
from auth import generate_password_with_salt, send_password_email

def send_password(self):
    # Clean separation of concerns
    plain_password, salt, hash_value = generate_password_with_salt()
    success = send_password_email(self.user_name, plain_password)
    if success:
        self.password_salt = salt
        self.password_hash = hash_value
```

## Benefits of the New Structure

1. **Separation of Concerns**: Each module has a single, well-defined responsibility
2. **Reduced Duplication**: Common functionality is centralized and reused
3. **Better Testing**: Smaller, focused modules are easier to test
4. **Improved Maintainability**: Changes to authentication logic are localized
5. **Type Safety**: Full type hints throughout the package
6. **Documentation**: Comprehensive docstrings for all functions and classes

## Security Features

- **Secure Password Generation**: Uses cryptographically secure random generation
- **Salt-based Hashing**: Protects against rainbow table attacks
- **Timing Attack Resistance**: Uses `secrets.compare_digest` for password validation
- **Session Management**: Automatic session timeout and token validation
- **License Validation**: Remote license checking with secure communication
- **Comprehensive Logging**: All authentication events are logged for security monitoring

## Future Enhancements

- Multi-factor authentication support
- OAuth integration
- Role-based access control
- Password complexity requirements
- Account lockout policies
- Audit trail improvements