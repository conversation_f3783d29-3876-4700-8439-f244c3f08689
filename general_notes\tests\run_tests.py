"""
Test Runner for General Notes Package
=====================================

Comprehensive test runner for all unit and integration tests.

Author: Drawing Production System
"""

import unittest
import sys
import os
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def discover_and_run_tests():
    """Discover and run all tests in the tests directory."""
    
    # Get the tests directory
    tests_dir = Path(__file__).parent
    
    # Create test loader
    loader = unittest.TestLoader()
    
    # Discover all tests
    suite = loader.discover(
        start_dir=str(tests_dir),
        pattern='test_*.py',
        top_level_dir=str(project_root)
    )
    
    # Create test runner
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        descriptions=True,
        failfast=False
    )
    
    # Run tests
    print("Running General Notes Package Tests")
    print("=" * 50)
    
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('Error:')[-1].strip()}")
    
    # Return success status
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n❌ Some tests failed. See details above.")
    
    return success

def run_specific_test_module(module_name):
    """Run tests from a specific module."""
    
    try:
        # Import the test module
        test_module = __import__(f'general_notes.tests.{module_name}', fromlist=[''])
        
        # Create test suite
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(test_module)
        
        # Run tests
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return len(result.failures) == 0 and len(result.errors) == 0
        
    except ImportError as e:
        print(f"Error importing test module '{module_name}': {e}")
        return False

def main():
    """Main test runner function."""
    
    if len(sys.argv) > 1:
        # Run specific test module
        module_name = sys.argv[1]
        if not module_name.startswith('test_'):
            module_name = f'test_{module_name}'
        
        print(f"Running tests from module: {module_name}")
        success = run_specific_test_module(module_name)
    else:
        # Run all tests
        success = discover_and_run_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
