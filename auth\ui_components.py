"""
UI Components Module
==================

This module provides UI-related functions for the authentication system.
Handles login screen creation, form management, and UI helper functions.

Features:
    - Login screen layout and widgets
    - Form validation and user interaction
    - UI element styling and positioning
    - Keyboard event handling

Author: Drawing Production System
Date: 2025
"""

import logging
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable

from .constants import (
    FONT_TITLE, FONT_SUBTITLE, FONT_NORMAL, FONT_SMALL,
    BUTTON_WIDTH_LARGE, BUTTON_HEIGHT_LARGE, BUTTON_WIDTH_MEDIUM, BUTTON_HEIGHT_MEDIUM,
    ENTRY_WIDTH
)


def create_login_screen(root: tk.Tk, username_entry_var: tk.StringVar, password_entry_var: tk.StringVar,
                       send_password_callback: Callable, login_callback: Callable,
                       developer_callback: Optional[Callable] = None) -> None:
    """
    Create and display the login screen UI.
    
    Args:
        root: Root window for the application
        username_entry_var: StringVar for username entry
        password_entry_var: StringVar for password entry
        send_password_callback: Function to call when sending password
        login_callback: Function to call when logging in
        developer_callback: Optional function for developer login
    """
    # Clear the window (except menu)
    for widget in root.winfo_children():
        if not isinstance(widget, tk.Menu):
            widget.destroy()
    
    # Create header
    header_frame = tk.Frame(root)
    header_frame.pack(fill="x", padx=20, pady=10)
    
    title_label = tk.Label(header_frame, text="Drafting Agent", font=FONT_TITLE)
    title_label.pack(anchor="w")
    
    separator = ttk.Separator(root, orient="horizontal")
    separator.pack(fill="x", padx=20, pady=5)
    
    # Create login form
    login_frame = tk.Frame(root)
    login_frame.pack(expand=True, fill="both", padx=40, pady=20)
    
    # Username entry
    tk.Label(login_frame, text="User Name (e.g. john.doe):", font=FONT_NORMAL).pack(anchor="w", pady=(10, 0))
    tk.Label(login_frame, text="User Name = <EMAIL>", font=FONT_SMALL).pack(anchor="w")
    
    username_entry = tk.Entry(login_frame, width=ENTRY_WIDTH, textvariable=username_entry_var)
    username_entry.pack(pady=(5, 20), fill="x")
    username_entry.focus()  # Set focus on username field
    
    # Get password button
    get_password_btn = tk.Button(login_frame, text="Send Password to Email",
                               command=send_password_callback, 
                               width=BUTTON_WIDTH_LARGE, 
                               height=BUTTON_HEIGHT_LARGE)
    get_password_btn.pack(pady=10)
    
    # Password entry
    tk.Label(login_frame, text="Enter the 30-unit password:", font=FONT_NORMAL).pack(anchor="w", pady=(10, 0))
    
    password_entry = tk.Entry(login_frame, width=ENTRY_WIDTH, show="*", textvariable=password_entry_var)
    password_entry.pack(pady=(5, 20), fill="x")
    
    # Bind Enter key to login
    password_entry.bind('<Return>', lambda event: login_callback())
    
    # Login button
    login_btn = tk.Button(login_frame, text="Login", 
                        command=login_callback, 
                        width=BUTTON_WIDTH_LARGE, 
                        height=BUTTON_HEIGHT_LARGE)
    login_btn.pack(pady=10)
    
    # Developer button (only shown if callback provided)
    if developer_callback:
        developer_btn = tk.Button(login_frame, text="Developer Login",
                                command=developer_callback,
                                width=BUTTON_WIDTH_MEDIUM,
                                height=BUTTON_HEIGHT_MEDIUM)
        developer_btn.pack(pady=5)


def create_timer_display(parent: tk.Widget, timer_text_var: tk.StringVar) -> tk.Label:
    """
    Create a timer display widget.
    
    Args:
        parent: Parent widget
        timer_text_var: StringVar for timer display
        
    Returns:
        Timer label widget
    """
    timer_label = tk.Label(parent, textvariable=timer_text_var, font=FONT_SMALL)
    timer_label.pack(side="right", padx=10)
    return timer_label


def create_menu_bar(root: tk.Tk, is_logged_in: bool = False, 
                   logout_callback: Optional[Callable] = None,
                   exit_callback: Optional[Callable] = None,
                   about_callback: Optional[Callable] = None) -> None:
    """
    Create the application menu bar.
    
    Args:
        root: Root window
        is_logged_in: Whether user is currently logged in
        logout_callback: Function to call for logout
        exit_callback: Function to call for exit
        about_callback: Function to call for about dialog
    """
    menubar = tk.Menu(root)
    root.config(menu=menubar)
    
    # File menu
    file_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="File", menu=file_menu)
    
    if is_logged_in and logout_callback:
        file_menu.add_command(label="Logout", command=logout_callback)
        file_menu.add_separator()
    
    if exit_callback:
        file_menu.add_command(label="Exit", command=exit_callback)
    
    # Help menu
    help_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="Help", menu=help_menu)
    
    if about_callback:
        help_menu.add_command(label="About", command=about_callback)


def show_error_message(title: str, message: str) -> None:
    """
    Show an error message dialog.
    
    Args:
        title: Dialog title
        message: Error message
    """
    try:
        messagebox.showerror(title, message)
    except Exception as e:
        logging.error(f"Error showing error message: {e}")


def show_info_message(title: str, message: str) -> None:
    """
    Show an info message dialog.
    
    Args:
        title: Dialog title
        message: Info message
    """
    try:
        messagebox.showinfo(title, message)
    except Exception as e:
        logging.error(f"Error showing info message: {e}")


def show_warning_message(title: str, message: str) -> None:
    """
    Show a warning message dialog.
    
    Args:
        title: Dialog title
        message: Warning message
    """
    try:
        messagebox.showwarning(title, message)
    except Exception as e:
        logging.error(f"Error showing warning message: {e}")


def validate_username_input(username: str) -> bool:
    """
    Validate username input.
    
    Args:
        username: Username to validate
        
    Returns:
        Boolean indicating if username is valid
    """
    return bool(username and username.strip())


def validate_password_input(password: str) -> bool:
    """
    Validate password input.
    
    Args:
        password: Password to validate
        
    Returns:
        Boolean indicating if password is valid
    """
    return bool(password and password.strip())


def requires_login_decorator(func: Callable) -> Callable:
    """
    Decorator to ensure user is logged in before accessing a function.
    
    Args:
        func: Function to protect with login requirement
        
    Returns:
        Wrapped function that checks login status
    """
    def wrapper(self, *args, **kwargs):
        if not getattr(self, 'login_status', False):
            show_error_message("Access Denied", "Please log in to access this feature.")
            return None
        return func(self, *args, **kwargs)
    return wrapper