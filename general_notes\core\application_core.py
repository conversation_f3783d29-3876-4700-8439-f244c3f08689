"""
Application Core
===============

Facade pattern implementation for the General Notes Drawing Generator core functionality.

This module provides the central coordination point for all application components,
following the facade pattern established in the column_drawing package.

The ApplicationCore class serves as the main entry point for:
- Component initialization and dependency injection
- Configuration management
- High-level operation coordination
- Error handling and logging

Author: Drawing Production System
"""

import logging
from typing import Optional, Dict, Any, List
from pathlib import Path

from ..models.configuration import GeneralNotesConfig
from ..models.note_detail import NoteDetailCollection
from ..models.selection import SelectionState
from ..parsers.excel_parser import ExcelParser
from ..parsers.dxf_parser import DXFParser, EntityCatalog
from .business_logic import BusinessLogic
from .dependency_injection import DependencyContainer
from .constants import APPLICATION_NAME, APPLICATION_VERSION

logger = logging.getLogger(__name__)


class ApplicationCore:
    """
    Core application facade providing centralized access to all functionality.
    
    This class follows the facade pattern to provide a simplified interface
    to the complex subsystem of parsers, processors, generators, and managers.
    
    Attributes:
        config: Application configuration
        business_logic: Business logic coordinator
        dependencies: Dependency injection container
        excel_parser: Excel file parser
        dxf_parser: DXF file parser
        selection_state: Current selection state
    """
    
    def __init__(self, config: Optional[GeneralNotesConfig] = None):
        """
        Initialize the application core.
        
        Args:
            config: Application configuration (uses default if None)
        """
        # Initialize configuration
        self.config = config or GeneralNotesConfig.create_default()
        
        # Initialize core components
        self.business_logic = BusinessLogic(self.config)
        self.dependencies = DependencyContainer(self.config)
        
        # Initialize parsers
        self.excel_parser = ExcelParser(
            sheet_name=self.config.get_ui_setting('excel_sheet_name', 'Detail'),
            strict_coordinates=self.config.get_ui_setting('strict_coordinates', False)
        )
        self.dxf_parser = DXFParser()
        
        # Initialize state management
        self.selection_state = SelectionState()
        
        # Cache for loaded data
        self._note_collection: Optional[NoteDetailCollection] = None
        self._entity_catalog: Optional[EntityCatalog] = None
        self._dxf_document = None
        
        logger.info(f"ApplicationCore initialized for {APPLICATION_NAME} v{APPLICATION_VERSION}")
    
    def load_note_collection(self, excel_path: str) -> NoteDetailCollection:
        """
        Load note collection from Excel file.
        
        Args:
            excel_path: Path to Excel summary file
            
        Returns:
            NoteDetailCollection with loaded note details
            
        Raises:
            Exception: If loading fails
        """
        try:
            logger.info(f"Loading note collection from: {excel_path}")
            
            # Validate business rules
            self.business_logic.validate_excel_file_path(excel_path)
            
            # Parse Excel file
            self._note_collection = self.excel_parser.parse_excel_file(excel_path)
            
            # Apply business logic validation
            self.business_logic.validate_note_collection(self._note_collection)
            
            logger.info(f"Successfully loaded {len(self._note_collection)} note details")
            return self._note_collection
            
        except Exception as e:
            logger.error(f"Failed to load note collection: {str(e)}")
            raise
    
    def load_dxf_database(self, dxf_path: str) -> None:
        """
        Load DXF database file.
        
        Args:
            dxf_path: Path to DXF database file
            
        Raises:
            Exception: If loading fails
        """
        try:
            logger.info(f"Loading DXF database from: {dxf_path}")
            
            # Validate business rules
            self.business_logic.validate_dxf_file_path(dxf_path)
            
            # Load DXF file
            self._dxf_document = self.dxf_parser.load_dxf_file(dxf_path)
            
            logger.info("Successfully loaded DXF database")
            
        except Exception as e:
            logger.error(f"Failed to load DXF database: {str(e)}")
            raise
    
    def catalog_entities(self) -> EntityCatalog:
        """
        Catalog entities from loaded DXF file by note boundaries.
        
        Returns:
            EntityCatalog with entities organized by note titles
            
        Raises:
            Exception: If cataloging fails or prerequisites not met
        """
        try:
            if not self._note_collection:
                raise ValueError("Note collection not loaded. Call load_note_collection() first.")
            
            if not self._dxf_document:
                raise ValueError("DXF document not loaded. Call load_dxf_database() first.")
            
            logger.info("Cataloging entities by note boundaries")
            
            # Catalog entities
            self._entity_catalog = self.dxf_parser.catalog_entities_by_notes(self._note_collection)
            
            # Apply business logic validation
            self.business_logic.validate_entity_catalog(self._entity_catalog)
            
            logger.info(f"Successfully cataloged entities for {len(self._entity_catalog.get_all_note_titles())} notes")
            return self._entity_catalog
            
        except Exception as e:
            logger.error(f"Failed to catalog entities: {str(e)}")
            raise
    
    def get_available_notes(self) -> List[str]:
        """
        Get list of available note titles.
        
        Returns:
            List of note titles
            
        Raises:
            Exception: If note collection not loaded
        """
        if not self._note_collection:
            raise ValueError("Note collection not loaded. Call load_note_collection() first.")
        
        return [note.title for note in self._note_collection]
    
    def get_note_collection(self) -> Optional[NoteDetailCollection]:
        """
        Get the loaded note collection.
        
        Returns:
            NoteDetailCollection or None if not loaded
        """
        return self._note_collection
    
    def get_entity_catalog(self) -> Optional[EntityCatalog]:
        """
        Get the entity catalog.
        
        Returns:
            EntityCatalog or None if not created
        """
        return self._entity_catalog
    
    def get_selection_state(self) -> SelectionState:
        """
        Get the current selection state.
        
        Returns:
            SelectionState object
        """
        return self.selection_state
    
    def validate_workflow_prerequisites(self) -> Dict[str, bool]:
        """
        Validate that all prerequisites for workflow execution are met.
        
        Returns:
            Dictionary with validation results for each prerequisite
        """
        prerequisites = {
            'note_collection_loaded': self._note_collection is not None,
            'dxf_document_loaded': self._dxf_document is not None,
            'entity_catalog_created': self._entity_catalog is not None,
            'has_selected_notes': len(self.selection_state.selected_titles) > 0
        }
        
        all_met = all(prerequisites.values())
        prerequisites['all_prerequisites_met'] = all_met
        
        return prerequisites
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """
        Get comprehensive status of the workflow.
        
        Returns:
            Dictionary with workflow status information
        """
        prerequisites = self.validate_workflow_prerequisites()
        
        status = {
            'prerequisites': prerequisites,
            'note_collection': {
                'loaded': self._note_collection is not None,
                'count': len(self._note_collection) if self._note_collection else 0
            },
            'dxf_database': {
                'loaded': self._dxf_document is not None,
                'metadata': self.dxf_parser.get_dxf_metadata() if self._dxf_document else None
            },
            'entity_catalog': {
                'created': self._entity_catalog is not None,
                'summary': self._entity_catalog.get_catalog_summary() if self._entity_catalog else None
            },
            'selection': self.selection_state.get_selection_summary(),
            'configuration': {
                'database_path': self.config.default_database_path,
                'summary_path': self.config.default_summary_path,
                'drawing_space_dimensions': self.config.drawing_space_dimensions,
                'module_dimensions': self.config.module_dimensions
            }
        }
        
        return status
    
    def reset_workflow(self) -> None:
        """
        Reset the workflow to initial state.
        
        Clears all loaded data and resets selection state.
        """
        logger.info("Resetting workflow state")
        
        self._note_collection = None
        self._entity_catalog = None
        self._dxf_document = None
        self.selection_state = SelectionState()
        
        # Reset parsers
        self.dxf_parser = DXFParser()
        
        logger.info("Workflow state reset complete")
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        Get system information for debugging and support.
        
        Returns:
            Dictionary with system information
        """
        return {
            'application': {
                'name': APPLICATION_NAME,
                'version': APPLICATION_VERSION,
                'config_version': self.config.version
            },
            'configuration': {
                'debug_mode': self.config.debug_mode,
                'log_level': self.config.log_level,
                'ui_settings': self.config.ui_settings
            },
            'workflow_status': self.get_workflow_status(),
            'dependencies': self.dependencies.get_component_status()
        }
    
    def cleanup(self) -> None:
        """
        Cleanup resources and temporary data.
        
        Should be called when the application is shutting down.
        """
        logger.info("Cleaning up application core resources")
        
        try:
            # Reset workflow state
            self.reset_workflow()
            
            # Cleanup dependencies
            self.dependencies.cleanup()
            
            logger.info("Application core cleanup complete")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.cleanup()
    
    def __repr__(self) -> str:
        """String representation of the application core."""
        status = self.validate_workflow_prerequisites()
        return (f"ApplicationCore("
                f"config={self.config.application_name}, "
                f"notes_loaded={status['note_collection_loaded']}, "
                f"dxf_loaded={status['dxf_document_loaded']}, "
                f"catalog_created={status['entity_catalog_created']}, "
                f"selected_notes={len(self.selection_state.selected_titles)})")


# Factory functions for common use cases
def create_application_core(config_path: Optional[str] = None) -> ApplicationCore:
    """
    Create an application core instance with optional configuration file.
    
    Args:
        config_path: Path to configuration file (optional)
        
    Returns:
        ApplicationCore instance
    """
    # In a full implementation, this would load configuration from file
    config = GeneralNotesConfig.create_default()
    return ApplicationCore(config)


def create_development_core() -> ApplicationCore:
    """
    Create an application core instance with development settings.
    
    Returns:
        ApplicationCore instance configured for development
    """
    config = GeneralNotesConfig.create_development()
    return ApplicationCore(config)
