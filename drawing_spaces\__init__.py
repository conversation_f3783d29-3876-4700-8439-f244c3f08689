"""
Drawing Spaces Package
=====================

A simplified Python package for adding drawing space rectangles to DXF documents.
Focuses on the core responsibility of creating rectangular boundaries/frames within
existing DXF files.

Main Components:
- DXF rectangle drawing with ezdxf
- Simple coordinate-based positioning
- Basic text annotations (title and scale)
- Integration with existing DXF documents

Usage:
    from drawing_spaces import DXFDrawingSpaceGenerator
    import ezdxf
    
    # Create or open DXF document
    doc = ezdxf.new('R2010')
    msp = doc.modelspace()
    
    # Initialize generator
    generator = DXFDrawingSpaceGenerator(msp, layer_name="DRAWING_SPACES")
    
    # Add a single drawing space rectangle
    generator.add_drawing_space(
        x=0, y=0, width=42000, height=29700,  # A1 at 1:50 scale
        title="PLAN VIEW",
        scale_text="SCALE 1:50"
    )
    
    # Add multiple spaces horizontally
    generator.add_horizontal_layout(
        start_x=0, start_y=0,
        width=42000, height=29700,
        count=3, spacing=5000,
        title_prefix="ELEVATION",
        scale_text="SCALE 1:50"
    )
"""

# Main API
from .drawing_space_generator import (
    DXFDrawingSpaceGenerator,
    calculate_drawing_space_size,
    get_paper_size,
    PAPER_SIZES
)

__version__ = "2.0.0"
__author__ = "Drawing Production Development Team"

# Simplified API for backward compatibility
DrawingSpaceGenerator = DXFDrawingSpaceGenerator

# Main exports
__all__ = [
    "DXFDrawingSpaceGenerator",
    "DrawingSpaceGenerator",  # Backward compatibility
    "calculate_drawing_space_size",
    "get_paper_size",
    "PAPER_SIZES",
    "__version__",
    "__author__"
]
