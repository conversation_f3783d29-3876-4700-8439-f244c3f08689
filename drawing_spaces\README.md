# Drawing Spaces Package

A simplified Python package for adding drawing space rectangles to AutoCAD DXF files using ezdxf. Focused on adding drawing spaces to existing DXF files with proper scale calculations and layout.

## Features

- **DXF Integration**: Add drawing spaces to existing DXF files
- **Paper Size Support**: A0, A1, A2, A3, A4 with landscape/portrait orientations
- **Scale Calculations**: Common architectural scales (1:50, 1:100, 1:200, etc.)
- **Layout Management**: Horizontal, vertical, and grid arrangements
- **AutoCAD Compatibility**: DXF R2010 format with proper layers
- **Lightweight**: Minimal dependencies and simplified API

## Installation

```bash
pip install ezdxf  # Required dependency
```

Add the `drawing_spaces` package to your project structure.

## Quick Start

### Basic Usage

```python
from drawing_spaces import DXFDrawingSpaceGenerator

# Create generator instance
generator = DXFDrawingSpaceGenerator()

# Add drawing spaces to an existing DXF file
generator.add_drawing_spaces(
    'existing_drawing.dxf',    # Input DXF file
    'output_with_spaces.dxf',  # Output DXF file
    paper_size='A1',
    scale_factor=50,
    orientation='landscape',
    spacing=10.0,              # Spacing between spaces
    count=4,                   # Number of spaces
    layout='horizontal'        # Layout arrangement
)
    layout='horizontal',
    spacing_mm=5000.0,  # 5m spacing
    title_prefix='ELEVATION'
)

# Create vertical layout
generator.create_drawing_spaces(
```

## Layout Options

### Horizontal Layout

```python
# Arrange spaces horizontally (left to right)
generator.add_drawing_spaces(
    'input.dxf', 'output.dxf',
    paper_size='A1',
    scale_factor=100,
    count=3,
    layout='horizontal',
    spacing=5000.0  # 5m spacing between spaces
)
```

### Vertical Layout

```python
# Arrange spaces vertically (top to bottom)
generator.add_drawing_spaces(
    'input.dxf', 'output.dxf',
    paper_size='A1',
    scale_factor=50,
    count=3,
    layout='vertical',
    spacing=3000.0  # 3m spacing between spaces
)
```

### Grid Layout

```python
# Arrange spaces in a grid pattern
generator.add_drawing_spaces(
    'input.dxf', 'output.dxf',
    paper_size='A1',
    scale_factor=200,
    count=6,
    layout='grid',
    spacing=4000.0,  # 4m spacing
    grid_cols=3      # 3 columns, 2 rows (6 total spaces)
)
```

## API Reference

### DXFDrawingSpaceGenerator

Main class for adding drawing spaces to DXF files.

#### Methods

**`add_drawing_spaces(input_path, output_path, **kwargs)`\*\*

- Adds drawing space rectangles to an existing DXF file
- **Parameters:**
  - `input_path` (str): Path to input DXF file
  - `output_path` (str): Path to output DXF file
  - `paper_size` (str): Paper size ('A0', 'A1', 'A2', 'A3', 'A4')
  - `scale_factor` (int): Scale factor (e.g., 50 for 1:50)
  - `orientation` (str): 'landscape' or 'portrait'
  - `count` (int): Number of drawing spaces
  - `layout` (str): 'horizontal', 'vertical', or 'grid'
  - `spacing` (float): Spacing between spaces in mm
  - `grid_cols` (int): Number of columns for grid layout
  - `layer_name` (str): DXF layer for drawing spaces (default: 'DRAWING_SPACES')

### Utility Functions

Available utility functions for calculations:

```python
from drawing_spaces import (
    calculate_paper_dimensions,
    calculate_space_dimensions,
    calculate_layout_positions
)

# Calculate paper dimensions
width, height = calculate_paper_dimensions('A1', 'landscape')

# Calculate drawing space dimensions at scale
space_width, space_height = calculate_space_dimensions('A1', 50, 'landscape')

# Calculate positions for layout
positions = calculate_layout_positions(
    count=4, layout='horizontal',
    space_width=space_width, space_height=space_height,
    spacing=5000.0
)
```

### Supported Paper Sizes and Scales

#### Paper Sizes

- **A0**: 1189×841mm
- **A1**: 841×594mm
- **A2**: 594×420mm
- **A3**: 420×297mm
- **A4**: 297×210mm

#### Common Scales

1:1, 1:2, 1:5, 1:10, 1:20, 1:25, 1:50, 1:100, 1:200, 1:250, 1:500, 1:1000, 1:2000, 1:2500

## Examples

### Adding Spaces to Column Drawings

```python
from drawing_spaces import DXFDrawingSpaceGenerator

# Create generator
generator = DXFDrawingSpaceGenerator()

# Add drawing spaces for column elevation drawings
generator.add_drawing_spaces(
    'column_elevations.dxf',
    'column_elevations_with_spaces.dxf',
    paper_size='A1',
    scale_factor=50,
    orientation='landscape',
    count=4,
    layout='horizontal',
    spacing=8000.0,  # 8m spacing for large columns
    layer_name='COLUMN_SPACES'
)
```

### Site Plan Layout

```python
# Large-scale site plan with multiple drawing spaces
generator.add_drawing_spaces(
    'site_plan.dxf',
    'site_plan_with_spaces.dxf',
    paper_size='A1',
    scale_factor=500,  # 1:500 for site plans
    count=2,
    layout='horizontal',
    spacing=15000.0,   # 15m spacing for site scale
    layer_name='SITE_SPACES'
)
```

### Detail Drawing Grid

```python
# Grid of detail spaces
generator.add_drawing_spaces(
    'details.dxf',
    'details_with_spaces.dxf',
    paper_size='A1',
    scale_factor=20,   # 1:20 for details
    count=6,
    layout='grid',
    spacing=2000.0,    # 2m spacing for details
    grid_cols=3,       # 3×2 grid
    layer_name='DETAIL_SPACES'
)
```

## Integration

This package is designed to integrate seamlessly with existing drawing generation workflows. It focuses on one task: adding drawing space rectangles to DXF files with proper scaling and layout.

## License

Part of the Drawing-Production project. Follows the same architectural patterns and conventions.
