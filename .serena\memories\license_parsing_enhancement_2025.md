# License File Parsing Enhancement

## Issue Description
The license validation was only loading 1 user instead of all users from the Google Drive license file. The debug logs revealed that the license file contains all usernames in a single line in CSV format with quotes:
```
'alex.sze', 'nicholas.chong', 'sam.loo', 'michael.lam'
```

## Root Cause
The original parsing logic assumed usernames were separated by newlines, but the Google Drive file stored all users on one line in comma-separated format with quotes.

## Solution Implemented
Enhanced the `_parse_license_data()` function in `auth/license_service.py` to handle both formats:

### Key Changes
1. **CSV Format Detection**: Detects comma-separated format with quotes
2. **Regex Parsing**: Uses regex pattern to extract usernames from quoted CSV format
3. **Backward Compatibility**: Maintains original line-by-line parsing as fallback
4. **Enhanced Debug Logging**: Added comprehensive debug logging for troubleshooting

### Implementation Details
- **CSV Detection**: Checks for comma and quote characters in the data
- **Regex Pattern**: `['\"]([^'\"]+)['\"]` extracts usernames from quoted strings
- **Dual Parsing**: Supports both CSV format and line-by-line format
- **Debug Logging**: Can be enabled by setting logging level to DEBUG

## Result
- ✅ Successfully loads all 4 users from license file
- ✅ Proper license validation for all approved users
- ✅ Enhanced debug logging for troubleshooting
- ✅ Backward compatibility with different license file formats
- ✅ SSL certificate issues resolved with fallback context

## Testing
Verified that all users (alex.sze, nicholas.chong, sam.loo, michael.lam) are now correctly loaded and validated.

## Date
July 18, 2025