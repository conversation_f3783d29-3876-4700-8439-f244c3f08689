"""
Utility modules for the column drawing application.

This package contains helper functions and classes used throughout the column drawing system, such as:
- Geometry calculations
- Logging configuration
- Dimension utilities

Import specific utilities as needed from submodules.
"""

from .dimension_geometry import DimensionGeometry, DimensionGeometryCalculator
from .logging_config import (
    configure_logger_for_module,
    enable_debug_logging,
    get_logging_summary,
    get_message_counter,
    log_consolidated_warning,
    log_reminder,
    setup_logging,
)

__all__ = [
    "DimensionGeometry",
    "DimensionGeometryCalculator",
    "setup_logging",
    "enable_debug_logging",
    "get_logging_summary",
    "log_consolidated_warning",
    "log_reminder",
    "configure_logger_for_module",
    "get_message_counter",
]
