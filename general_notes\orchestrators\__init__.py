"""
Orchestrators Package
====================

High-level workflow coordination for the General Notes Drawing Generator.

This package provides orchestrators that coordinate complex workflows:
- Note selection workflow coordination
- Drawing generation process management
- High-level workflow orchestration
- Error handling and recovery

Modules:
    - workflow_orchestrator: High-level process coordination
    - note_selection_orchestrator: Coordinate note selection workflow
    - generation_orchestrator: Manage output generation process
"""

from .workflow_orchestrator import WorkflowOrchestrator
from .note_selection_orchestrator import NoteSelectionOrchestrator
from .generation_orchestrator import GenerationOrchestrator

__all__ = [
    'WorkflowOrchestrator',
    'NoteSelectionOrchestrator', 
    'GenerationOrchestrator'
]
