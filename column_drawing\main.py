"""
Main Column Drawing Generator
============================

Main application class that orchestrates the column drawing generation process
using the modular components.
"""

import logging
import os


from typing import Optional

from .models.drawing_config import DrawingConfig
from .models.layer_config import StructuralLayerConfig
from .models.rebar_layer_data import RebarLayerData
from .core.application_core import ApplicationCore

from .utils.logging_config import setup_logging, enable_debug_logging, get_logging_summary

logger = logging.getLogger(__name__)


class ColumnDrawingGenerator:
    """
    Main application facade for generating reinforced column drawings.

    This class provides a simplified interface to the core application functionality
    while maintaining backward compatibility. It delegates to the ApplicationCore
    for actual implementation.
    """

    def __init__(self, config: Optional[DrawingConfig] = None, layer_config: Optional[StructuralLayerConfig] = None):
        """
        Initialize the column drawing generator.

        Args:
            config: Drawing configuration object (uses default if None)
            layer_config: Layer configuration object (uses default if None)
        """
        # Initialize the application core
        self.core = ApplicationCore(config, layer_config)

        # Expose core components for backward compatibility
        self.config = self.core.config
        self.layer_config = self.core.layer_config
        self.table_cell_config = self.core.table_cell_config

        logger.info("Column Drawing Generator initialized")

    def generate_drawings(self, csv_filename: str, output_filename: str, use_zone_details: bool = True) -> int:
        """
        Generate all column drawings from CSV data.

        Args:
            csv_filename: Path to CSV file containing column data
            output_filename: Path for output DXF file
            use_zone_details: Whether to use zone details (default: True)

        Returns:
            int: Number of successfully generated drawings

        Raises:
            FileNotFoundError: If CSV file doesn't exist
            ValueError: If CSV data is invalid
        """
        return self.core.generate_drawings(csv_filename, output_filename, use_zone_details)

    def generate_drawings_with_spaces(self, csv_filename: str, output_filename: str, use_zone_details: bool = True) -> int:
        """
        Generate all column drawings from CSV data with drawing space organization.
        
        This enhanced version automatically organizations column detail tables into
        structured drawing spaces within the same DXF file:
        - A1 size at 1:50 scale
        - Maximum 4 tables per row, 2 rows per space (8 tables total)
        - Automatic overflow to additional drawing spaces

        Args:
            csv_filename: Path to CSV file containing column data
            output_filename: Path for output DXF file
            use_zone_details: Whether to use zone details (default: True)

        Returns:
            int: Number of successfully generated drawings

        Raises:
            FileNotFoundError: If CSV file doesn't exist
            ValueError: If CSV data is invalid
        """
        return self.core.generate_drawings_with_spaces(csv_filename, output_filename, use_zone_details)

    def get_generation_statistics(self) -> dict:
        """
        Get comprehensive statistics about the drawing generation process.

        Returns:
            dict: Detailed statistics including document info, validation results, table configuration, etc.
        """
        return self.core.get_generation_statistics()

    def get_column_rebar_data(self, column_name: str = None) -> Optional[RebarLayerData]:
        """
        Get organized rebar data for a specific column or all columns.

        Args:
            column_name: Name of the column to retrieve data for (optional)

        Returns:
            RebarLayerData for specific column, or dict of all column data
        """
        return self.core.get_column_rebar_data(column_name)

    def export_rebar_data_summary(self) -> dict:
        """
        Export a comprehensive summary of all rebar data for analysis.

        Returns:
            Dictionary with detailed rebar information for all columns
        """
        return self.core.export_rebar_data_summary()

    def get_table_cell_info(self, cell_name: str = None) -> dict:
        """
        Get information about table cell configuration for debugging and analysis.

        Args:
            cell_name: Specific cell name to get info for (optional)

        Returns:
            dict: Table cell information
        """
        return self.core.get_table_cell_info(cell_name)

# CLI interface is now handled by the interfaces module
# This maintains backward compatibility for direct imports


def main():
    """Main function - delegates to CLI interface for separation of concerns."""
    from .interfaces.cli_interface import main as cli_main
    cli_main()


if __name__ == "__main__":
    main()
