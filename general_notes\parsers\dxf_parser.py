"""
DXF Parser
=========

DXF file parsing functionality for the General Notes Drawing Generator.

This module provides functionality for:
- Loading DXF database files using ezdxf
- Cataloging entities by boundary regions
- Entity selection and filtering
- DXF file validation and metadata extraction

Author: Drawing Production System
"""

import logging
from typing import List, Dict, Any, Optional, Union, Set, Tuple
from pathlib import Path
import ezdxf
from ezdxf.document import Drawing
from ezdxf.entities import DXFEntity
from ezdxf.math import Vec3

from ..models.boundary import Boundary, BoundaryPoint
from ..models.note_detail import NoteDetail, NoteDetailCollection
from .validation import ValidationResult

logger = logging.getLogger(__name__)


class DXFParsingError(Exception):
    """Raised when DXF parsing fails."""
    pass


class EntityCatalog:
    """
    Catalog of DXF entities organized by note detail boundaries.
    
    Provides efficient lookup and filtering of entities within specific
    boundary regions for note detail extraction.
    """
    
    def __init__(self):
        """Initialize empty entity catalog."""
        self.entities_by_note: Dict[str, List[DXFEntity]] = {}
        self.entity_metadata: Dict[str, Dict[str, Any]] = {}
        self.total_entities = 0
    
    def add_entities(self, note_title: str, entities: List[DXFEntity]) -> None:
        """
        Add entities for a note detail.
        
        Args:
            note_title: Title of the note detail
            entities: List of entities within the note boundary
        """
        self.entities_by_note[note_title] = entities
        self.total_entities += len(entities)
        
        # Store metadata
        self.entity_metadata[note_title] = {
            'entity_count': len(entities),
            'entity_types': self._get_entity_types(entities),
            'layers': self._get_entity_layers(entities)
        }
        
        logger.debug(f"Added {len(entities)} entities for note '{note_title}'")
    
    def get_entities(self, note_title: str) -> List[DXFEntity]:
        """
        Get entities for a specific note detail.
        
        Args:
            note_title: Title of the note detail
            
        Returns:
            List of entities or empty list if not found
        """
        return self.entities_by_note.get(note_title, [])
    
    def get_entity_count(self, note_title: str) -> int:
        """
        Get entity count for a specific note detail.
        
        Args:
            note_title: Title of the note detail
            
        Returns:
            Number of entities
        """
        return len(self.get_entities(note_title))
    
    def get_all_note_titles(self) -> List[str]:
        """
        Get all note titles in the catalog.
        
        Returns:
            List of note titles
        """
        return list(self.entities_by_note.keys())
    
    def get_catalog_summary(self) -> Dict[str, Any]:
        """
        Get summary information about the catalog.
        
        Returns:
            Dictionary with catalog statistics
        """
        return {
            'note_count': len(self.entities_by_note),
            'total_entities': self.total_entities,
            'notes': {
                title: self.entity_metadata[title]
                for title in self.entities_by_note.keys()
            }
        }
    
    def _get_entity_types(self, entities: List[DXFEntity]) -> Dict[str, int]:
        """Get count of each entity type."""
        type_counts = {}
        for entity in entities:
            entity_type = entity.dxftype()
            type_counts[entity_type] = type_counts.get(entity_type, 0) + 1
        return type_counts
    
    def _get_entity_layers(self, entities: List[DXFEntity]) -> Set[str]:
        """Get unique layers used by entities."""
        layers = set()
        for entity in entities:
            if hasattr(entity.dxf, 'layer'):
                layers.add(entity.dxf.layer)
        return layers


class DXFParser:
    """
    Parser for DXF database files containing note details.
    
    Handles loading DXF files, cataloging entities by boundary regions,
    and providing efficient entity selection for note detail extraction.
    """
    
    def __init__(self, spatial_tolerance: float = 1e-6):
        """
        Initialize the DXF parser.
        
        Args:
            spatial_tolerance: Tolerance for spatial calculations
        """
        self.spatial_tolerance = spatial_tolerance
        self.document: Optional[Drawing] = None
        self.entity_catalog: Optional[EntityCatalog] = None
    
    def load_dxf_file(self, file_path: Union[str, Path]) -> Drawing:
        """
        Load a DXF file and return the document.
        
        Args:
            file_path: Path to the DXF file
            
        Returns:
            ezdxf Drawing document
            
        Raises:
            DXFParsingError: If file cannot be loaded
        """
        try:
            logger.info(f"Loading DXF file: {file_path}")
            
            # Load the DXF document
            self.document = ezdxf.readfile(str(file_path))
            
            logger.info(f"Successfully loaded DXF file (version: {self.document.dxfversion})")
            return self.document
            
        except ezdxf.DXFError as e:
            error_msg = f"DXF format error: {str(e)}"
            logger.error(error_msg)
            raise DXFParsingError(error_msg) from e
        except FileNotFoundError:
            error_msg = f"DXF file not found: {file_path}"
            logger.error(error_msg)
            raise DXFParsingError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error loading DXF file: {str(e)}"
            logger.error(error_msg)
            raise DXFParsingError(error_msg) from e
    
    def catalog_entities_by_notes(self, note_collection: NoteDetailCollection) -> EntityCatalog:
        """
        Catalog entities from the loaded DXF file by note detail boundaries.
        
        Args:
            note_collection: Collection of note details with boundaries
            
        Returns:
            EntityCatalog with entities organized by note titles
            
        Raises:
            DXFParsingError: If cataloging fails
        """
        if not self.document:
            raise DXFParsingError("No DXF document loaded. Call load_dxf_file() first.")
        
        try:
            logger.info(f"Cataloging entities for {len(note_collection)} note details")
            
            self.entity_catalog = EntityCatalog()
            modelspace = self.document.modelspace()
            
            # Get all entities from modelspace
            all_entities = list(modelspace)
            logger.debug(f"Found {len(all_entities)} total entities in modelspace")
            
            # Process each note detail
            for note_detail in note_collection:
                entities_in_boundary = self._get_entities_in_boundary(
                    all_entities, note_detail.boundary
                )
                self.entity_catalog.add_entities(note_detail.title, entities_in_boundary)
            
            logger.info(f"Cataloging complete. Total entities cataloged: {self.entity_catalog.total_entities}")
            return self.entity_catalog
            
        except Exception as e:
            error_msg = f"Error cataloging entities: {str(e)}"
            logger.error(error_msg)
            raise DXFParsingError(error_msg) from e
    
    def _get_entities_in_boundary(self, entities: List[DXFEntity], boundary: Boundary) -> List[DXFEntity]:
        """
        Get all entities that fall within the specified boundary.
        
        Args:
            entities: List of all entities to check
            boundary: Boundary to check against
            
        Returns:
            List of entities within the boundary
        """
        entities_in_boundary = []
        boundary_polygon = boundary.to_polygon_2d()
        
        for entity in entities:
            if self._is_entity_in_boundary(entity, boundary_polygon):
                entities_in_boundary.append(entity)
        
        logger.debug(f"Found {len(entities_in_boundary)} entities in boundary for '{boundary.title}'")
        return entities_in_boundary
    
    def _is_entity_in_boundary(self, entity: DXFEntity, boundary_polygon: List[Tuple[float, float]]) -> bool:
        """
        Check if an entity is within the boundary polygon.
        
        Args:
            entity: DXF entity to check
            boundary_polygon: List of (x, y) points defining boundary
            
        Returns:
            True if entity is within boundary
        """
        try:
            # Get representative points for the entity
            test_points = self._get_entity_test_points(entity)
            
            # Check if any test point is within the boundary
            for point in test_points:
                if self._point_in_polygon(point, boundary_polygon):
                    return True
            
            return False
            
        except Exception as e:
            logger.warning(f"Error checking entity {entity.dxftype()}: {str(e)}")
            return False
    
    def _get_entity_test_points(self, entity: DXFEntity) -> List[Tuple[float, float]]:
        """
        Get representative points for an entity to test boundary inclusion.
        
        Args:
            entity: DXF entity
            
        Returns:
            List of (x, y) test points
        """
        entity_type = entity.dxftype()
        
        try:
            if entity_type == 'LINE':
                # Test both endpoints
                return [
                    (entity.dxf.start.x, entity.dxf.start.y),
                    (entity.dxf.end.x, entity.dxf.end.y)
                ]
            
            elif entity_type in ['TEXT', 'MTEXT']:
                # Test insertion point
                insert_point = getattr(entity.dxf, 'insert', None)
                if insert_point:
                    return [(insert_point.x, insert_point.y)]
            
            elif entity_type == 'CIRCLE':
                # Test center point
                center = getattr(entity.dxf, 'center', None)
                if center:
                    return [(center.x, center.y)]
            
            elif entity_type == 'LWPOLYLINE':
                # Test all vertices
                points = []
                for point in entity.get_points():
                    points.append((point[0], point[1]))
                return points
            
            elif entity_type == 'POLYLINE':
                # Test all vertices
                points = []
                for vertex in entity.vertices:
                    location = getattr(vertex.dxf, 'location', None)
                    if location:
                        points.append((location.x, location.y))
                return points
            
            elif entity_type == 'INSERT':
                # Test insertion point
                insert_point = getattr(entity.dxf, 'insert', None)
                if insert_point:
                    return [(insert_point.x, insert_point.y)]
            
            elif entity_type in ['DIMENSION', 'LEADER']:
                # Test definition points
                points = []
                for attr_name in ['defpoint', 'defpoint2', 'defpoint3', 'defpoint4']:
                    point = getattr(entity.dxf, attr_name, None)
                    if point:
                        points.append((point.x, point.y))
                return points
            
            else:
                # For unknown entity types, try to get any point attribute
                for attr_name in ['insert', 'start', 'center', 'location']:
                    point = getattr(entity.dxf, attr_name, None)
                    if point:
                        return [(point.x, point.y)]
        
        except Exception as e:
            logger.debug(f"Error getting test points for {entity_type}: {str(e)}")
        
        # Fallback: return empty list (entity will be excluded)
        return []
    
    def _point_in_polygon(self, point: Tuple[float, float], polygon: List[Tuple[float, float]]) -> bool:
        """
        Check if a point is inside a polygon using ray casting algorithm.
        
        Args:
            point: (x, y) point to test
            polygon: List of (x, y) polygon vertices
            
        Returns:
            True if point is inside polygon
        """
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def get_dxf_metadata(self) -> Dict[str, Any]:
        """
        Get metadata about the loaded DXF file.
        
        Returns:
            Dictionary with DXF file metadata
            
        Raises:
            DXFParsingError: If no document is loaded
        """
        if not self.document:
            raise DXFParsingError("No DXF document loaded")
        
        modelspace = self.document.modelspace()
        all_entities = list(modelspace)
        
        # Get entity type counts
        entity_types = {}
        layers = set()
        
        for entity in all_entities:
            entity_type = entity.dxftype()
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            if hasattr(entity.dxf, 'layer'):
                layers.add(entity.dxf.layer)
        
        return {
            'dxf_version': self.document.dxfversion,
            'total_entities': len(all_entities),
            'entity_types': entity_types,
            'layers': sorted(list(layers)),
            'layer_count': len(layers)
        }
    
    def validate_dxf_file(self, file_path: Union[str, Path]) -> ValidationResult:
        """
        Validate DXF file without full loading.
        
        Args:
            file_path: Path to DXF file
            
        Returns:
            ValidationResult with validation details
        """
        result = ValidationResult()
        
        try:
            # Try to load the file
            doc = ezdxf.readfile(str(file_path))
            
            result.add_info(f"DXF file is valid (version: {doc.dxfversion})")
            
            # Check for modelspace
            modelspace = doc.modelspace()
            entity_count = len(list(modelspace))
            
            if entity_count == 0:
                result.add_warning("DXF file contains no entities in modelspace")
            else:
                result.add_info(f"Found {entity_count} entities in modelspace")
            
            result.metadata.update({
                'dxf_version': doc.dxfversion,
                'entity_count': entity_count
            })
            
        except ezdxf.DXFError as e:
            result.add_error(f"Invalid DXF format: {str(e)}")
        except FileNotFoundError:
            result.add_error(f"DXF file not found: {file_path}")
        except Exception as e:
            result.add_error(f"Error validating DXF file: {str(e)}")
        
        return result


# Convenience functions
def load_and_catalog_dxf(dxf_path: Union[str, Path], 
                        note_collection: NoteDetailCollection) -> Tuple[Drawing, EntityCatalog]:
    """
    Load DXF file and catalog entities by note boundaries.
    
    Args:
        dxf_path: Path to DXF file
        note_collection: Collection of note details
        
    Returns:
        Tuple of (DXF document, entity catalog)
        
    Raises:
        DXFParsingError: If loading or cataloging fails
    """
    parser = DXFParser()
    document = parser.load_dxf_file(dxf_path)
    catalog = parser.catalog_entities_by_notes(note_collection)
    return document, catalog


def validate_dxf_file(file_path: Union[str, Path]) -> ValidationResult:
    """
    Validate DXF file format and content.
    
    Args:
        file_path: Path to DXF file
        
    Returns:
        ValidationResult with validation details
    """
    parser = DXFParser()
    return parser.validate_dxf_file(file_path)
