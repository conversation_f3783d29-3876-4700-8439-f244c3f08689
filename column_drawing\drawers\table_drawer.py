"""
Table Drawing Component
======================

Handles drawing of table structures and content placement for column drawings
with professional layer management following AIA standards.

Key Features:
- Intelligent line management to prevent duplicate/overlapping lines in horizontal table layouts
- Proper sequencing of tables by column mark and floor level
- Horizontal grouping of tables with the same column mark
- Zone detail drawing integration with proper cell bounds
- Professional layer management following AIA standards

Updated for horizontal table arrangement:
- Tables with same column mark are grouped horizontally
- Within each group, tables are ordered by floor level (left to right)
- Left border lines are omitted for non-first tables to prevent overlaps
- Proper spacing and bounds calculation for multi-table layouts

REFACTORING COMPLETED:
=====================

All hardcoded values have been successfully extracted to the TableConfig class:

✓ TRIANGLE CELL COORDINATES: Moved to TriangleCoordinates dataclass
✓ CELL CENTER COORDINATES: Moved to CellCenterCoordinates dataclass
✓ TEXT POSITIONING: Moved to TextConfiguration dataclass
✓ SPACING AND LAYOUT: Moved to SpacingConfiguration dataclass
✓ VALIDATION THRESHOLDS: Moved to ValidationConfiguration dataclass
✓ REBAR LOGIC CONSTANTS: Moved to RebarLogicConfiguration dataclass
✓ TABLE DIMENSIONS: Moved to TableDimensionsConfiguration dataclass
✓ COLOR AND STYLING: Moved to TableColorConfiguration dataclass
✓ TEXT HEIGHTS AND GAPS: Extracted to TextConfiguration with proper methods
✓ COLUMN POSITIONING: Extracted to SpacingConfiguration with accessor methods

The enhanced TableConfig class provides:
- Centralized configuration management for all table-specific parameters
- Type-safe parameter access through dedicated getter methods
- Complete separation of table-specific config from general drawing config
- Backward compatibility with existing ColumnDrawingGenerator interface
- Improved maintainability and testability
- Clear, descriptive names for all configuration parameters
"""

import logging
from typing import Tuple, Optional, List
from ..models.column_config import ColumnConfig
from ..models.drawing_config import DrawingConfig
from ..models.zone_config import ZoneConfigSet
from ..models.table_config import TableConfig
from ..io.dxf_writer import DXFWriter
from .elevation_drawer import ElevationDrawer
from .table.table_text_utils import TableTextManager
from .table.table_position_utils import TablePositionCalculator
from .table.table_layout_utils import TableLayoutValidator
from .table.table_cell_manager import TableCellManager
from .table.table_structure_manager import TableStructureManager

logger = logging.getLogger(__name__)


class TableDrawer:
    """
    Drawer for table structures and content with AIA layer management.

    This class handles:
    - Drawing precise table line structures on appropriate layers
    - Adding text content to table cells with proper layer assignment
    - Positioning and formatting table elements following AIA standards
    """

    def __init__(self, modelspace, config: DrawingConfig, dxf_writer: Optional[DXFWriter] = None, section_drawer=None, doc=None):
        """
        Initialize the table drawer with layer management.

        Args:
            modelspace: DXF modelspace for drawing operations
            config: Drawing configuration object
            dxf_writer: DXF writer with layer management (optional for backward compatibility)
            section_drawer: SectionDrawer instance for zone detail drawing (optional)
            doc: DXF document for dimension drawing (optional)
        """
        self.msp = modelspace
        self.config = config
        self.dxf_writer = dxf_writer
        self.section_drawer = section_drawer
        self.doc = doc

        # Initialize table-specific configuration
        self.table_config = TableConfig()

        # Initialize utility managers
        self.text_manager = TableTextManager(
            self.table_config, dxf_writer, modelspace)
        self.position_calc = TablePositionCalculator(self.table_config)
        self.layout_validator = TableLayoutValidator(self.table_config)
        self.cell_manager = TableCellManager(
            self.table_config, dxf_writer, modelspace, section_drawer, config)
        self.structure_manager = TableStructureManager(
            self.table_config, dxf_writer, modelspace, config)

        # Initialize elevation drawer with document for dimension support
        self.elevation_drawer = ElevationDrawer(
            modelspace, config, dxf_writer, doc)

        logger.debug("TableDrawer initialized")

    # ==================== PUBLIC API METHODS ====================

    def draw_table_structure(self, origin_x: float, origin_y: float, table_index: int = 0, is_first_in_group: bool = True) -> Tuple[float, float, float, float]:
        """
        Draw the table structure using precise coordinates with intelligent line management.
        Prevents duplicate lines when tables are arranged horizontally.

        Args:
            origin_x: Table origin X coordinate
            origin_y: Table origin Y coordinate
            table_index: Index of this table in the horizontal group (0-based)
            is_first_in_group: Whether this is the first table in a horizontal group

        Returns:
            Tuple[float, float, float, float]: Table bounds (x, y, width, height)
        """
        return self.structure_manager.draw_table_structure(origin_x, origin_y, table_index, is_first_in_group)

    def add_table_content(self, column_config: ColumnConfig, table_x: float, table_y: float, actual_rebar_data=None, zone_config_set: Optional[ZoneConfigSet] = None) -> None:
        """
        Add text content to the table cells (ASD format).

        Args:
            column_config: Column configuration data
            table_x: Table origin X coordinate
            table_y: Table origin Y coordinate
            actual_rebar_data: Actual drawn rebar data (RebarLayerData) - optional
            zone_config_set: Zone configurations for detailed drawings - optional
        """
        try:
            # Calculate cell positions for the table
            cell_positions = self._calculate_table_cell_positions(
                table_x, table_y)

            # Add header content (column mark and floor information)
            self._add_header_content(cell_positions, column_config)

            # Add elevation diagrams first to store link marks in DataFrame
            self.elevation_drawer.draw_elevation_diagrams(
                table_x, table_y, column_config, zone_config_set)

            # Add zone details in the middle section (now can access DataFrame marks)
            self._add_zone_content(
                cell_positions, column_config, zone_config_set)

            # Add size and main bar content in the bottom section
            self._add_size_and_main_bar_content(
                cell_positions, column_config, actual_rebar_data)

            logger.debug(f"Added ASD table content for {column_config.name}")

        except Exception as e:
            logger.error(
                f"Error adding ASD table content for {column_config.name}: {e}")
            raise

    def _calculate_table_cell_positions(self, table_x: float, table_y: float) -> dict:
        """
        Calculate absolute positions for all table cells.

        Args:
            table_x: Table origin X coordinate
            table_y: Table origin Y coordinate (bottom-left corner)

        Returns:
            dict: Dictionary containing all calculated cell positions
        """
        # Get ASD table cell positions using new naming conventions
        cell_positions = self.config.get_cell_positions()

        # COORDINATE SYSTEM FIX:
        # DrawingSpaceOrganizer passes table_y as bottom-left corner
        # But table content coordinate system expects top-left corner
        # Convert: top-left Y = bottom-left Y + table height
        table_height = self.table_config.get_table_height()
        table_top_left_y = table_y + table_height

        # DEBUG: Print the coordinate conversion to trace table positioning
        print(f"=== TABLE CELL POSITIONING DEBUG ===")
        print(f"Input table_y (bottom-left): {table_y:.0f}")
        print(f"Table height: {table_height:.0f}")
        print(f"Calculated table_top_left_y: {table_top_left_y:.0f}")
        print(f"Table X: {table_x:.0f}")
        print(f"=====================================")

        # Calculate absolute ASD table cell positions using top-left origin
        return {
            # Column positions (X coordinates unchanged)
            'title_column_x': table_x + cell_positions['title_column_x'],
            'detail_column_x': table_x + cell_positions['detail_column_x'],
            'elevation_column_x': table_x + cell_positions['elevation_column_x'],

            # Row positions (Y coordinates relative to top-left corner)
            'header_row_y': table_top_left_y + cell_positions['header_row_y'],
            'zone_d_row_y': table_top_left_y + cell_positions['zone_d_row_y'],
            'zone_c_row_y': table_top_left_y + cell_positions['zone_c_row_y'],
            'zone_b_row_y': table_top_left_y + cell_positions['zone_b_row_y'],
            'zone_a_row_y': table_top_left_y + cell_positions['zone_a_row_y'],
            'main_bar_row_y': table_top_left_y + cell_positions['main_bar_row_y'],
            'size_row_y': table_top_left_y + cell_positions['size_row_y']
        }

    def _add_header_content(self, cell_positions: dict, column_config: ColumnConfig) -> None:
        """
        Add header content including column mark and floor information.

        Args:
            cell_positions: Dictionary of calculated cell positions
            column_config: Column configuration data
        """
        # Add triangle cells with column mark and floor labels
        self.cell_manager.add_column_mark_triangle_cell(
            cell_positions['title_column_x'],
            cell_positions['header_row_y'],
            cell_positions['zone_d_row_y']
        )
        self.cell_manager.add_floor_mark_triangle_cell(
            cell_positions['title_column_x'],
            cell_positions['header_row_y'],
            cell_positions['zone_d_row_y']
        )

        # Add data cells with actual column mark and floor values
        self.cell_manager.add_column_mark_data_cell(
            cell_positions['detail_column_x'],
            cell_positions['header_row_y'],
            cell_positions['zone_d_row_y'],
            column_config
        )
        self.cell_manager.add_floor_value_cell(
            cell_positions['title_column_x'],
            cell_positions['header_row_y'],
            cell_positions['zone_d_row_y'],
            column_config
        )

    def _add_zone_content(self, cell_positions: dict, column_config: ColumnConfig, zone_config_set: Optional[ZoneConfigSet]) -> None:
        """
        Add zone detail content in the middle section of the table.

        Args:
            cell_positions: Dictionary of calculated cell positions
            column_config: Column configuration data
            zone_config_set: Zone configurations for detailed drawings
        """
        # Zone repositioning after merging Zone C into Zone A:
        # - Zone B moves to original Zone C cell position
        # - Zone A moves to original Zone B cell position
        # - Zone C position is no longer used
        zone_row_positions = [
            # Zone D stays in original position
            cell_positions['zone_d_row_y'],
            # Zone B moves to Zone C position
            cell_positions['zone_c_row_y'],
            # Zone A moves to Zone B position
            cell_positions['zone_b_row_y']
        ]

        self.cell_manager.add_zone_details(
            cell_positions['detail_column_x'],
            cell_positions['elevation_column_x'],
            zone_row_positions,
            column_config,
            zone_config_set
        )

    def _add_size_and_main_bar_content(self, cell_positions: dict, column_config: ColumnConfig, actual_rebar_data=None) -> None:
        """
        Add size and main bar content in the bottom section of the table.

        Args:
            cell_positions: Dictionary of calculated cell positions
            column_config: Column configuration data
            actual_rebar_data: Actual drawn rebar data (optional)
        """
        # Add labels
        self._add_size_and_main_bar_labels(
            cell_positions['title_column_x'],
            cell_positions['main_bar_row_y'],
            cell_positions['size_row_y']
        )

        # Add data
        self._add_size_and_main_bar_data(
            cell_positions['detail_column_x'],
            cell_positions['main_bar_row_y'],
            cell_positions['size_row_y'],
            column_config,
            actual_rebar_data
        )

    # ==================== CELL CONTENT METHODS ====================
    # Cell content methods have been moved to TableCellManager for better organization

    # ==================== LABEL AND DATA METHODS ====================

    def _add_size_and_main_bar_labels(self, title_column_x: float, main_bar_row_y: float, size_row_y: float) -> None:
        """Add 'MAIN BAR' and 'SIZE' labels in the TITLE_MAIN_BAR and TITLE_SIZE cells."""
        try:
            # Get text layer for labels
            text_layer = self.text_manager.get_text_layer()

            # Calculate cell positions for both labels
            main_bar_position, size_position = self.position_calc.calculate_title_cell_positions(
                title_column_x, main_bar_row_y, size_row_y
            )

            # Add both text labels
            self.text_manager.add_title_texts(
                text_layer, main_bar_position, size_position)

        except Exception as e:
            logger.error(f"Error adding size and main bar labels: {e}")
            raise

    def _add_size_and_main_bar_data(self, detail_column_x: float, main_bar_row_y: float, size_row_y: float, column_config: ColumnConfig, actual_rebar_data=None) -> None:
        """Add size and main bar data in the VALUE_SIZE and VALUE_MAIN_BAR cells."""
        try:
            # Get data layer for values
            data_layer = self.text_manager.get_data_layer()

            # Prepare text content
            size_text, main_bar_text = self.text_manager.prepare_data_text(
                column_config, actual_rebar_data)

            # Calculate cell positions for both data values
            main_bar_position, size_position = self.position_calc.calculate_data_cell_positions(
                detail_column_x, main_bar_row_y, size_row_y
            )

            # Add both data texts
            self.text_manager.add_data_texts(
                data_layer, main_bar_text, main_bar_position, size_text, size_position)

        except Exception as e:
            logger.error(f"Error adding size and main bar data: {e}")
            raise

    # ==================== HELPER METHODS ====================
    # Helper methods have been moved to appropriate utility classes for better organization

    # ==================== GROUP LAYOUT METHODS ====================

    def draw_table_group_structure(self, group_data: List[Tuple], origin_x: float, origin_y: float, horizontal_spacing: float = None) -> List[Tuple[float, float, float, float]]:
        """
        Draw the complete structure for a horizontal group of tables with intelligent line management.

        Args:
            group_data: List of (column_config, zone_config_set) tuples for the group
            origin_x: X coordinate for group origin
            origin_y: Y coordinate for group origin
            horizontal_spacing: Spacing between tables

        Returns:
            List of table bounds for each table in the group
        """
        try:
            # Validate input and prepare spacing
            if not self.layout_validator.validate_group_input(group_data):
                return []

            horizontal_spacing = self.layout_validator.prepare_horizontal_spacing(
                horizontal_spacing)

            # Draw all tables in the group
            table_bounds_list = self._draw_tables_in_group(
                group_data, origin_x, origin_y, horizontal_spacing)

            # Validate final layout
            self.layout_validator.validate_final_layout(
                group_data, origin_x, origin_y, horizontal_spacing)

            logger.info(f"Completed group structure: {len(group_data)} tables")
            return table_bounds_list

        except Exception as e:
            logger.error(f"Error drawing table group structure: {e}")
            raise

    def _draw_tables_in_group(self, group_data: List[Tuple], origin_x: float, origin_y: float, horizontal_spacing: float) -> List[Tuple[float, float, float, float]]:
        """Draw all tables in the group and return their bounds."""
        current_x = origin_x
        table_bounds_list = []

        logger.debug(
            f"Drawing group structure for {len(group_data)} tables at origin ({origin_x}, {origin_y})")

        for i, (column_config, zone_config_set) in enumerate(group_data):
            is_first = self.table_config.is_first_table_in_group(i)

            # Draw table structure with intelligent line filtering
            table_bounds = self.draw_table_structure(
                current_x, origin_y,
                table_index=i,
                is_first_in_group=is_first
            )

            table_bounds_list.append(table_bounds)

            # Move to next horizontal position
            current_x += table_bounds[2] + \
                horizontal_spacing  # Table width + spacing

            logger.debug(
                f"Drew table {i+1}/{len(group_data)} at x={table_bounds[0]}, width={table_bounds[2]}")

        return table_bounds_list

    # ==================== VALIDATION AND UTILITY METHODS ====================

    def validate_table_layout(self, group_data: List[Tuple], origin_x: float, origin_y: float, horizontal_spacing: float = None) -> bool:
        """
        Validate that the table layout will work correctly without overlaps.

        Args:
            group_data: List of (column_config, zone_config_set) tuples for the group
            origin_x: X coordinate for group origin
            origin_y: Y coordinate for group origin
            horizontal_spacing: Spacing between tables

        Returns:
            bool: True if layout is valid, False otherwise
        """
        return self.layout_validator.validate_table_layout(group_data, origin_x, origin_y, horizontal_spacing)

    def debug_table_coordinates(self, origin_x: float, origin_y: float, table_index: int = 0) -> None:
        """
        Debug method to log table coordinate information for troubleshooting.

        Args:
            origin_x: Table origin X coordinate
            origin_y: Table origin Y coordinate
            table_index: Index of this table in horizontal group
        """
        self.structure_manager.debug_table_coordinates(
            origin_x, origin_y, table_index)

    def get_table_group_total_width(self, group_size: int, horizontal_spacing: float = None) -> float:
        """
        Calculate the total width required for a horizontal group of tables.

        Args:
            group_size: Number of tables in the group
            horizontal_spacing: Spacing between tables

        Returns:
            float: Total width required for the group
        """
        if not self.table_config.validate_group_size(group_size):
            return 0.0

        # Use default spacing if not provided
        if horizontal_spacing is None:
            horizontal_spacing = self.table_config.get_default_horizontal_spacing()

        table_width = self.table_config.get_table_width()
        total_spacing = (group_size - 1) * horizontal_spacing
        total_width = (group_size * table_width) + total_spacing

        logger.debug(
            f"Group width calculation: {group_size} tables × {table_width} + {total_spacing} spacing = {total_width}")
        return total_width

    def get_recommended_horizontal_spacing(self, group_size: int) -> float:
        """
        Get recommended horizontal spacing based on group size.

        Args:
            group_size: Number of tables in the group

        Returns:
            float: Recommended spacing between tables
        """
        return self.table_config.get_recommended_horizontal_spacing(group_size)
