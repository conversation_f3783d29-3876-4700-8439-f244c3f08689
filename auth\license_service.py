"""
License Service Module
====================

This module provides license validation functionality for the authentication system.
Handles checking user licenses against the approved user list stored in Google Drive.

Features:
    - Remote license validation against Google Drive
    - Timeout handling for network requests
    - Graceful error handling for network issues
    - Secure license list retrieval

Author: Drawing Production System
Date: 2025
"""

import logging
import urllib.request
import urllib.error
from typing import List

from .constants import LICENSE_FILE_ID, URL_TIMEOUT


def check_user_license(user_name: str) -> bool:
    """
    Check if the user is in the approved license list.
    
    Args:
        user_name: Username to check against the license list
        
    Returns:
        <PERSON><PERSON><PERSON> indicating if user is licensed
    """
    import ssl
    
    try:
        # Construct the direct download URL using the constant file ID
        direct_url = f"https://drive.google.com/uc?export=download&id={LICENSE_FILE_ID}"
        logging.debug(f"Checking license for user: '{user_name}'")
        logging.debug(f"License URL: {direct_url}")

        # Create SSL context that handles certificate verification issues
        ssl_context = ssl.create_default_context()
        
        try:
            # First attempt with default SSL context (secure)
            logging.debug("Attempting to retrieve license data with verified SSL context")
            with urllib.request.urlopen(direct_url, timeout=URL_TIMEOUT, context=ssl_context) as response:
                license_data = response.read().decode('utf-8')
                logging.info("License data retrieved successfully with verified SSL context")
        except (ssl.SSLError, urllib.error.URLError) as ssl_error:
            # Check if the error is SSL-related
            if "SSL" in str(ssl_error) or "CERTIFICATE" in str(ssl_error):
                logging.warning(f"SSL certificate verification failed: {ssl_error}")
                logging.info("Attempting fallback with unverified SSL context...")
                
                # Fallback: Create unverified SSL context for problematic environments
                ssl_context_unverified = ssl.create_default_context()
                ssl_context_unverified.check_hostname = False
                ssl_context_unverified.verify_mode = ssl.CERT_NONE
                
                try:
                    with urllib.request.urlopen(direct_url, timeout=URL_TIMEOUT, context=ssl_context_unverified) as response:
                        license_data = response.read().decode('utf-8')
                        logging.info("License data retrieved successfully using fallback SSL context")
                except Exception as fallback_error:
                    logging.error(f"Fallback SSL context also failed: {fallback_error}")
                    raise
            else:
                # Re-raise non-SSL URLErrors
                raise
        
        # Debug: Log the raw response information
        logging.debug(f"License data length: {len(license_data)} characters")
        logging.debug(f"License data type: {type(license_data)}")
        
        # Parse the license data and check for the user
        approved_users = _parse_license_data(license_data)
        
        # Debug: Log the comparison process
        logging.debug(f"Checking if user '{user_name}' is in approved list")
        logging.debug(f"User name (lowercase): '{user_name.lower()}'")
        
        # Create lowercase list for comparison
        approved_users_lower = [user.lower() for user in approved_users]
        logging.debug(f"Approved users (lowercase): {approved_users_lower}")
        
        is_licensed = user_name.lower() in approved_users_lower
        
        if is_licensed:
            logging.info(f"User '{user_name}' is licensed")
        else:
            logging.warning(f"User '{user_name}' is not in the approved license list")
            logging.debug(f"Available users: {approved_users}")
            
        return is_licensed

    except urllib.error.URLError as e:
        logging.error(f"URL Error checking license: {str(e)}")
        logging.error("This may indicate network connectivity issues or firewall restrictions")
        return False
    except Exception as e:
        logging.error(f"Error checking license: {str(e)}")
        return False


def _parse_license_data(license_data: str) -> List[str]:
    """
    Parse the license data from the remote file.
    
    Args:
        license_data: Raw license data from the remote file
        
    Returns:
        List of approved usernames
    """
    try:
        # Debug: Log the raw license data
        logging.debug(f"Raw license data received (length: {len(license_data)} chars)")
        logging.debug(f"First 200 characters: {license_data[:200]}")
        
        # Clean up the data first
        cleaned_data = license_data.strip()
        
        # Check if the data is in CSV format (comma-separated with quotes)
        if "," in cleaned_data and ("'" in cleaned_data or '"' in cleaned_data):
            logging.debug("Detected CSV format with quotes, parsing as comma-separated values")
            
            # Remove outer quotes and split by comma
            # Handle both single and double quotes
            import re
            
            # Extract usernames from quoted CSV format
            # Pattern matches: 'username' or "username"
            username_pattern = r"['\"]([^'\"]+)['\"]"
            matches = re.findall(username_pattern, cleaned_data)
            
            approved_users = []
            for i, username in enumerate(matches):
                username = username.strip()
                if username and not username.startswith('#'):
                    approved_users.append(username)
                    logging.debug(f"Extracted user {i+1}: '{username}'")
            
            logging.debug(f"CSV parsing extracted {len(approved_users)} users")
            
        else:
            # Original line-by-line parsing for backward compatibility
            logging.debug("Using line-by-line parsing")
            lines = cleaned_data.split('\n')
            logging.debug(f"License data split into {len(lines)} lines")
            
            approved_users = []
            
            for i, line in enumerate(lines):
                # Remove whitespace and skip empty lines
                username = line.strip()
                logging.debug(f"Line {i+1}: '{line}' -> cleaned: '{username}'")
                
                if username and not username.startswith('#'):  # Skip comments
                    approved_users.append(username)
                    logging.debug(f"Added user: '{username}' to approved list")
                elif username.startswith('#'):
                    logging.debug(f"Skipped comment line: '{username}'")
                elif not username:
                    logging.debug(f"Skipped empty line {i+1}")
        
        logging.info(f"Loaded {len(approved_users)} approved users from license file")
        logging.debug(f"Approved users list: {approved_users}")
        return approved_users
        
    except Exception as e:
        logging.error(f"Error parsing license data: {e}")
        return []


def is_developer_mode_enabled() -> bool:
    """
    Check if developer mode is enabled.
    
    Returns:
        Boolean indicating if developer mode is active
    """
    from .constants import DEVELOPER_MODE
    return DEVELOPER_MODE