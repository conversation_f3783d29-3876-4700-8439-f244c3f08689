"""
Basic Test for General Notes Package
====================================

Simple test to verify the package structure and basic functionality.

Author: Drawing Production System
"""

import sys
import logging
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_package_imports():
    """Test that all main package components can be imported."""
    try:
        # Test main package import
        import general_notes
        print("✓ Main package imported successfully")
        
        # Test core components
        from general_notes.core import ApplicationCore
        from general_notes.models import GeneralNotesConfig, NoteDetail, Boundary
        from general_notes.parsers import CoordinateParser
        print("✓ Core components imported successfully")
        
        # Test configuration creation
        config = GeneralNotesConfig.create_default()
        print(f"✓ Configuration created: {config.application_name} v{config.version}")
        
        # Test application core creation
        core = ApplicationCore(config)
        print("✓ Application core created successfully")
        
        # Test coordinate parsing
        parser = CoordinateParser()
        coord = parser.parse_coordinate("(100, 200, 0)")
        print(f"✓ Coordinate parsing works: {coord.to_tuple()}")
        
        return True
        
    except Exception as e:
        print(f"✗ Import test failed: {str(e)}")
        return False

def test_workflow_orchestrator():
    """Test workflow orchestrator creation."""
    try:
        from general_notes.core import ApplicationCore
        from general_notes.models import GeneralNotesConfig
        from general_notes.orchestrators import WorkflowOrchestrator
        
        config = GeneralNotesConfig.create_default()
        core = ApplicationCore(config)
        orchestrator = WorkflowOrchestrator(core)
        
        status = orchestrator.get_workflow_status()
        print(f"✓ Workflow orchestrator created, status: {status['orchestrator']['current_step'] or 'Ready'}")
        
        return True
        
    except Exception as e:
        print(f"✗ Workflow orchestrator test failed: {str(e)}")
        return False

def test_main_application():
    """Test main application entry point."""
    try:
        from general_notes import GeneralNotesGenerator
        from general_notes.models import GeneralNotesConfig
        
        config = GeneralNotesConfig.create_development()
        generator = GeneralNotesGenerator(config)
        
        print(f"✓ Main application created successfully")
        print(f"  - Application: {config.application_name}")
        print(f"  - Version: {config.version}")
        print(f"  - Debug mode: {config.debug_mode}")
        
        return True
        
    except Exception as e:
        print(f"✗ Main application test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("Testing General Notes Package")
    print("=" * 40)
    
    # Setup basic logging
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        ("Package Imports", test_package_imports),
        ("Workflow Orchestrator", test_workflow_orchestrator),
        ("Main Application", test_main_application)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        if test_func():
            passed += 1
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Package structure is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
