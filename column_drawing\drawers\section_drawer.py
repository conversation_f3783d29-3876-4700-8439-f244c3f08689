"""
Rebar Drawing Component
======================

Handles drawing of reinforcement bars, links, and stirrups for column sections
with professional layer management following AIA standards.
"""

import logging
from typing import List, Tuple, Optional
from ..models.drawing_config import DrawingConfig
from ..models.rebar_layer_data import RebarLayerData
from ..models.column_config import ColumnConfig
from ..models.zone_config import ZoneConfig, ZoneConfigSet
from ..calculators.rebar_calculator import RebarCalculator
from ..calculators.geometry_calculator import GeometryCalculator
from .dimension_drawer import DimensionDrawer
from .rebar.link_drawer import LinkDrawer
from ..io.dxf_writer import DXFWriter

# Import the new modular components
from .section.core_drawing import CoreDrawingMixin
from .section.zone_detail import ZoneDetailMixin
from .section.arrow_drawing import ArrowDrawingMixin

import math
from ezdxf.enums import TextEntityAlignment

logger = logging.getLogger(__name__)


class SectionDrawer(CoreDrawingMixin, ZoneDetailMixin, ArrowDrawingMixin):
    """
    Drawer for reinforcement elements with AIA layer management.

    This class handles:
    - Drawing rebar patterns and individual bars on appropriate layers
    - Drawing links and stirrups (BS8666 compliant) with proper layer assignment
    - Column outline drawing following AIA standards
    - Layer alignment and positioning with professional organization

    The class is now organized using modular mixins for improved maintainability:
    - CoreDrawingMixin: Basic drawing operations (outlines, rebar layers, links)
    - ZoneDetailMixin: Zone-specific operations (zone details, reinforcement, scaling)
    - ArrowDrawingMixin: Arrow drawing operations (link intersection arrows, annotations)
    """

    def __init__(self, doc, modelspace, config: DrawingConfig, rebar_calculator: RebarCalculator,
                 dxf_writer: Optional[DXFWriter] = None):
        """
        Initialize the rebar drawer with layer management.

        Args:
            doc: DXF document for dimension creation
            modelspace: DXF modelspace for drawing operations
            config: Drawing configuration object
            rebar_calculator: Rebar calculation component
            dxf_writer: DXF writer with layer management (optional for backward compatibility)
        """
        # Set required attributes before calling super().__init__()
        self.doc = doc
        self.msp = modelspace
        self.config = config
        self.rebar_calc = rebar_calculator
        self.dxf_writer = dxf_writer
        self.dimension_drawer = DimensionDrawer(
            doc, modelspace, config, dxf_writer)
        self.link_drawer = LinkDrawer(doc, modelspace, config, dxf_writer)

        # Initialize parent mixins (this will set up lazy initialization attributes)
        super().__init__()

    def draw_section_dimensions(
        self,
        x: float,
        y: float,
        width: float,
        height: float,
        actual_width: float,
        actual_height: float
    ) -> bool:
        """
        Draw dimension annotations for a column section.

        Args:
            x, y: Section bottom-left corner coordinates
            width, height: Scaled section dimensions
            actual_width, actual_height: Actual column dimensions (mm)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            return self.dimension_drawer.draw_section_dimensions(
                x, y, width, height, actual_width, actual_height
            )

        except Exception as e:
            logger.error(f"Error drawing section dimensions: {e}")
            return False
