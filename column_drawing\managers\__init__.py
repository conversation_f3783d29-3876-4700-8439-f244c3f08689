"""
Managers Module
==============

Contains manager classes for handling various aspects of drawing generation
including layer management, drawing organization, and system coordination.
"""

from .layer_manager import LayerManager
from .link_mark_manager import LinkMarkManager, get_global_link_mark_manager, reset_global_link_mark_manager

__all__ = ['LayerManager', 'LinkMarkManager',
           'get_global_link_mark_manager', 'reset_global_link_mark_manager']
