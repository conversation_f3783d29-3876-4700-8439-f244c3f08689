"""
Authentication Package
====================

This package provides comprehensive authentication functionality for the Drawing Production System.
Includes user authentication, session management, license validation, and secure email communication.

Modules:
    - constants: Authentication-related constants and configuration
    - email_service: Email functionality for passwords and logging
    - password_service: Password generation and validation
    - license_service: User license validation
    - session_manager: Session timeout and token management
    - ui_components: UI elements and forms for authentication

Author: Drawing Production System
Date: 2025
"""

# Core authentication functionality
from .constants import (
    # Security settings
    MAX_LOGIN_ATTEMPTS,
    LOGIN_TIMEOUT_MINUTES,
    SESSION_TOKEN_BYTES,
    DEVELOPER_MODE,
    
    # UI constants
    FONT_TITLE,
    FONT_SUBTITLE,
    FONT_NORMAL,
    FONT_SMALL,
    BUTTON_WIDTH_LARGE,
    BUTTON_HEIGHT_LARGE,
    BUTTON_WIDTH_MEDIUM,
    BUTTON_HEIGHT_MEDIUM,
    ENTRY_WIDTH,
    
    # Version info
    APP_VERSION
)

from .email_service import (
    send_password_email,
    send_email_log,
    send_email_log_sync,
    send_drawing_completion_log,
    send_drawing_completion_log_sync,
    log_security_event
)

from .password_service import (
    generate_password_key,
    generate_password_with_salt,
    validate_password,
    clear_password_data
)

from .license_service import (
    check_user_license,
    is_developer_mode_enabled
)

from .session_manager import (
    SessionManager,
    create_session_manager
)

from .ui_components import (
    create_login_screen,
    create_timer_display,
    create_menu_bar,
    show_error_message,
    show_info_message,
    show_warning_message,
    validate_username_input,
    validate_password_input,
    requires_login_decorator
)

# Package version
__version__ = "1.0.0"
__author__ = "Drawing Production System"

# Export all public components
__all__ = [
    # Constants
    'MAX_LOGIN_ATTEMPTS',
    'LOGIN_TIMEOUT_MINUTES',
    'SESSION_TOKEN_BYTES',
    'DEVELOPER_MODE',
    'FONT_TITLE',
    'FONT_SUBTITLE',
    'FONT_NORMAL',
    'FONT_SMALL',
    'BUTTON_WIDTH_LARGE',
    'BUTTON_HEIGHT_LARGE',
    'BUTTON_WIDTH_MEDIUM',
    'BUTTON_HEIGHT_MEDIUM',
    'ENTRY_WIDTH',
    'APP_VERSION',
    
    # Email service
    'send_password_email',
    'send_email_log',
    'send_email_log_sync',
    'send_drawing_completion_log',
    'send_drawing_completion_log_sync',
    'log_security_event',
    
    # Password service
    'generate_password_key',
    'generate_password_with_salt',
    'validate_password',
    'clear_password_data',
    
    # License service
    'check_user_license',
    'is_developer_mode_enabled',
    
    # Session manager
    'SessionManager',
    'create_session_manager',
    
    # UI components
    'create_login_screen',
    'create_timer_display',
    'create_menu_bar',
    'show_error_message',
    'show_info_message',
    'show_warning_message',
    'validate_username_input',
    'validate_password_input',
    'requires_login_decorator'
]