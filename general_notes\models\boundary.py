"""
Boundary Models
==============

Data models for representing and validating coordinate boundaries used in 
note detail selection from DXF databases.

This module provides classes for:
- Individual boundary points with coordinate validation
- Complete boundary definitions with geometric validation
- Boundary validation and error handling

Author: Drawing Production System
"""

from dataclasses import dataclass
from typing import List, Tuple, Optional
import logging
import math

logger = logging.getLogger(__name__)


class BoundaryValidationError(Exception):
    """Raised when boundary validation fails."""
    pass


@dataclass(frozen=True)
class BoundaryPoint:
    """
    Represents a single point in 3D space for boundary definition.
    
    Attributes:
        x: X coordinate in drawing units
        y: Y coordinate in drawing units  
        z: Z coordinate in drawing units (typically 0 for 2D drawings)
    """
    x: float
    y: float
    z: float = 0.0
    
    def __post_init__(self):
        """Validate coordinate values after initialization."""
        if not all(isinstance(coord, (int, float)) for coord in [self.x, self.y, self.z]):
            raise BoundaryValidationError("All coordinates must be numeric")
        
        # Check for reasonable coordinate ranges (prevent extreme values)
        max_coord = 1e6  # 1 million units
        if any(abs(coord) > max_coord for coord in [self.x, self.y, self.z]):
            raise BoundaryValidationError(f"Coordinates exceed maximum range: ±{max_coord}")
    
    def distance_to(self, other: 'BoundaryPoint') -> float:
        """
        Calculate Euclidean distance to another point.
        
        Args:
            other: Target point
            
        Returns:
            Distance in drawing units
        """
        return math.sqrt(
            (self.x - other.x) ** 2 + 
            (self.y - other.y) ** 2 + 
            (self.z - other.z) ** 2
        )
    
    def to_2d(self) -> Tuple[float, float]:
        """
        Convert to 2D coordinate tuple (x, y).
        
        Returns:
            Tuple of (x, y) coordinates
        """
        return (self.x, self.y)
    
    def to_3d(self) -> Tuple[float, float, float]:
        """
        Convert to 3D coordinate tuple (x, y, z).
        
        Returns:
            Tuple of (x, y, z) coordinates
        """
        return (self.x, self.y, self.z)
    
    @classmethod
    def from_string(cls, coord_str: str) -> 'BoundaryPoint':
        """
        Create BoundaryPoint from string format "(x, y, z)".
        
        Args:
            coord_str: Coordinate string in format "(x, y, z)"
            
        Returns:
            BoundaryPoint instance
            
        Raises:
            BoundaryValidationError: If string format is invalid
        """
        try:
            # Remove parentheses and split by comma
            clean_str = coord_str.strip().strip('()')
            parts = [part.strip() for part in clean_str.split(',')]
            
            if len(parts) != 3:
                raise ValueError(f"Expected 3 coordinates, got {len(parts)}")
            
            x, y, z = map(float, parts)
            return cls(x, y, z)
            
        except (ValueError, AttributeError) as e:
            raise BoundaryValidationError(f"Invalid coordinate string '{coord_str}': {e}")


@dataclass
class Boundary:
    """
    Represents a rectangular boundary defined by four corner points.
    
    The boundary is defined in counterclockwise order starting from bottom-left:
    - Point 1: Bottom-left corner
    - Point 2: Bottom-right corner  
    - Point 3: Top-right corner
    - Point 4: Top-left corner
    
    Attributes:
        point1: Bottom-left corner
        point2: Bottom-right corner
        point3: Top-right corner
        point4: Top-left corner
        title: Optional title for the boundary
    """
    point1: BoundaryPoint  # Bottom-left
    point2: BoundaryPoint  # Bottom-right
    point3: BoundaryPoint  # Top-right
    point4: BoundaryPoint  # Top-left
    title: Optional[str] = None
    
    def __post_init__(self):
        """Validate boundary after initialization."""
        self._validate_rectangular_shape()
        self._validate_counterclockwise_order()
    
    def _validate_rectangular_shape(self) -> None:
        """Validate that the four points form a rectangle."""
        # Check that opposite sides are parallel and equal
        # Bottom side (point1 to point2) should be parallel to top side (point4 to point3)
        bottom_vector = (self.point2.x - self.point1.x, self.point2.y - self.point1.y)
        top_vector = (self.point3.x - self.point4.x, self.point3.y - self.point4.y)
        
        # Left side (point1 to point4) should be parallel to right side (point2 to point3)
        left_vector = (self.point4.x - self.point1.x, self.point4.y - self.point1.y)
        right_vector = (self.point3.x - self.point2.x, self.point3.y - self.point2.y)
        
        # Check if vectors are parallel (cross product should be near zero)
        tolerance = 1e-6
        
        bottom_top_cross = abs(bottom_vector[0] * top_vector[1] - bottom_vector[1] * top_vector[0])
        left_right_cross = abs(left_vector[0] * right_vector[1] - left_vector[1] * right_vector[0])
        
        if bottom_top_cross > tolerance or left_right_cross > tolerance:
            logger.warning(f"Boundary may not be perfectly rectangular (tolerance: {tolerance})")
    
    def _validate_counterclockwise_order(self) -> None:
        """Validate that points are in counterclockwise order."""
        # Calculate the signed area using the shoelace formula
        points = [self.point1, self.point2, self.point3, self.point4]
        signed_area = 0.0
        
        for i in range(4):
            j = (i + 1) % 4
            signed_area += (points[j].x - points[i].x) * (points[j].y + points[i].y)
        
        if signed_area > 0:
            logger.warning("Boundary points may not be in counterclockwise order")
    
    @property
    def width(self) -> float:
        """Get the width of the boundary."""
        return abs(self.point2.x - self.point1.x)
    
    @property
    def height(self) -> float:
        """Get the height of the boundary."""
        return abs(self.point4.y - self.point1.y)
    
    @property
    def area(self) -> float:
        """Get the area of the boundary."""
        return self.width * self.height
    
    @property
    def center(self) -> BoundaryPoint:
        """Get the center point of the boundary."""
        center_x = (self.point1.x + self.point3.x) / 2
        center_y = (self.point1.y + self.point3.y) / 2
        center_z = (self.point1.z + self.point3.z) / 2
        return BoundaryPoint(center_x, center_y, center_z)
    
    @property
    def min_point(self) -> BoundaryPoint:
        """Get the minimum coordinate point (bottom-left)."""
        return self.point1
    
    @property
    def max_point(self) -> BoundaryPoint:
        """Get the maximum coordinate point (top-right)."""
        return self.point3
    
    def contains_point(self, point: BoundaryPoint) -> bool:
        """
        Check if a point is within the boundary.
        
        Args:
            point: Point to check
            
        Returns:
            True if point is within boundary
        """
        return (
            self.min_point.x <= point.x <= self.max_point.x and
            self.min_point.y <= point.y <= self.max_point.y
        )
    
    def to_polygon_2d(self) -> List[Tuple[float, float]]:
        """
        Convert boundary to 2D polygon representation.
        
        Returns:
            List of (x, y) coordinate tuples
        """
        return [
            self.point1.to_2d(),
            self.point2.to_2d(), 
            self.point3.to_2d(),
            self.point4.to_2d()
        ]
    
    @classmethod
    def from_excel_row(cls, row_data: dict) -> 'Boundary':
        """
        Create Boundary from Excel row data.
        
        Args:
            row_data: Dictionary with keys 'Title', 'Point1', 'Point2', 'Point3', 'Point4'
            
        Returns:
            Boundary instance
            
        Raises:
            BoundaryValidationError: If row data is invalid
        """
        try:
            title = row_data.get('Title', '').strip()
            
            point1 = BoundaryPoint.from_string(row_data['Point1'])
            point2 = BoundaryPoint.from_string(row_data['Point2'])
            point3 = BoundaryPoint.from_string(row_data['Point3'])
            point4 = BoundaryPoint.from_string(row_data['Point4'])
            
            return cls(point1, point2, point3, point4, title)
            
        except KeyError as e:
            raise BoundaryValidationError(f"Missing required column: {e}")
        except Exception as e:
            raise BoundaryValidationError(f"Error creating boundary from row data: {e}")
    
    def __str__(self) -> str:
        """String representation of the boundary."""
        title_str = f" ({self.title})" if self.title else ""
        return f"Boundary{title_str}: {self.width:.1f}×{self.height:.1f} at ({self.center.x:.1f}, {self.center.y:.1f})"
