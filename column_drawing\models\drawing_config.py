"""
Drawing Configuration
====================

Configuration settings for drawing parameters including fonts, colors,
line weights, and table coordinates.
"""

from ezdxf import colors
from typing import List, Tuple, Dict
from dataclasses import dataclass


@dataclass
class BaseDrawingConfig:
    """
    Base configuration class containing common parameters shared across multiple config classes.

    This class consolidates colors, text heights, line weights, and fonts that were
    previously duplicated across DrawingConfig, TableConfig, ZoneDetailConfig, etc.
    """

    # ==================== STANDARD COLORS ====================
    # AutoCAD Color Index values - single source of truth
    COLOR_BLACK: int = colors.BLACK
    COLOR_RED: int = colors.RED
    COLOR_BLUE: int = colors.BLUE
    COLOR_GREEN: int = colors.GREEN
    COLOR_CYAN: int = colors.CYAN
    COLOR_YELLOW: int = colors.YELLOW
    COLOR_MAGENTA: int = colors.MAGENTA
    COLOR_WHITE: int = colors.WHITE
    COLOR_GRAY: int = 8
    COLOR_LIGHT_GRAY: int = 9

    # ==================== STANDARD TEXT HEIGHTS ====================
    # Text heights in millimeters - single source of truth
    # NOTE: All text heights now have minimum of 80 units for DXF output legibility
    # Small text (zone detail dimensions) - minimum 80
    TEXT_HEIGHT_SMALL: float = 100.0
    # Standard text (table data, general dimensions)
    TEXT_HEIGHT_STANDARD: float = 100.0
    # Base text height (arrows, general text)
    TEXT_HEIGHT_BASE: float = 100.0
    # Medium text (zone labels, column marks)
    TEXT_HEIGHT_MEDIUM: float = 120.0
    TEXT_HEIGHT_LARGE: float = 150.0     # Large text (titles, headers)
    # Title text (floor values, major headers)
    TEXT_HEIGHT_TITLE: float = 200.0

    # ==================== PROFESSIONAL ENGINEERING FONTS ====================
    # Professional fonts for engineering drawings
    # Updated to use Arial Narrow for improved readability and professional appearance

    # Primary font: Arial Narrow - Professional narrow font for technical drawings
    # - Excellent readability and space efficiency
    # - Professional appearance for technical drawings
    # - Consistent character width and spacing
    # - Wide compatibility across systems
    # Professional engineering font (primary)
    FONT_ENGINEERING: str = 'Arial Narrow'

    # Alternative professional fonts for different contexts
    FONT_TECHNICAL: str = 'txt.shx'             # Technical drawings and annotations
    FONT_SIMPLEX: str = 'simplex.shx'           # Clean, simple font for labels

    # Unified font configuration - all text uses Arial Narrow
    FONT_STANDARD: str = FONT_ENGINEERING       # Standard text uses Arial Narrow
    FONT_TITLE: str = FONT_ENGINEERING          # Title text uses Arial Narrow
    FONT_DIMENSION: str = FONT_ENGINEERING      # Dimension text uses Arial Narrow

    # ==================== STANDARD LINE WEIGHTS ====================
    # Line weights in AutoCAD units (13 = 0.13mm, 100 = 1.00mm)
    # 0.13mm - Fine lines (dimensions, annotations)
    LINEWEIGHT_FINE: int = 13
    # 0.30mm - Light lines (reinforcement details)
    LINEWEIGHT_LIGHT: int = 30
    # 0.70mm - Medium lines (secondary elements)
    LINEWEIGHT_MEDIUM: int = 70
    # 1.00mm - Heavy lines (main structural elements)
    LINEWEIGHT_HEAVY: int = 100

    # ==================== PROFESSIONAL TEXT STYLE PARAMETERS ====================
    # Optimized for Arial Narrow font appearance
    # Based on best practices for narrow fonts in technical drawings
    # Optimal width factor for Arial Narrow readability
    TEXT_STYLE_WIDTH_FACTOR: float = 0.9
    # Height for Standard text style (0.0 = use specified height)
    TEXT_STYLE_HEIGHT: float = 0.0
    # No oblique angle for engineering drawings (straight text)
    TEXT_STYLE_OBLIQUE_ANGLE: float = 0.0

    # ==================== STANDARD TOLERANCES ====================
    GEOMETRY_TOLERANCE: float = 1.0          # General geometric tolerance (mm)
    # Tolerance for rebar position calculations (mm)             # Tolerance for rebar position calculations (mm)             # Tolerance for rebar position calculations (mm)
    REBAR_TOLERANCE: float = 1.0


class DrawingConfig(BaseDrawingConfig):
    """
    Configuration class for drawing parameters and constants.

    This class centralizes all drawing-related configuration including
    rebar parameters, table coordinates, dimension settings, and
    all numeric parameters used throughout the application.

    Inherits common parameters (colors, text heights, fonts, line weights)
    from BaseDrawingConfig to eliminate duplication.
    """

    # ==================== REBAR CONFIGURATION ====================

    # Basic rebar parameters
    # Minimum rebar radius for visibility (mm)
    REBAR_MIN_RADIUS = 8
    # Default radius for layer 1 rebar calculations (mm)
    REBAR_DEFAULT_LAYER1_RADIUS = 10.0
    # Default radius for link calculations (mm)
    REBAR_DEFAULT_LINK_RADIUS = 8.0

    # Rebar spacing and positioning
    # Typical spacing between rebar layers (mm)
    LAYER_SPACING = 25
    # Tolerance for rebar position calculations (mm)
    REBAR_TOLERANCE = 1.0
    # Minimum clear spacing between rebars (mm)
    REBAR_MIN_CLEAR_SPACING = 20
    # Minimum clear spacing for column size validation (mm)
    REBAR_MIN_COLUMN_SPACING = 100

    # Link/stirrup configuration
    LINK_CORNER_ANGLES = {
        # Bottom-left quarter circle angles
        'bottom_left': {'start': 180, 'end': 270},
        # Bottom-right quarter circle angles
        'bottom_right': {'start': 270, 'end': 0},
        # Top-right quarter circle angles
        'top_right': {'start': 0, 'end': 90},
        # Top-left quarter circle angles
        'top_left': {'start': 90, 'end': 180}
    }

    # 25a Link parameters (BS8666 Shape Code 25a)
    LINK_25A_EXTENSION_A = 50         # Extension A length for 25a links (mm)
    LINK_25A_EXTENSION_C = 50         # Extension C length for 25a links (mm)
    # Point adjustment along gravity axis (mm)
    LINK_25A_POINT_ADJUSTMENT = 5

    # ==================== OVERLAP EXTENSION CONFIGURATION ====================

    # Overlap extension parameters (Part 2 of BS8666 Shape Code 52)
    # X-direction offset from corner rebar (mm)
    OVERLAP_OFFSET_X = -30
    # Y-direction offset from corner rebar (mm)
    OVERLAP_OFFSET_Y = -30
    # Length of each overlap extension line (mm)
    OVERLAP_LINE_LENGTH = 150
    OVERLAP_ANGLE_DEGREES = 225       # S45W direction angle in degrees

    # NOTE: DXF cleanup parameters removed as they were unused in the codebase

    # ==================== GEOMETRY CALCULATION PARAMETERS ====================

    # Scale and sizing parameters
    SCALE_MAX = 2.5                   # Maximum scale factor for sections
    SCALE_MIN = 0.1                   # Minimum scale factor to avoid division by zero
    GEOMETRY_TOLERANCE = 1.0          # General geometric tolerance (mm)

    # ==================== TABLE LAYOUT CONFIGURATION ====================
    # NOTE: Table dimensions are now managed by TableConfig class
    # These constants are maintained for backward compatibility but reference TableConfig

    # Table dimensions (ASD format) - DEPRECATED: Use TableConfig.get_table_width/height()
    @classmethod
    def get_table_width(cls) -> float:
        """DEPRECATED: Use TableConfig.get_table_width() instead."""
        from .table_config import TableConfig
        return TableConfig().get_table_width()

    @classmethod
    def get_table_height(cls) -> float:
        """DEPRECATED: Use TableConfig.get_table_height() instead."""
        from .table_config import TableConfig
        return TableConfig().get_table_height()

    # Backward compatibility constants
    TABLE_WIDTH = 6500                # DEPRECATED: Use get_table_width() or TableConfig
    TABLE_HEIGHT = 12100              # DEPRECATED: Use get_table_height() or TableConfig

    # Table column widths (ASD format) - kept for backward compatibility
    TITLE_COLUMN_WIDTH = 1000         # Width of title/header column (mm)
    DETAIL_COLUMN_WIDTH = 3500        # Width of zone detail column (mm)
    ELEVATION_COLUMN_WIDTH = 2000     # Width of elevation column (mm)

    # Table row heights (ASD format) - kept for backward compatibility
    HEADER_ROW_HEIGHT = 1200          # Height of header row (mm)
    ZONE_ROW_HEIGHT = 3500            # Height of zone detail rows (mm)
    DATA_ROW_HEIGHT = 200             # Height of data rows (mm)

    # Cell content positioning
    # Margin within table cells for content (mm)
    CELL_CONTENT_MARGIN = 600

    # Table text positioning offsets (ASD format) - DEPRECATED: Use TableConfig methods
    # These constants reference BaseDrawingConfig values for backward compatibility
    # DEPRECATED: Use TableConfig.get_title_column_mark_text_height()
    TITLE_COLUMN_MARK_TEXT_HEIGHT = BaseDrawingConfig.TEXT_HEIGHT_MEDIUM
    # DEPRECATED: Use TableConfig.get_title_column_mark_gap()
    TITLE_COLUMN_MARK_GAP = 150
    # DEPRECATED: Use TableConfig.get_title_floor_mark_text_height()
    TITLE_FLOOR_MARK_TEXT_HEIGHT = BaseDrawingConfig.TEXT_HEIGHT_MEDIUM
    # NOTE: TRIANGULAR_CELL_TEXT_HEIGHT removed as it was unused (legacy constant)
    TITLE_COLUMN_MARK_Y_OFFSET = 100       # Upward shift for column mark title text
    # Height for zone detail labels
    ZONE_DETAIL_LABEL_HEIGHT = BaseDrawingConfig.TEXT_HEIGHT_LARGE
    # Y offset for zone detail label positioning
    ZONE_DETAIL_LABEL_Y_OFFSET = 2062.5
    # DEPRECATED: Use TableConfig.get_table_data_text_height()
    TABLE_DATA_TEXT_HEIGHT = BaseDrawingConfig.TEXT_HEIGHT_STANDARD
    # DEPRECATED: Use TableConfig.get_table_data_y_offset()
    TABLE_DATA_Y_OFFSET = 100
    # Height for "FLOOR" title text
    TITLE_FLOOR_TEXT_HEIGHT = BaseDrawingConfig.TEXT_HEIGHT_LARGE
    TITLE_FLOOR_X_OFFSET = 50              # Left shift for floor title
    VALUE_FLOOR_LABEL_Y_OFFSET = 562.5     # Y offset for floor value positioning
    # DEPRECATED: Use TableConfig.get_value_floor_text_height()
    VALUE_FLOOR_TEXT_HEIGHT = BaseDrawingConfig.TEXT_HEIGHT_TITLE
    VALUE_FLOOR_LINE_SPACING = 400         # Spacing between floor text lines
    VALUE_FLOOR_CELL_CENTER_Y_OFFSET = 2000  # Y offset for floor cell center

    # Table column positioning constants (ASD format)
    # Title column center offset
    TITLE_COLUMN_CENTER_OFFSET = 500.0
    DETAIL_COLUMN_CENTER_OFFSET = TITLE_COLUMN_WIDTH + \
        (DETAIL_COLUMN_WIDTH / 2)  # Detail column center offset
    # Elevation column center offset
    ELEVATION_COLUMN_CENTER_OFFSET = (ELEVATION_COLUMN_WIDTH / 2)

    # Elevation cell specific heights
    # Upper elevation cell height (mm)
    UPPER_ELEVATION_CELL_HEIGHT = HEADER_ROW_HEIGHT
    CURRENT_ELEVATION_CELL_HEIGHT = ZONE_ROW_HEIGHT * 4 + \
        DATA_ROW_HEIGHT * 2  # Current elevation cell height (mm)

    # Elevation drawing configuration
    # Distance between main and parallel rebar (mm)
    ELEVATION_REBAR_PARALLEL_OFFSET = 50
    # Horizontal offset for diagonal rebar (mm)
    ELEVATION_REBAR_DIAGONAL_HORIZONTAL_OFFSET = 50
    # Vertical offset for diagonal rebar (mm)
    ELEVATION_REBAR_DIAGONAL_VERTICAL_OFFSET = 100
    # Distance of dimension line from rebar (mm)
    ELEVATION_DIMENSION_OFFSET = 150

    # Elevation cell positioning and sizing
    # Rebar X position as ratio of cell width (1/5 from left)
    ELEVATION_REBAR_X_POSITION_RATIO = 0.2
    # Triangle X position as ratio of cell width (1/5 from right)
    ELEVATION_TRIANGLE_X_POSITION_RATIO = 0.8
    # Horizontal line position as ratio of cell height (4/5)
    ELEVATION_HORIZONTAL_LINE_HEIGHT_RATIO = 0.8
    # Horizontal line start as ratio of cell width
    ELEVATION_LINE_START_X_RATIO = 0.5
    # Offset from right edge for horizontal line end (mm)
    ELEVATION_LINE_END_OFFSET = 100
    # Length of horizontal line for beam soffit level (mm)
    ELEVATION_HORIZONTAL_LINE_LENGTH = 200

    # Elevation text positioning
    # Y offset for level text above triangles (mm)
    ELEVATION_LEVEL_TEXT_Y_OFFSET = 150
    # Height for level text (mm) - minimum 80
    ELEVATION_LEVEL_TEXT_HEIGHT = 100
    # Y offset for first description line (mm)
    ELEVATION_DESC_LINE1_Y_OFFSET = 320
    # Y offset for second description line (mm)
    ELEVATION_DESC_LINE2_Y_OFFSET = 200
    # Y offset for third description line (mm)
    ELEVATION_DESC_LINE3_Y_OFFSET = 440
    # Height for description text (mm) - minimum 80
    ELEVATION_DESC_TEXT_HEIGHT = 100

    # Elevation triangle geometry
    # Width of triangular floor markers (mm)
    ELEVATION_TRIANGLE_WIDTH = 200
    # Height of triangular floor markers (mm)
    ELEVATION_TRIANGLE_HEIGHT = 160

    # Upper floor elevation cell positioning
    # Start position ratio (bottom of cell)
    ELEVATION_UPPER_REBAR_START_RATIO = 1.0
    # End position ratio (middle of cell)
    ELEVATION_UPPER_REBAR_END_RATIO = 0.5

    # Link zone dimension settings
    # Spacer height between link zones (mm)
    ELEVATION_LINK_ZONE_A_SPACER_HEIGHT = 50

    # ==================== DIMENSION SETTINGS ====================

    # Height of dimension text
    DIMENSION_TEXT_HEIGHT = BaseDrawingConfig.TEXT_HEIGHT_STANDARD
    DIMENSION_ARROW_SIZE = 120        # Size of dimension arrows
    DIMENSION_EXTENSION_LENGTH = 50   # Extension line length beyond dimension line
    DIMENSION_OFFSET = 20            # Offset of extension lines from measurement points
    DIMENSION_GAP = 100               # Gap between dimension text and line
    # Gap between multiple dimension lines (1st row, 2nd row data)
    DIMENSION_LINE_SPACING = 200

    # ==================== ZONE CELL DETAIL CONFIGURATION ====================

    # Zone detail cell dimensions for embedded drawings
    # Width of zone detail cells (detail column width)
    ZONE_DETAIL_CELL_WIDTH = 3500
    # Height of zone detail cells (zone row height)
    ZONE_DETAIL_CELL_HEIGHT = 3500
    # Margin for zone detail drawings within cells
    ZONE_DETAIL_DRAWING_MARGIN = 300
    # Space reserved for zone labels and descriptions
    ZONE_DETAIL_LABEL_SPACE = 400
    ZONE_DETAIL_DRAWING_MAX_WIDTH = ZONE_DETAIL_CELL_WIDTH - \
        (2 * ZONE_DETAIL_DRAWING_MARGIN)   # 2900
    ZONE_DETAIL_DRAWING_MAX_HEIGHT = ZONE_DETAIL_CELL_HEIGHT - \
        (2 * ZONE_DETAIL_DRAWING_MARGIN) - ZONE_DETAIL_LABEL_SPACE  # 2500

    # Zone detail drawing scaling parameters - Real scale (1:1) approach
    # Real scale (1:1) - preferred scale for zone drawings
    ZONE_DETAIL_DRAWING_SCALE_REAL = 1.0
    # Maximum scale for zone detail drawings (never scale up)
    ZONE_DETAIL_DRAWING_SCALE_MAX = 1.0
    # Minimum scale for zone detail drawings (maintains readability)
    ZONE_DETAIL_DRAWING_SCALE_MIN = 0.2

    # Zone detail text sizing
    # Height for zone detail labels
    ZONE_DETAIL_LABEL_TEXT_HEIGHT = BaseDrawingConfig.TEXT_HEIGHT_MEDIUM
    # Smaller dimensions for zone detail drawings
    ZONE_DETAIL_DIMENSION_TEXT_HEIGHT = BaseDrawingConfig.TEXT_HEIGHT_SMALL
    # Spacing below zone details for labels (mm)
    ZONE_DETAIL_LABEL_SPACING = 200

    # ==================== APPLICATION SETTINGS ====================

    # Drawing generation parameters
    DRAWING_START_Y_POSITION = 0  # Initial Y position for first drawing

    # ==================== COORDINATE TABLES ====================

    @classmethod
    def get_table_coordinates(cls) -> List[Tuple[Tuple[float, float, float], Tuple[float, float, float]]]:
        """
        Get the precise table coordinate lines for ASD format.

        Returns:
            List of coordinate pairs defining table structure lines.
            Each pair contains ((start_x, start_y, start_z), (end_x, end_y, end_z))
        """
        return [
            ((1000, -1200, 0), (0, -1200, 0)),
            ((0, -1200, 0), (0, 0, 0)),
            ((1000, -1200, 0), (1000, 0, 0)),
            ((4500, -1200, 0), (6500, -1200, 0)),
            ((6500, 0, 0), (4500, 0, 0)),
            ((1000, 0, 0), (4500, 0, 0)),
            ((1000, -1200, 0), (4500, -1200, 0)),
            ((1000, -4700, 0), (1000, -1200, 0)),
            ((4500, -1200, 0), (4500, -4700, 0)),
            ((1000, -4700, 0), (4500, -4700, 0)),
            ((0, -11700, 0), (0, -11900, 0)),
            ((0, -11900, 0), (0, -12100, 0)),
            ((1000, -11900, 0), (1000, -11700, 0)),
            ((1000, -12100, 0), (1000, -11900, 0)),
            ((4500, -12100, 0), (0, -12100, 0)),
            ((4500, -11700, 0), (4500, -12100, 0)),
            ((1000, -11700, 0), (1000, -8200, 0)),
            ((4500, -8200, 0), (4500, -11700, 0)),
            ((1000, -8200, 0), (4500, -8200, 0)),
            ((1000, -11700, 0), (1000, -8200, 0)),
            ((1000, -4700, 0), (1000, -8200, 0)),
            ((4500, -4700, 0), (4500, -8200, 0)),
            ((1000, -4700, 0), (4500, -4700, 0)),
            ((0, -1200, 0), (0, -11700, 0)),
            ((4500, -12100, 0), (6500, -12100, 0)),
            ((4500, -1200, 0), (4500, 0, 0)),
            ((1000, 0, 0), (0, 0, 0)),
            ((4500, -11700, 0), (1000, -11700, 0)),
            ((4500, -11900, 0), (1000, -11900, 0)),
            ((0, -11700, 0), (1000, -11700, 0)),
            ((0, -11900, 0), (1000, -11900, 0)),
            ((0, 0, 0), (1000, -1200, 0)),
            ((6500, -12100, 0), (6500, -1200, 0)),
            ((6500, 0, 0), (6500, -1200, 0))
        ]

    @classmethod
    def get_cell_positions(cls) -> dict:
        """
        Get standardized cell positions for table content (ASD format).

        DEPRECATED: Use ASDTableCellConfig for new implementations.
        This method is maintained for backward compatibility.

        Returns:
            dict: Cell position coordinates for different table areas
        """
        return {
            # Column X positions
            'title_column_x': 0,
            'detail_column_x': cls.TITLE_COLUMN_WIDTH,
            'elevation_column_x': cls.TITLE_COLUMN_WIDTH + cls.DETAIL_COLUMN_WIDTH,

            # Row Y positions
            'header_row_y': 0,
            'zone_d_row_y': -cls.HEADER_ROW_HEIGHT,
            'zone_c_row_y': -cls.HEADER_ROW_HEIGHT - cls.ZONE_ROW_HEIGHT,
            'zone_b_row_y': -cls.HEADER_ROW_HEIGHT - (2 * cls.ZONE_ROW_HEIGHT),
            'zone_a_row_y': -cls.HEADER_ROW_HEIGHT - (3 * cls.ZONE_ROW_HEIGHT),
            'main_bar_row_y': -cls.HEADER_ROW_HEIGHT - (3 * cls.ZONE_ROW_HEIGHT) - cls.DATA_ROW_HEIGHT,
            'size_row_y': -cls.HEADER_ROW_HEIGHT - (3 * cls.ZONE_ROW_HEIGHT) - (2 * cls.DATA_ROW_HEIGHT),

            # Cell dimensions (backward compatibility)
            'elevation_cell_width': cls.ELEVATION_COLUMN_WIDTH,
            'zone_detail_cell_height': cls.ZONE_ROW_HEIGHT,
        }

    @classmethod
    def get_zone_cell_bounds(cls, zone: str, cell_x: float, cell_y: float) -> dict:
        """
        Get the bounds for a specific zone cell for detail drawing.

        DEPRECATED: This method is maintained for backward compatibility.
        Use ASDTableCellConfig.get_zone_cell_bounds() for new implementations.

        Args:
            zone: Zone identifier ('A', 'B', 'C', or 'D')
            cell_x: Actual X coordinate of the zone detail cell (detail column position)
            cell_y: Actual Y coordinate of the zone detail cell (zone row position)

        Returns:
            dict: Zone cell bounds with keys: x, y, width, height, center_x, center_y
        """
        if zone not in ['A', 'B', 'C', 'D']:
            raise ValueError(f"Invalid zone: {zone}. Must be A, B, C, or D")

        # Use the zone detail cell dimensions
        cell_width = cls.ZONE_DETAIL_CELL_WIDTH
        cell_height = cls.ZONE_DETAIL_CELL_HEIGHT

        # Calculate center point of the zone detail cell
        center_x = cell_x + cell_width / 2
        center_y = cell_y - cell_height / 2  # Negative because Y decreases downward

        # Calculate drawing area bounds within the zone detail cell
        drawing_area_x = cell_x + cls.ZONE_DETAIL_DRAWING_MARGIN
        drawing_area_y = cell_y - cls.ZONE_DETAIL_DRAWING_MARGIN - cls.ZONE_DETAIL_LABEL_SPACE
        drawing_area_width = cls.ZONE_DETAIL_DRAWING_MAX_WIDTH
        drawing_area_height = cls.ZONE_DETAIL_DRAWING_MAX_HEIGHT

        # Calculate center of the drawing area (not the cell center)
        drawing_center_x = drawing_area_x + drawing_area_width / 2
        drawing_center_y = drawing_area_y - drawing_area_height / 2

        return {
            'x': cell_x,
            'y': cell_y,
            'width': cell_width,
            'height': cell_height,
            'center_x': center_x,  # Zone detail cell center
            'center_y': center_y,  # Zone detail cell center
            'detail_area_x': drawing_area_x,
            'detail_area_y': drawing_area_y,
            'detail_area_width': drawing_area_width,
            'detail_area_height': drawing_area_height,
            'detail_center_x': drawing_center_x,  # Zone detail drawing area center
            'detail_center_y': drawing_center_y,  # Zone detail drawing area center
            # Area reserved for zone labels
            'label_area_y': cell_y - cls.ZONE_DETAIL_DRAWING_MARGIN,
            'label_area_height': cls.ZONE_DETAIL_LABEL_SPACE
        }

    @classmethod
    def get_table_cell_config(cls):
        """
        Get an instance of the ASD table cell configuration.

        Returns:
            ASDTableCellConfig: Centralized table cell configuration
        """
        # Import here to avoid circular imports
        from .table_cell_config import ASDTableCellConfig
        return ASDTableCellConfig()

    @classmethod
    def get_dimension_style_config(cls) -> dict:
        """
        Get configuration for AutoCAD dimension style.

        Returns:
            dict: Dimension style configuration parameters
        """
        base_config = BaseDrawingConfig()
        return {
            'dimtxt': cls.DIMENSION_TEXT_HEIGHT,      # Text height
            'dimasz': cls.DIMENSION_ARROW_SIZE,       # Arrow size
            # Tick size (0 = use arrows)
            'dimtsz': 0.0,
            'dimblk': "",                             # Default arrows
            'dimtad': 1,                              # Text above dimension line
            'dimgap': cls.DIMENSION_GAP,              # Text-line gap
            'dimexe': cls.DIMENSION_EXTENSION_LENGTH,  # Extension length
            'dimexo': cls.DIMENSION_OFFSET,           # Extension offset
            'dimclrt': base_config.COLOR_BLACK,       # Text color
            'dimclrd': base_config.COLOR_BLACK,       # Dimension line color
            'dimclre': base_config.COLOR_BLACK,       # Extension line color
            'dimdec': 0,                              # Decimal places
            'dimzin': 8,                              # Suppress trailing zeros
            'dimlfac': 1.0,                           # Linear factor
            # Spacing between multiple dimension lines
            'dim_line_spacing': cls.DIMENSION_LINE_SPACING,
        }

    @classmethod
    def get_rebar_colors(cls) -> dict:
        """
        Get color scheme for different rebar layers.

        Returns:
            dict: Color assignments for rebar elements
        """
        base_config = BaseDrawingConfig()
        return {
            'layer1': base_config.COLOR_RED,      # Primary reinforcement
            'layer2': base_config.COLOR_RED,     # Secondary reinforcement
            'links': base_config.COLOR_GREEN,     # Links/stirrups
            'outline': base_config.COLOR_BLACK,   # Column outline
        }

    @classmethod
    def get_text_styles(cls) -> dict:
        """
        Get professional text style configurations with Arial Narrow font.

        Returns:
            dict: Text style parameters - consolidated to use Arial Narrow font
        """
        base_config = BaseDrawingConfig()

        # Professional Arial Narrow text style configuration
        # Uses Arial Narrow font for improved readability and professional appearance
        professional_config = {
            # Arial Narrow for professional appearance
            'font': base_config.FONT_ENGINEERING,
            # 0.9 for optimal Arial Narrow readability
            'width_factor': base_config.TEXT_STYLE_WIDTH_FACTOR,
            'height': base_config.TEXT_STYLE_HEIGHT,                 # Variable height
            # 0.0 for straight professional text
            'oblique_angle': base_config.TEXT_STYLE_OBLIQUE_ANGLE,
        }

        return {
            'standard': professional_config,
            # Legacy compatibility - all point to same Arial Narrow config
            'engineering': professional_config,
            'title': professional_config,
            'dimension': professional_config
        }

    @classmethod
    def get_overlap_extension_config(cls) -> dict:
        """
        Get configuration for overlap extensions in BS8666 Shape Code 52.

        Returns:
            dict: Parameters for drawing overlap extensions
        """
        import math
        return {
            'offset_x': cls.OVERLAP_OFFSET_X,
            'offset_y': cls.OVERLAP_OFFSET_Y,
            'line_length': cls.OVERLAP_LINE_LENGTH,
            'angle_radians': math.radians(cls.OVERLAP_ANGLE_DEGREES),
            'angle_degrees': cls.OVERLAP_ANGLE_DEGREES
        }

    # NOTE: get_dxf_cleanup_config method removed as DXF cleanup parameters were unused

    @classmethod
    def get_elevation_config(cls) -> Dict[str, float]:
        """
        Get elevation drawing configuration parameters.

        Returns:
            Dict[str, float]: Dictionary of elevation-specific configuration values
        """
        return {
            # Positioning ratios
            'rebar_x_position_ratio': cls.ELEVATION_REBAR_X_POSITION_RATIO,
            'triangle_x_position_ratio': cls.ELEVATION_TRIANGLE_X_POSITION_RATIO,
            'horizontal_line_height_ratio': cls.ELEVATION_HORIZONTAL_LINE_HEIGHT_RATIO,
            'line_start_x_ratio': cls.ELEVATION_LINE_START_X_RATIO,

            # Offsets and dimensions
            'rebar_parallel_offset': cls.ELEVATION_REBAR_PARALLEL_OFFSET,
            'rebar_diagonal_horizontal_offset': cls.ELEVATION_REBAR_DIAGONAL_HORIZONTAL_OFFSET,
            'rebar_diagonal_vertical_offset': cls.ELEVATION_REBAR_DIAGONAL_VERTICAL_OFFSET,
            'dimension_offset': cls.ELEVATION_DIMENSION_OFFSET,
            'line_end_offset': cls.ELEVATION_LINE_END_OFFSET,

            # Text positioning
            'level_text_y_offset': cls.ELEVATION_LEVEL_TEXT_Y_OFFSET,
            'desc_line1_y_offset': cls.ELEVATION_DESC_LINE1_Y_OFFSET,
            'desc_line2_y_offset': cls.ELEVATION_DESC_LINE2_Y_OFFSET,
            'desc_line3_y_offset': cls.ELEVATION_DESC_LINE3_Y_OFFSET,

            # Text heights
            'level_text_height': cls.ELEVATION_LEVEL_TEXT_HEIGHT,
            'desc_text_height': cls.ELEVATION_DESC_TEXT_HEIGHT,

            # Triangle geometry
            'triangle_width': cls.ELEVATION_TRIANGLE_WIDTH,
            'triangle_height': cls.ELEVATION_TRIANGLE_HEIGHT,

            # Upper floor positioning
            'upper_rebar_start_ratio': cls.ELEVATION_UPPER_REBAR_START_RATIO,
            'upper_rebar_end_ratio': cls.ELEVATION_UPPER_REBAR_END_RATIO,
        }

    # ==================== DIMENSION SETTINGS ====================
