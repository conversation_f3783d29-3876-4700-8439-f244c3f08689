"""
Data Models Package
==================

Contains data models and configuration classes for general notes drawing generation.

This package provides the core data structures used throughout the application,
following dataclass patterns established in the column_drawing package.

Modules:
    - note_detail: Note detail data structure with boundary information
    - configuration: Application configuration classes
    - selection: User selection state management
    - boundary: Boundary coordinate representation and validation
"""

from .note_detail import NoteDetail, NoteDetailCollection
from .configuration import (
    GeneralNotesConfig,
    DatabaseConfig,
    LayoutConfig,
    OutputConfig
)
from .selection import SelectionState, SelectionCriteria
from .boundary import Boundary, BoundaryPoint, BoundaryValidationError

__all__ = [
    # Note detail models
    'NoteDetail',
    'NoteDetailCollection',
    
    # Configuration models
    'GeneralNotesConfig',
    'DatabaseConfig', 
    'LayoutConfig',
    'OutputConfig',
    
    # Selection models
    'SelectionState',
    'SelectionCriteria',
    
    # Boundary models
    'Boundary',
    'BoundaryPoint',
    'BoundaryValidationError'
]
