"""
Link Mark Coordinator Module
=============================

Handles the coordination of link mark assignment and management
for the column drawing application.
"""

import logging
from typing import List, Tuple, Dict

from ..models.column_config import ColumnConfig
from ..models.zone_config import ZoneConfigSet
from ..processors.column_sorter import ColumnSorter

logger = logging.getLogger(__name__)


class LinkMarkCoordinator:
    """
    Coordinates link mark assignment and management.

    This class handles the pre-assignment of link marks in the correct
    order and provides summary reporting functionality.
    """

    def __init__(self, column_sorter: ColumnSorter):
        """
        Initialize the link mark coordinator.

        Args:
            column_sorter: Column sorting component
        """
        self.column_sorter = column_sorter

    def pre_assign_link_marks_for_all_groups(self, column_groups_by_mark: Dict[str, List[Tuple[ColumnConfig, ZoneConfigSet]]]) -> None:
        """
        Pre-assign all link marks in the correct order before drawing begins.

        Processing order follows engineering drawing standards:
        1. Group by column mark (already done)
        2. Within each column mark, sort by floor level (ascending)
        3. Within each floor level, process zones A→B→C→D
        4. Within each zone, process 52 links first, then 25a links

        Args:
            column_groups_by_mark: Dictionary mapping column marks to their table data
        """
        try:
            from ..managers.link_mark_manager import get_global_link_mark_manager
            link_mark_manager = get_global_link_mark_manager()

            logger.info(
                f"Pre-assigning link marks for {len(column_groups_by_mark)} column groups")

            # Process each column mark independently for clear organization
            for column_mark, column_group_data in column_groups_by_mark.items():

                # Sort this column mark's data by floor level for consistency
                floor_sorted_group = sorted(
                    column_group_data, key=lambda item: self.column_sorter.get_floor_level_for_sorting(item[0]))

                # Process each floor level within this column mark
                for column_config, zone_config_set in floor_sorted_group:

                    # Process zones in standard order A→B→C→D
                    standard_zone_order = ['A', 'B', 'C', 'D']

                    # Use the column_config from the current iteration (already correct)
                    # No need for additional lookup - column_config is already the right one

                    for zone_id in standard_zone_order:
                        zone_config = zone_config_set.get_zone(zone_id)

                        # Import BS8666 length calculation functions
                        from ..drawers.rebar.bs8666_shapes import calculate_shape_52_length, calculate_shape_25a_length

                        # 1. Process 52 link first within this zone
                        if zone_config.outer_link_diameter > 0:
                            # Calculate actual BS8666 Shape 52 length
                            corner_rebars = [
                                # Bottom-left
                                (50, 50),
                                # Bottom-right  # Fixed: use 'B' instead of 'width'
                                (getattr(column_config, 'B', 1000.0) - 50, 50),
                                # Top-right  # Fixed: use 'B' and 'D'
                                (getattr(column_config, 'B', 1000.0) - 50,
                                 getattr(column_config, 'D', 1000.0) - 50),
                                # Top-left  # Fixed: use 'D' instead of 'depth'
                                (50, getattr(column_config, 'D', 1000.0) - 50)
                            ]
                            stirrup_length = calculate_shape_52_length(
                                corner_rebars=corner_rebars,
                                rebar_diameter=getattr(
                                    column_config, 'dia1', 20),
                                link_diameter=zone_config.outer_link_diameter
                            )

                            mark_52_data = link_mark_manager.get_or_assign_mark(
                                category="52",
                                diameter=zone_config.outer_link_diameter,
                                floor_level=column_config.floor,
                                zone_id=zone_id,
                                column_mark=column_config.name,  # Use actual column name, not group key
                                length=stirrup_length
                            )

                        # 2. Process 25A Y-direction link second within this zone
                        if zone_config.has_inner_links() and zone_config.inner_link_diameter > 0:
                            # Calculate actual BS8666 Shape 25A Y-direction (horizontal) length
                            start_horizontal = (0, 100)
                            # Fixed: use 'B' instead of 'width'
                            end_horizontal = (
                                getattr(column_config, 'B', 1000.0), 100)

                            y_length = calculate_shape_25a_length(
                                start_point=start_horizontal,
                                end_point=end_horizontal,
                                rebar_diameter=getattr(
                                    column_config, 'dia1', 20),
                                link_diameter=zone_config.inner_link_diameter
                            )

                            mark_25a_y_data = link_mark_manager.get_mark_for_25a_link_y(
                                inner_diameter=zone_config.inner_link_diameter,
                                floor_level=column_config.floor,
                                zone_id=zone_id,
                                column_mark=column_config.name,  # Use actual column name, not group key
                                y_length=y_length
                            )

                        # 3. Process 25A X-direction link third within this zone
                        if zone_config.has_inner_links() and zone_config.inner_link_diameter > 0:
                            # Calculate actual BS8666 Shape 25A X-direction (vertical) length
                            start_vertical = (100, 0)
                            # Fixed: use 'D' instead of 'depth'
                            end_vertical = (100, getattr(
                                column_config, 'D', 1000.0))

                            x_length = calculate_shape_25a_length(
                                start_point=start_vertical,
                                end_point=end_vertical,
                                rebar_diameter=getattr(
                                    column_config, 'dia1', 20),
                                link_diameter=zone_config.inner_link_diameter
                            )

                            mark_25a_x_data = link_mark_manager.get_mark_for_25a_link_x(
                                inner_diameter=zone_config.inner_link_diameter,
                                floor_level=column_config.floor,
                                zone_id=zone_id,
                                column_mark=column_config.name,  # Use actual column name, not group key
                                x_length=x_length
                            )

            # Log completion summary
            total_columns = sum(len(group_data)
                                for group_data in column_groups_by_mark.values())
            logger.info(
                f"Link mark pre-assignment completed for {total_columns} columns")

        except Exception as e:
            logger.error(f"Error in link mark pre-assignment: {e}")
            raise

    def log_link_mark_summary(self):
        """Log a summary of link mark assignments."""
        try:
            from ..managers.link_mark_manager import get_global_link_mark_manager

            manager = get_global_link_mark_manager()
            summary = manager.get_assignment_summary()

            logger.info(f"Link Mark Assignment Summary:")
            logger.info(
                f"  Total column marks: {summary['total_column_marks']}")
            logger.info(
                f"  Total unique link types: {summary['total_unique_link_types']}")
            logger.info(
                f"  Total marks assigned: {summary['total_marks_assigned']}")

            # Log by column mark
            for column_mark, column_summary in summary['column_mark_summaries'].items():
                logger.info(f"  Column {column_mark}:")
                logger.info(
                    f"    Unique link types: {column_summary['total_unique_link_types']}")
                logger.info(
                    f"    Marks assigned: {column_summary['total_marks_assigned']}")
                logger.info(
                    f"    Next available mark: {column_summary['next_available_mark']}")

                # Log by category for this column mark
                for category, links in column_summary['link_types'].items():
                    logger.info(
                        f"    {category} links: {len(links)} unique diameters")
                    for link_info in links:
                        logger.info(
                            f"      D{link_info['diameter']}mm -> {link_info['mark_text']} (first: {link_info['first_occurrence']})")

            print(
                f"Link mark assignment complete: {summary['total_marks_assigned']} marks assigned ({summary['total_unique_link_types']} unique link types)")

        except Exception as e:
            logger.error(f"Error logging link mark summary: {e}")
