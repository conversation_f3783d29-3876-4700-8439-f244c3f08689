"""
Business Logic
=============

Core business rules and validation logic for the General Notes Drawing Generator.

This module encapsulates the business rules and domain logic including:
- File validation rules
- Note detail validation
- Entity catalog validation
- Workflow validation
- Business constraints and limits

Author: Drawing Production System
"""

import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..models.configuration import GeneralNotesConfig
from ..models.note_detail import NoteDetailCollection
from ..parsers.dxf_parser import EntityCatalog
from .constants import (
    MAX_ENTITIES_PER_NOTE,
    MIN_BOUNDARY_SIZE,
    MAX_BOUNDARY_SIZE,
    SUPPORTED_FILE_FORMATS
)

logger = logging.getLogger(__name__)


class BusinessLogicError(Exception):
    """Raised when business logic validation fails."""
    pass


class BusinessLogic:
    """
    Encapsulates business rules and domain logic for the application.
    
    Provides validation and business rule enforcement for:
    - File format and accessibility
    - Note detail constraints
    - Entity processing limits
    - Workflow prerequisites
    """
    
    def __init__(self, config: GeneralNotesConfig):
        """
        Initialize business logic with configuration.
        
        Args:
            config: Application configuration
        """
        self.config = config
        
    def validate_excel_file_path(self, file_path: str) -> None:
        """
        Validate Excel file path according to business rules.
        
        Args:
            file_path: Path to Excel file
            
        Raises:
            BusinessLogicError: If validation fails
        """
        path = Path(file_path)
        
        # Check file existence
        if not path.exists():
            raise BusinessLogicError(f"Excel file does not exist: {file_path}")
        
        if not path.is_file():
            raise BusinessLogicError(f"Path is not a file: {file_path}")
        
        # Check file extension
        if path.suffix.lower() not in SUPPORTED_FILE_FORMATS['excel']:
            raise BusinessLogicError(
                f"Unsupported Excel file format: {path.suffix}. "
                f"Supported formats: {SUPPORTED_FILE_FORMATS['excel']}"
            )
        
        # Check file size (business rule: max 50MB for Excel files)
        max_size_mb = 50
        file_size_mb = path.stat().st_size / (1024 * 1024)
        if file_size_mb > max_size_mb:
            raise BusinessLogicError(
                f"Excel file too large: {file_size_mb:.1f}MB. Maximum allowed: {max_size_mb}MB"
            )
        
        logger.debug(f"Excel file path validation passed: {file_path}")
    
    def validate_dxf_file_path(self, file_path: str) -> None:
        """
        Validate DXF file path according to business rules.
        
        Args:
            file_path: Path to DXF file
            
        Raises:
            BusinessLogicError: If validation fails
        """
        path = Path(file_path)
        
        # Check file existence
        if not path.exists():
            raise BusinessLogicError(f"DXF file does not exist: {file_path}")
        
        if not path.is_file():
            raise BusinessLogicError(f"Path is not a file: {file_path}")
        
        # Check file extension
        if path.suffix.lower() not in SUPPORTED_FILE_FORMATS['dxf']:
            raise BusinessLogicError(
                f"Unsupported DXF file format: {path.suffix}. "
                f"Supported formats: {SUPPORTED_FILE_FORMATS['dxf']}"
            )
        
        # Check file size (business rule: max 500MB for DXF files)
        max_size_mb = 500
        file_size_mb = path.stat().st_size / (1024 * 1024)
        if file_size_mb > max_size_mb:
            raise BusinessLogicError(
                f"DXF file too large: {file_size_mb:.1f}MB. Maximum allowed: {max_size_mb}MB"
            )
        
        logger.debug(f"DXF file path validation passed: {file_path}")
    
    def validate_output_path(self, output_path: str) -> None:
        """
        Validate output file path according to business rules.
        
        Args:
            output_path: Path for output file
            
        Raises:
            BusinessLogicError: If validation fails
        """
        path = Path(output_path)
        
        # Check parent directory exists
        if not path.parent.exists():
            raise BusinessLogicError(f"Output directory does not exist: {path.parent}")
        
        # Check file extension
        if path.suffix.lower() not in SUPPORTED_FILE_FORMATS['output']:
            raise BusinessLogicError(
                f"Unsupported output file format: {path.suffix}. "
                f"Supported formats: {SUPPORTED_FILE_FORMATS['output']}"
            )
        
        # Check if file already exists and warn
        if path.exists():
            logger.warning(f"Output file already exists and will be overwritten: {output_path}")
        
        # Check write permissions
        try:
            # Try to create/touch the file to check write permissions
            path.touch(exist_ok=True)
        except PermissionError:
            raise BusinessLogicError(f"No write permission for output path: {output_path}")
        except Exception as e:
            raise BusinessLogicError(f"Cannot write to output path: {output_path}. Error: {str(e)}")
        
        logger.debug(f"Output path validation passed: {output_path}")
    
    def validate_note_collection(self, note_collection: NoteDetailCollection) -> None:
        """
        Validate note collection according to business rules.
        
        Args:
            note_collection: Collection of note details
            
        Raises:
            BusinessLogicError: If validation fails
        """
        if not note_collection or len(note_collection) == 0:
            raise BusinessLogicError("Note collection is empty")
        
        # Business rule: Maximum number of notes per collection
        max_notes = 1000
        if len(note_collection) > max_notes:
            raise BusinessLogicError(
                f"Too many notes in collection: {len(note_collection)}. Maximum allowed: {max_notes}"
            )
        
        # Validate individual notes
        validation_errors = []
        for note in note_collection:
            try:
                self._validate_individual_note(note)
            except BusinessLogicError as e:
                validation_errors.append(f"Note '{note.title}': {str(e)}")
        
        if validation_errors:
            raise BusinessLogicError(f"Note validation errors: {'; '.join(validation_errors)}")
        
        # Check for duplicate titles (business rule: all titles must be unique)
        titles = [note.title for note in note_collection]
        duplicate_titles = [title for title in set(titles) if titles.count(title) > 1]
        if duplicate_titles:
            raise BusinessLogicError(f"Duplicate note titles found: {duplicate_titles}")
        
        logger.debug(f"Note collection validation passed: {len(note_collection)} notes")
    
    def _validate_individual_note(self, note) -> None:
        """
        Validate an individual note detail.
        
        Args:
            note: NoteDetail to validate
            
        Raises:
            BusinessLogicError: If validation fails
        """
        # Validate title
        if not note.title or len(note.title.strip()) == 0:
            raise BusinessLogicError("Note title cannot be empty")
        
        # Business rule: Title length limits
        if len(note.title) > 200:
            raise BusinessLogicError(f"Note title too long: {len(note.title)} characters. Maximum: 200")
        
        # Validate boundary dimensions
        if note.boundary.width < MIN_BOUNDARY_SIZE or note.boundary.height < MIN_BOUNDARY_SIZE:
            raise BusinessLogicError(
                f"Boundary too small: {note.boundary.width}×{note.boundary.height}. "
                f"Minimum size: {MIN_BOUNDARY_SIZE}"
            )
        
        if note.boundary.width > MAX_BOUNDARY_SIZE or note.boundary.height > MAX_BOUNDARY_SIZE:
            raise BusinessLogicError(
                f"Boundary too large: {note.boundary.width}×{note.boundary.height}. "
                f"Maximum size: {MAX_BOUNDARY_SIZE}"
            )
        
        # Business rule: Reasonable aspect ratios
        aspect_ratio = note.boundary.width / note.boundary.height
        if aspect_ratio > 10 or aspect_ratio < 0.1:
            logger.warning(f"Note '{note.title}' has extreme aspect ratio: {aspect_ratio:.2f}")
    
    def validate_entity_catalog(self, entity_catalog: EntityCatalog) -> None:
        """
        Validate entity catalog according to business rules.
        
        Args:
            entity_catalog: Entity catalog to validate
            
        Raises:
            BusinessLogicError: If validation fails
        """
        if not entity_catalog:
            raise BusinessLogicError("Entity catalog is empty")
        
        catalog_summary = entity_catalog.get_catalog_summary()
        
        # Business rule: Check for notes with no entities
        empty_notes = []
        for note_title in entity_catalog.get_all_note_titles():
            entity_count = entity_catalog.get_entity_count(note_title)
            if entity_count == 0:
                empty_notes.append(note_title)
            elif entity_count > MAX_ENTITIES_PER_NOTE:
                raise BusinessLogicError(
                    f"Note '{note_title}' has too many entities: {entity_count}. "
                    f"Maximum allowed: {MAX_ENTITIES_PER_NOTE}"
                )
        
        if empty_notes:
            logger.warning(f"Notes with no entities found: {empty_notes}")
        
        # Business rule: Total entity limit
        max_total_entities = 100000
        if catalog_summary['total_entities'] > max_total_entities:
            raise BusinessLogicError(
                f"Too many total entities: {catalog_summary['total_entities']}. "
                f"Maximum allowed: {max_total_entities}"
            )
        
        logger.debug(f"Entity catalog validation passed: {catalog_summary['note_count']} notes, "
                    f"{catalog_summary['total_entities']} entities")
    
    def validate_selection(self, selected_titles: List[str], available_titles: List[str]) -> None:
        """
        Validate note selection according to business rules.
        
        Args:
            selected_titles: List of selected note titles
            available_titles: List of available note titles
            
        Raises:
            BusinessLogicError: If validation fails
        """
        if not selected_titles:
            raise BusinessLogicError("No notes selected for generation")
        
        # Business rule: Maximum number of selected notes
        max_selected = 100
        if len(selected_titles) > max_selected:
            raise BusinessLogicError(
                f"Too many notes selected: {len(selected_titles)}. Maximum allowed: {max_selected}"
            )
        
        # Check that all selected titles are available
        invalid_titles = [title for title in selected_titles if title not in available_titles]
        if invalid_titles:
            raise BusinessLogicError(f"Invalid note titles selected: {invalid_titles}")
        
        logger.debug(f"Selection validation passed: {len(selected_titles)} notes selected")
    
    def validate_layout_constraints(self, selected_notes: List, layout_config) -> None:
        """
        Validate layout constraints for selected notes.
        
        Args:
            selected_notes: List of selected note details
            layout_config: Layout configuration
            
        Raises:
            BusinessLogicError: If validation fails
        """
        if not selected_notes:
            return
        
        # Calculate total area of selected notes
        total_area = sum(note.boundary.area for note in selected_notes)
        
        # Calculate available area in drawing spaces
        drawing_space_area = layout_config.drawing_space_width * layout_config.drawing_space_height
        module_area = layout_config.module_width * layout_config.module_height
        usable_area_per_space = module_area * layout_config.max_modules_per_space
        
        # Business rule: Check if notes will fit in reasonable number of drawing spaces
        max_drawing_spaces = 50
        max_total_area = usable_area_per_space * max_drawing_spaces
        
        if total_area > max_total_area:
            raise BusinessLogicError(
                f"Selected notes require too much area: {total_area:.0f} units². "
                f"Maximum available: {max_total_area:.0f} units² ({max_drawing_spaces} drawing spaces)"
            )
        
        # Estimate number of drawing spaces needed
        estimated_spaces = max(1, int(total_area / usable_area_per_space) + 1)
        if estimated_spaces > 10:
            logger.warning(f"Selected notes will require approximately {estimated_spaces} drawing spaces")
        
        logger.debug(f"Layout constraints validation passed: {len(selected_notes)} notes, "
                    f"estimated {estimated_spaces} drawing spaces")
    
    def get_business_rules_summary(self) -> Dict[str, Any]:
        """
        Get summary of all business rules and constraints.
        
        Returns:
            Dictionary with business rules information
        """
        return {
            'file_constraints': {
                'max_excel_size_mb': 50,
                'max_dxf_size_mb': 500,
                'supported_excel_formats': SUPPORTED_FILE_FORMATS['excel'],
                'supported_dxf_formats': SUPPORTED_FILE_FORMATS['dxf']
            },
            'note_constraints': {
                'max_notes_per_collection': 1000,
                'max_title_length': 200,
                'min_boundary_size': MIN_BOUNDARY_SIZE,
                'max_boundary_size': MAX_BOUNDARY_SIZE,
                'max_entities_per_note': MAX_ENTITIES_PER_NOTE
            },
            'selection_constraints': {
                'max_selected_notes': 100,
                'max_drawing_spaces': 50
            },
            'system_constraints': {
                'max_total_entities': 100000,
                'max_memory_usage_mb': 512
            }
        }
