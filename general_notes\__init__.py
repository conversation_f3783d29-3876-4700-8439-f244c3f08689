"""
General Notes Drawing Generator Package
======================================

A professional package for generating technical drawings by selecting and copying 
predefined note details from a template DXF database.

This package provides modular components for:
- Reading note metadata from Excel summary files
- Loading and parsing DXF database files
- Selecting entities within defined boundaries
- Intelligent layout arrangement in drawing spaces
- Professional DXF output generation

Features:
    - Excel-based note detail metadata management
    - Boundary-based entity selection from DXF databases
    - Intelligent layout optimization with drawing space organization
    - Professional DXF output with entity property preservation
    - User-friendly GUI interface with searchable checklist
    - Integration with auth and drawing_spaces packages

Author: Drawing Production System
Version: 1.0.0
"""

# Core application components
from .main import GeneralNotesGenerator, main

# Core models
from .models.note_detail import NoteDetail
from .models.configuration import GeneralNotesConfig
from .models.boundary import Boundary
from .models.selection import SelectionState

# Core orchestrators
from .orchestrators.workflow_orchestrator import WorkflowOrchestrator

# Package metadata
__version__ = "1.0.0"
__author__ = "Drawing Production System"

# Export main public API
__all__ = [
    # Main application
    'GeneralNotesGenerator',
    'main',
    
    # Core models
    'NoteDetail',
    'GeneralNotesConfig', 
    'Boundary',
    'SelectionState',
    
    # Core orchestrator
    'WorkflowOrchestrator'
]
