"""
Vertical Rebar Drawing Module
============================

Specialized module for drawing reinforcement bar layers with proper positioning,
alignment, and AIA-standard layer management. Handles both standard and aligned
rebar positioning at real scale.
"""

import logging
from typing import TYPE_CHECKING, List, Tuple

if TYPE_CHECKING:
    from ezdxf.layouts import Modelspace
    from ...models.drawing_config import DrawingConfig
    from ...io.dxf_writer import DXFWriter
    from ...calculators.rebar_calculator import RebarCalculator

logger = logging.getLogger(__name__)


class VerticalRebarDrawer:
    """
    Specialized drawer for vertical reinforcement bar layers.

    This class handles drawing reinforcement bars with:
    - Real-scale diameter representation (from CSV data)
    - Proper AIA layer management (AIS291__)
    - Standard and aligned positioning algorithms
    - Perimeter-based layout calculations

    Follows single responsibility principle by focusing solely on rebar layer drawing.
    """

    def __init__(
        self,
        msp: 'Modelspace',
        dxf_writer: 'DXFWriter',
        config: 'DrawingConfig',
        rebar_calc: 'RebarCalculator'
    ):
        """
        Initialize the vertical rebar drawer.

        Args:
            msp: DXF modelspace for drawing operations
            dxf_writer: DXF writer with layer management capabilities
            config: Drawing configuration for styling parameters
            rebar_calc: Rebar calculator for position calculations
        """
        self.msp = msp
        self.dxf_writer = dxf_writer
        self.config = config
        self.rebar_calc = rebar_calc

        logger.debug("Initialized VerticalRebarDrawer")

    def draw_rebar_layer(
        self,
        x: float,
        y: float,
        width: float,
        height: float,
        num_x: int,
        num_y: int,
        diameter: float,
        color: int
    ) -> List[Tuple[float, float]]:
        """
        Draw a layer of reinforcement bars around the perimeter at real scale.

        Creates rebar circles positioned around the perimeter of the specified
        rectangle using actual diameter from CSV data for real-scale representation.
        Rebar circles are drawn with solid hatching to represent cross-sections
        of reinforcement bars following BS8666 standards.

        Args:
            x: Rectangle bottom-left corner X coordinate (mm)
            y: Rectangle bottom-left corner Y coordinate (mm)
            width: Rectangle width dimension (mm)
            height: Rectangle height dimension (mm)
            num_x: Number of bars in X direction
            num_y: Number of bars in Y direction
            diameter: Rebar diameter in mm (actual size from CSV)
            color: Drawing color (AutoCAD color index)

        Returns:
            List[Tuple[float, float]]: Positions of drawn rebars

        Raises:
            Exception: If drawing operation fails
        """
        try:
            # Calculate rebar positions using perimeter algorithm
            positions = self.rebar_calc.calculate_perimeter_positions(
                x, y, width, height, num_x, num_y
            )

            # Get appropriate layer for rebar
            rebar_layer = self._get_rebar_layer()

            # Draw solid-filled rebar circles using actual diameter from CSV data (real scale)
            radius = diameter / 2

            logger.debug(
                f"Drawing solid-filled rebar layer: diameter={diameter}mm, radius={radius}mm (real scale), "
                f"layout={num_x}x{num_y}, positions={len(positions)}"
            )

            for bar_x, bar_y in positions:
                self._draw_solid_rebar_circle(
                    bar_x, bar_y, radius, rebar_layer, color)

            logger.debug(
                f"Drew {len(positions)} solid-filled rebars for {num_x}x{num_y} layout on layer {rebar_layer}")
            return positions

        except Exception as e:
            logger.error(f"Error drawing rebar layer: {e}")
            raise

    def draw_rebar_layer_aligned(
        self,
        x: float,
        y: float,
        width: float,
        height: float,
        num_x: int,
        num_y: int,
        diameter: float,
        color: int,
        reference_positions: List[Tuple[float, float]]
    ) -> List[Tuple[float, float]]:
        """
        Draw a layer of reinforcement bars with alignment to reference positions at real scale.

        Creates rebar circles positioned using alignment algorithm to match reference
        positions from another layer, ensuring proper structural connectivity.
        Rebar circles are drawn with solid hatching to represent cross-sections
        of reinforcement bars following BS8666 standards.

        Args:
            x: Rectangle bottom-left corner X coordinate (mm)
            y: Rectangle bottom-left corner Y coordinate (mm)
            width: Rectangle width dimension (mm)
            height: Rectangle height dimension (mm)
            num_x: Number of bars in X direction
            num_y: Number of bars in Y direction
            diameter: Rebar diameter in mm (actual size from CSV)
            color: Drawing color (AutoCAD color index)
            reference_positions: Reference positions for alignment

        Returns:
            List[Tuple[float, float]]: Positions of drawn aligned rebars

        Raises:
            Exception: If drawing operation fails
        """
        try:
            # Calculate initial positions using perimeter algorithm
            initial_positions = self.rebar_calc.calculate_perimeter_positions(
                x, y, width, height, num_x, num_y
            )

            # Apply alignment algorithm to match reference positions
            aligned_positions = self.rebar_calc.calculate_aligned_positions(
                initial_positions, reference_positions,
                x, y, width, height, num_x, num_y
            )

            # Get appropriate layer for rebar
            rebar_layer = self._get_rebar_layer()

            # Draw aligned solid-filled rebar circles using actual diameter from CSV data (real scale)
            radius = diameter / 2

            logger.debug(
                f"Drawing aligned solid-filled rebar layer: diameter={diameter}mm, radius={radius}mm (real scale), "
                f"layout={num_x}x{num_y}, aligned_positions={len(aligned_positions)}"
            )

            for bar_x, bar_y in aligned_positions:
                self._draw_solid_rebar_circle(
                    bar_x, bar_y, radius, rebar_layer, color)

            logger.debug(
                f"Drew {len(aligned_positions)} aligned solid-filled rebars on layer {rebar_layer}")
            return aligned_positions

        except Exception as e:
            logger.error(f"Error drawing aligned rebar layer: {e}")
            raise

    def draw_multiple_rebar_layers(
        self,
        layer_specs: List[dict]
    ) -> List[List[Tuple[float, float]]]:
        """
        Draw multiple rebar layers efficiently with consistent layer management.

        Batch drawing operation for multiple rebar layers using individual
        specifications for each layer.

        Args:
            layer_specs: List of dictionaries containing layer specifications.
                        Each dict should contain: x, y, width, height, num_x, num_y,
                        diameter, color, and optionally reference_positions for alignment.

        Returns:
            List[List[Tuple[float, float]]]: List of position lists for each drawn layer

        Raises:
            Exception: If any drawing operation fails
        """
        try:
            all_positions = []
            rebar_layer = self._get_rebar_layer()

            for i, spec in enumerate(layer_specs):
                # Extract required parameters
                x = spec['x']
                y = spec['y']
                width = spec['width']
                height = spec['height']
                num_x = spec['num_x']
                num_y = spec['num_y']
                diameter = spec['diameter']
                color = spec['color']
                reference_positions = spec.get('reference_positions')

                # Calculate positions (aligned or standard)
                if reference_positions:
                    initial_positions = self.rebar_calc.calculate_perimeter_positions(
                        x, y, width, height, num_x, num_y
                    )
                    positions = self.rebar_calc.calculate_aligned_positions(
                        initial_positions, reference_positions,
                        x, y, width, height, num_x, num_y
                    )
                else:
                    positions = self.rebar_calc.calculate_perimeter_positions(
                        x, y, width, height, num_x, num_y
                    )

                # Draw solid-filled rebar circles
                radius = diameter / 2
                for bar_x, bar_y in positions:
                    self._draw_solid_rebar_circle(
                        bar_x, bar_y, radius, rebar_layer, color)

                all_positions.append(positions)

            logger.debug(
                f"Drew {len(layer_specs)} rebar layers on layer {rebar_layer}")
            return all_positions

        except Exception as e:
            logger.error(f"Error drawing multiple rebar layers: {e}")
            raise

    def _get_rebar_layer(self) -> str:
        """
        Get the appropriate layer name for reinforcement bars.

        Uses the DXF writer's layer management system to get the correct
        AIA-standard layer name for reinforcement elements.

        Returns:
            str: Layer name for reinforcement bars (typically "AIS291__")
        """
        try:
            # Use layer management system to get appropriate layer
            if self.dxf_writer:
                rebar_layer = self.dxf_writer.get_layer_for_element(
                    "reinforcement")
                self.dxf_writer.ensure_layer_exists(rebar_layer)
                return rebar_layer
            else:
                # Fallback to standard layer name if no DXF writer available
                logger.warning(
                    "No DXF writer available, using fallback layer name")
                return "AIS291__"

        except Exception as e:
            logger.error(f"Error getting rebar layer: {e}")
            # Return fallback layer name
            return "AIS291__"

    def _draw_solid_rebar_circle(
        self,
        center_x: float,
        center_y: float,
        radius: float,
        layer: str,
        color: int
    ) -> None:
        """
        Draw a solid-filled rebar circle representing a reinforcement bar cross-section.

        Creates a circle with solid hatching to represent rebar cross-sections
        following BS8666 compliance standards for reinforcement representation.
        Uses ezdxf's solid hatch functionality to fill the circle completely.

        Args:
            center_x: Circle center X coordinate (mm)
            center_y: Circle center Y coordinate (mm)
            radius: Circle radius (mm) - half of rebar diameter
            layer: DXF layer name for the rebar element
            color: AutoCAD color index for the circle and hatch

        Raises:
            Exception: If circle or hatch creation fails
        """
        try:
            # Create the circle outline first
            circle = self.msp.add_circle(
                center=(center_x, center_y),
                radius=radius,
                dxfattribs={
                    'layer': layer,
                    'color': color
                }
            )

            # Create solid hatch to fill the circle
            # Use SOLID pattern for complete fill (BS8666 compliant)
            hatch = self.msp.add_hatch(
                color=color,
                dxfattribs={
                    'layer': layer,
                    'solid_fill': 1,  # Enable solid fill
                    'pattern_name': 'SOLID',  # Solid hatch pattern
                    'pattern_type': 1,  # Predefined pattern
                }
            )

            # Associate the circle as the boundary path for the hatch
            # This creates a solid-filled circle representing the rebar cross-section
            edge_path = hatch.paths.add_edge_path()
            edge_path.add_arc(
                center=(center_x, center_y),
                radius=radius,
                start_angle=0,
                end_angle=360
            )

            logger.debug(
                f"Created solid-filled rebar circle at ({center_x}, {center_y}) with radius {radius}mm on layer {layer}")

        except Exception as e:
            logger.warning(
                f"Failed to create solid hatch for rebar circle, falling back to outline only: {e}")
            # Fallback: ensure at least the circle outline exists if hatch fails
            if 'circle' not in locals():
                self.msp.add_circle(
                    center=(center_x, center_y),
                    radius=radius,
                    dxfattribs={
                        'layer': layer,
                        'color': color
                    }
                )
